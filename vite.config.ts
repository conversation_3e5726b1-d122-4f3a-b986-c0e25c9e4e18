import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import vueJsx from '@vitejs/plugin-vue-jsx'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  // 判断是否开启调试模式
  const isDebug = env.VITE_DEBUG === 'true'

  console.log(`🔧 构建模式: ${mode}`)
  console.log(`🐛 调试模式: ${isDebug ? '开启' : '关闭'}`)

  return {
    plugins: [
      vue(),
      vueJsx({
        // 启用 JSX 和 TSX 支持
        include: [/\.[jt]sx$/]
      })
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '@assets': path.resolve(__dirname, './src/assets'),
        '@components': path.resolve(__dirname, './src/components'),
        '@views': path.resolve(__dirname, './src/views'),
        '@store': path.resolve(__dirname, './src/store'),
        '@api': path.resolve(__dirname, './src/api'),
        '@hooks': path.resolve(__dirname, './src/hooks'),
        '@utils': path.resolve(__dirname, './src/utils'),
        '@types': path.resolve(__dirname, './src/types'),
        '@constants': path.resolve(__dirname, './src/constants'),
        '@styles': path.resolve(__dirname, './src/styles'),
        '@layout': path.resolve(__dirname, './src/layout')
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          // 全局引入scss变量
          additionalData: `@use "@/styles/_variables.scss" as *;`
        }
      }
    },
    // 不同环境的基础路径配置
    base: './',
    // 开发服务器配置
    server: {
      host: '0.0.0.0',
      port: 5173,
      open: true,
      proxy: {
        '/api': {
          // target: 'http://**************:9400/',
          // target: 'http://***************:39400/',
          target: 'http://**************:8080/',
          changeOrigin: true,
          rewrite: path => path.replace(/^\/api/, '')
        }
      }
    },
    // 根据调试模式配置日志级别
    logLevel: isDebug ? 'info' : 'warn',

    // 构建配置 - 根据调试模式动态配置
    build: {
      // 调试模式下生成详细的 sourcemap，否则不生成
      sourcemap: isDebug,

      // 调试模式下禁用代码压缩，便于调试
      minify: isDebug ? false : 'terser',

      // 简化的 Terser 配置
      terserOptions: isDebug
        ? undefined
        : {
            compress: {
              drop_console: !isDebug,
              drop_debugger: !isDebug
            }
          },

      // 根据调试模式配置输出文件名
      rollupOptions: {
        output: {
          // 调试模式下使用简单文件名，便于调试
          entryFileNames: isDebug ? 'assets/[name].js' : 'assets/[name]-[hash].js',
          chunkFileNames: isDebug ? 'assets/[name].js' : 'assets/[name]-[hash].js',
          assetFileNames: isDebug ? 'assets/[name].[ext]' : 'assets/[name]-[hash].[ext]'
        }
      }
    }
  }
})
