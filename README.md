# VOC 标准化UI组件库

基于 Vue 3 + TypeScript + Element Plus 构建的现代化VOC数据分析管理系统UI组件库，专为客户之声数据分析而设计。

## 🚀 技术栈

### 核心技术

- **前端框架**: Vue 3.5.13 (Composition API)
- **开发语言**: TypeScript 5.8.3
- **构建工具**: Vite 6.3.5
- **UI 组件库**: Element Plus 2.10.2
- **路由管理**: Vue Router 4.5.1
- **状态管理**: Pinia 3.0.2

### 开发工具链

- **样式预处理**: Sass 1.89.0
- **图标库**: Remix Icon 4.6.0
- **HTTP 客户端**: Axios 1.9.0
- **日期处理**: Day.js 1.11.13
- **代码规范**: ESLint 9.22.0 + Prettier 3.5.3
- **包管理器**: pnpm (强制使用)

### 功能库

- **图表库**: ECharts 5.6.0 + ECharts WordCloud 2.1.0
- **工具库**: Lodash-es 4.17.21
- **Cookie管理**: js-cookie 3.0.5
- **PDF查看**: vue-pdf 4.3.0

## 📁 项目结构

```
voc-std-ui/
├── .cursor/                  # Cursor AI 配置和规则
│   └── rules/               # 项目开发规范文档
├── docs/                    # 项目文档
│   ├── framework/           # 框架相关文档
│   ├── promote/             # 功能推广文档
│   └── template/            # 模板文件
├── public/                  # 静态资源
├── src/                     # 源代码目录
│   ├── api/                # API 接口封装
│   ├── assets/             # 静态资源
│   ├── components/         # 组件库
│   │   ├── OriginalDetails/ # 原文明细组件 ⭐
│   │   ├── ButtonGroup/     # 按钮组组件
│   │   ├── Charts/          # 图表组件集合
│   │   ├── PopulationCharacteristics/ # 人群特征分析
│   │   ├── RegionAnalysis/  # 地域分析
│   │   ├── IndexAnalysis/   # 指标分析
│   │   ├── DataSourceAnalysis/ # 数据源分析
│   │   ├── VocTrendChart/   # VOC趋势图表
│   │   ├── TopQuestion/     # TOP问题排行榜
│   │   └── ShowCompare/     # 数据环比显示组件
│   ├── layout/             # 布局组件
│   ├── router/             # 路由配置
│   ├── store/              # Pinia 状态管理
│   ├── styles/             # 全局样式
│   ├── types/              # TypeScript 类型定义
│   ├── utils/              # 工具函数
│   ├── views/              # 页面组件
│   ├── App.vue             # 根组件
│   ├── main.ts             # 应用入口
│   └── vite-env.d.ts       # Vite 环境类型声明
├── package.json            # 项目依赖和脚本
├── vite.config.ts          # Vite 构建配置
└── README.md               # 项目说明
```

## 🛠️ 快速开始

### 系统要求

- **Node.js**: >= 18.0.0
- **包管理器**: pnpm >= 8.0.0 (强制使用)
- **操作系统**: Windows、macOS、Linux

### 安装使用

```bash
# 1. 克隆项目
git clone <project-url>
cd voc-std-ui

# 2. 安装依赖
pnpm install

# 3. 启动开发服务器
pnpm dev
# 访问 http://localhost:5173

# 4. 构建生产版本
pnpm build

# 5. 预览构建结果
pnpm preview
```

### 常用命令

```bash
# 代码质量
pnpm lint                 # 代码检查
pnpm lint:fix             # 自动修复代码问题
pnpm format               # 格式化代码
pnpm type-check           # TypeScript 类型检查

# 依赖管理
pnpm outdated             # 检查过期依赖
pnpm audit                # 安全漏洞检查
```

## 📦 核心组件

### 数据展示组件

#### OriginalDetails - 原文明细组件 ⭐ 最新

专为VOC数据详细信息展示设计的高性能组件。

**核心特性：**

- 🔍 支持多种数据源（帖子评论、工单、意见反馈、咨询服务、问卷调研）
- 📊 智能情感标签（正面=绿色、中性=蓝色、负面=红色）
- 👥 用户详情查看功能
- 📄 原文详情弹窗展示
- 📱 完整响应式设计
- ⚡ 高性能分页和加载状态

**使用示例：**

```vue
<OriginalDetails
  :data="originalData"
  :total="totalCount"
  :loading="loading"
  title="原文明细"
  @page-change="handlePageChange"
  @user-detail="handleUserDetail"
/>
```

#### PopulationCharacteristics - 人群特征分析 (v2.1.0)

- ✅ **Vue 3 重构**: 完全使用 Composition API + `<script setup>` 语法
- ✅ **移除依赖**: 不再依赖 chart-box 容器组件，独立运行
- ✅ **TypeScript 支持**: 完整的类型定义和类型检查
- ✅ **响应式设计**: 支持移动端适配和自定义样式
- ✅ **加载状态**: 内置加载骨架屏和无数据状态
- ✅ **下载功能**: 支持数据导出和事件回调

#### ShowCompare - 数据环比显示组件 (v2.0.0)

- ✅ **多指标支持**: 支持提及数、体验值、负面提及率三种指标类型
- ✅ **智能颜色**: 根据数值正负自动显示不同颜色
- ✅ **方向指示**: 使用箭头图标直观显示变化方向
- ✅ **格式化显示**: 自动格式化为百分比并添加千分位分隔符
- ✅ **TypeScript 支持**: 完整的类型定义和类型检查

### 图表组件

#### BarAndPointChart - 柱状图加散点图 (v2.0.0)

- ✅ **简化 Props**: 只需要传入 `data`、`width`、`height`、`needDetails` 四个核心属性
- ✅ **响应式设计**: 支持固定尺寸和百分比宽度
- ✅ **交互功能**: 可选的点击详情和钻取功能
- ✅ **样式一致**: 保持与原有设计风格一致
- ✅ **性能优化**: 自动事件清理和实例管理

#### BarOrLineChart - 柱状图和折线图组合

- 支持柱状图堆叠显示（正面、中性、负面提及量）
- 支持折线图显示（体验值）
- 支持双Y轴配置
- 支持自定义尺寸和响应式布局
- 支持点击事件和数据详情查看
- 完整的数据格式化（万、千万、亿等单位自动转换）

### 其他业务组件

- **ButtonGroup** - 按钮组组件，支持单选模式
- **VocTrendChart** - VOC趋势图表，基于ECharts
- **TopQuestion** - TOP问题排行榜
- **RegionAnalysis** - 地域分析
- **IndexAnalysis** - 指标分析
- **DataSourceAnalysis** - 数据源分析

## 📄 页面结构

- **首页** (`/home`): 技术栈展示和项目介绍
- **登录页** (`/login`): 用户登录界面（用户名: admin，密码: 123456）
- **系统管理**:
  - **用户管理** (`/system/user`): 用户列表和操作
  - **角色管理** (`/system/role`): 角色列表和操作
- **数据管理**:
  - **数据分析** (`/data/analysis`): 数据统计和趋势图表
  - **图表演示** (`/data/analysis/chart-demo`): 组件演示
  - **BarOrLineChart测试** (`/data/analysis/bar-or-line-chart-test`): 组件测试
  - **数据报表** (`/data/report`): 数据报表生成
- **404页面** (`/404`): 页面未找到错误页

## 📋 菜单系统

项目采用动态路由生成菜单，具有以下特性：

- **🔄 动态生成**: 根据路由配置自动生成菜单，无需硬编码
- **🔐 权限控制**: 支持基于权限的菜单过滤
- **📱 响应式**: 支持菜单折叠/展开，适配不同屏幕尺寸
- **🎯 类型安全**: 完整的 TypeScript 类型支持

**使用方法**：

1. **添加新菜单**: 只需在路由配置中添加新路由，菜单会自动生成
2. **控制显示**: 通过路由 `meta.hidden` 属性控制菜单项显示/隐藏
3. **权限控制**: 通过路由 `meta.permission` 属性设置权限标识

详细使用说明请参考：[菜单系统使用指南](docs/menu-system.md)

## ⚙️ 配置说明

### 环境变量

```bash
# .env.development - 开发环境
VITE_API_BASE_URL=/api
VITE_DEBUG=true

# .env.production - 生产环境
VITE_API_BASE_URL=/api
VITE_DEBUG=false
VITE_APP_VERSION=1.0.0
```

### 路径别名配置

```typescript
// 可用的导入别名
import { formatDate } from '@/utils'
import UserCard from '@/components/UserCard'
import { useAppStore } from '@/store'
import type { BaseResponse } from '@/types'
```

### API 代理配置

```typescript
// vite.config.ts
server: {
  proxy: {
    '/api': {
      target: 'http://***************:39400/',
      changeOrigin: true,
      rewrite: path => path.replace(/^\/api/, '')
    }
  }
}
```

## 📈 项目状态

- **框架基础**: 95% ✅
- **核心功能**: 60% ✅
- **业务组件**: 70% ✅
- **开发工具**: 90% ✅
- **生产可用度**: 80% ✅

**项目数据**:

- 📦 组件数量：15+ 个核心组件
- 📝 代码行数：~3000+ 行
- 📱 移动端适配：✅ 完成
- 🌐 浏览器支持：Chrome、Firefox、Safari、Edge

## 📝 开发规范

### 代码规范

- **组件**: 使用 Vue 3 Composition API + `<script setup>`
- **类型**: 充分利用 TypeScript 类型系统
- **样式**: SCSS 预处理 + Element Plus 栅格系统
- **命名**: 遵循 Vue 风格指南和项目约定

### 文件命名约定

| 文件类型   | 命名规范   | 示例                    |
| ---------- | ---------- | ----------------------- |
| Vue 组件   | PascalCase | `UserProfile.vue`       |
| TypeScript | camelCase  | `formatDate.ts`         |
| 样式文件   | kebab-case | `global-variables.scss` |
| 目录名称   | PascalCase | `UserCard/`             |

### Git 提交规范

```bash
feat(页面): 添加用户管理页面
fix(组件): 修复登录表单验证问题
docs(文档): 更新开发指南
style(样式): 调整按钮样式
refactor(重构): 优化API请求逻辑
```

## 📖 组件文档

每个组件都包含详细的README文档和使用示例：

- [OriginalDetails 原文明细组件](src/components/OriginalDetails/README.md)
- [ButtonGroup 按钮组组件](docs/promote/按钮组组件.md)
- [PopulationCharacteristics 人群特征分析](docs/promote/人群特征分析.md)
- [RegionAnalysis 地域分析](docs/promote/地域分析.md)
- [更多组件文档](docs/framework/)

## 📝 更新日志

### v2.1.0 (2024-12-19)

- 🎉 **OriginalDetails 组件**: 全新的VOC原文数据展示组件

  - 完整的数据源类型支持
  - 智能情感标签和用户详情功能
  - 响应式设计和高性能分页

- 🎉 **PopulationCharacteristics 组件重构**: 移除 chart-box 依赖，升级为 Vue 3 框架

  - 完全移除对 chart-box 容器组件的依赖
  - 重构为 Vue 3 Composition API + `<script setup>` 语法
  - 添加完整的 TypeScript 类型定义和类型检查

- 🔧 **菜单系统优化**: 从硬编码升级为动态路由生成
  - 菜单数据从路由配置中自动生成，无需手动维护
  - 支持多级菜单结构，自动过滤有效路由

### v2.0.0 (2024-12-19)

- 🎉 **BarAndPointChart 组件重构**: 简化 Props 接口，提升易用性
- 🎯 **ShowCompare 组件重构**: 升级为 Vue 3 Composition API + TypeScript

### v1.0.0 (2024-12-18)

- 🚀 **项目初始化**: Vue 3 + TypeScript + Vite 基础框架
- 📊 **图表组件**: BarAndPointChart、BarOrLineChart 基础实现
- 🎨 **UI 系统**: Element Plus + SCSS 样式系统

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支：`git checkout -b feature/AmazingFeature`
3. 提交更改：`git commit -m 'feat(组件): Add some AmazingFeature'`
4. 推送分支：`git push origin feature/AmazingFeature`
5. 提交 Pull Request

## 📄 详细文档

- [项目完整开发指南](.cursor/rules/project-guide.mdc)
- [组件开发规范](docs/framework/)
- [样式设计规范](docs/promote/)
- [菜单系统使用指南](docs/menu-system.md)

## 📜 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

**⚡ 特色亮点：**

- 🆕 **专业的VOC数据分析组件** - 完整的客户反馈数据展示解决方案
- 🎨 **现代化UI设计** - 基于Element Plus的美观界面
- 📱 **完整响应式** - 优秀的移动端适配体验
- 🚀 **高性能渲染** - 基于Vue 3的快速响应
- 🛠️ **TypeScript支持** - 完整的类型安全保障
- 📚 **详细文档** - 每个组件都有完整的使用指南
