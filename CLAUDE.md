# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 在此代码仓库中工作时提供指导。

## 项目概述

VOC标准化UI (voc-std-ui) 是一个基于 Vue 3 + TypeScript + Element Plus 构建的现代化 VOC（客户之声）数据分析管理系统UI组件库。该项目专门用于客户反馈数据展示和分析组件。

## 开发命令

### 核心开发命令

```bash
# 开发环境
pnpm dev                    # 启动开发服务器 (http://localhost:5173)
pnpm build                  # 生产环境构建 (包含类型检查)
pnpm preview               # 预览生产构建

# 代码质量
pnpm lint                  # 运行 ESLint 代码检查
pnpm lint:fix              # 自动修复 ESLint 问题
pnpm format                # 使用 Prettier 格式化代码

# 类型检查
vue-tsc -b                 # TypeScript 编译和类型检查 (包含在构建中)
```

### 附加命令

```bash
# 代码行数统计工具
./count_lines.sh           # 统计项目代码行数

# 包管理
pnpm install               # 安装依赖
pnpm outdated              # 检查过期包
pnpm audit                 # 安全审计
```

## 项目架构

### 技术栈

- **前端框架**: Vue 3.5.13 (Composition API + `<script setup>`)
- **开发语言**: TypeScript 5.8.3 (严格模式)
- **构建工具**: Vite 6.3.5
- **UI框架**: Element Plus 2.10.2
- **状态管理**: Pinia 3.0.2
- **路由管理**: Vue Router 4.5.1
- **图表库**: ECharts 5.6.0 + ECharts WordCloud 2.1.0
- **包管理器**: pnpm (必需)

### 目录结构

```
src/
├── api/                    # API 接口和 HTTP 客户端
├── assets/                 # 静态资源和图片
├── components/             # 可复用 UI 组件
│   ├── OriginalDetails/    # VOC原文明细组件 (核心组件)
│   ├── PopulationCharacteristics/ # 人群特征分析
│   ├── Charts/             # 图表组件 (柱状图、折线图、词云图)
│   ├── ShowCompare/        # 数据环比显示组件
│   └── [其他业务组件]
├── constants/              # 应用常量
├── layout/                 # 布局组件 (Header, Menu, Sidebar)
├── router/                 # Vue Router 配置
├── store/                  # Pinia 状态管理
├── styles/                 # 全局 SCSS 样式
├── types/                  # TypeScript 类型定义
├── utils/                  # 工具函数
└── views/                  # 页面组件
```

### 关键架构模式

1. **动态菜单系统**: 菜单从路由配置自动生成，避免硬编码菜单数据
2. **组件优先方法**: 业务逻辑封装在可复用组件中
3. **类型安全**: 启用严格模式的完整 TypeScript 覆盖
4. **路径别名**: 完善的导入别名系统 (`@/`, `@components/`, `@api/` 等)
5. **基于环境的配置**: 基于 `VITE_DEBUG` 环境变量的调试模式和构建优化

## 组件开发指南

### 核心 VOC 组件

项目包含专业的 VOC（客户之声）分析组件：

1. **OriginalDetails** (`src/components/OriginalDetails/`): VOC 数据详情展示的主要组件
   - 支持多种数据源（帖子、工单、反馈、调研）
   - 智能情感标签和用户详情
   - 高性能分页

2. **PopulationCharacteristics** (`src/components/PopulationCharacteristics/`): 人群特征分析图表
   - 使用 Composition API 重构的 Vue 3 版本
   - 独立于 chart-box 容器
   - 完整的 TypeScript 支持

3. **Charts** (`src/components/Charts/`): 图表组件集合
   - BarAndPointChart: 简化的 props 接口
   - BarOrLineChart: 组合柱状图和折线图
   - WordCloudChart: 基于 ECharts 的词云图

### 组件标准

- 使用 Vue 3 Composition API 和 `<script setup>` 语法
- 在 `types.d.ts` 文件中实现完整的 TypeScript 类型定义
- 为复杂组件包含 README.md
- 遵循 Element Plus 设计系统
- 实现响应式设计模式

## 构建和环境配置

### 环境变量

```bash
# 开发环境 (.env.development)
VITE_API_BASE_URL=/api
VITE_DEBUG=true

# 生产环境 (.env.production)
VITE_API_BASE_URL=/api
VITE_DEBUG=false
VITE_APP_VERSION=1.0.0
```

### 调试模式功能

当 `VITE_DEBUG=true` 时：
- 生成源代码映射
- 禁用代码压缩
- 保留控制台日志
- 简化文件命名以便调试

### API 代理配置

开发服务器将 `/api` 请求代理到 `http://**************:8080/`（在 vite.config.ts 中配置）

## 代码风格和标准

### ESLint 配置

- 使用现代 flat 配置格式
- TypeScript ESLint 集成
- Vue 3 代码检查规则
- JSX/TSX 支持
- Prettier 集成用于代码格式化

### 重要规则

- 禁用多词组件名称要求 (`vue/multi-word-component-names: off`)
- 灵活的 TypeScript 规则以提高开发效率
- 自动导入全局变量支持

### 文件命名约定

| 文件类型 | 命名约定 | 示例 |
|-----------|------------|---------|
| Vue 组件 | PascalCase | `UserProfile.vue` |
| TypeScript 文件 | camelCase | `formatDate.ts` |
| 样式文件 | kebab-case | `global-variables.scss` |
| 目录 | PascalCase | `UserCard/` |

## 测试和质量保证

### 当前状态
- 目前未配置测试框架
- 通过 TypeScript 严格模式和 ESLint 确保代码质量
- 通过开发服务器进行手动测试

### 测试设置建议
- 考虑添加 Vitest 进行单元测试
- 使用 Vue Testing Utils 实现组件测试
- 使用 Cypress 或 Playwright 添加 E2E 测试

## 部署和生产

### 构建流程
1. TypeScript 编译和类型检查 (`vue-tsc -b`)
2. Vite 构建过程和环境特定优化
3. 资源优化和打包

### 生产环境注意事项
- 基础路径配置为 `./` 以便灵活部署
- 生产环境禁用源码映射（除非调试模式）
- 生产构建中移除控制台日志
- 资源文件包含哈希值用于缓存失效

## 开发流程

### 添加新功能
1. 从 `dev_voc_v1` 创建功能分支
2. 按照既定模式实现组件
3. 添加适当的 TypeScript 类型
4. 如果添加新页面则更新路由配置
5. 提交前运行代码检查和类型检查

### 菜单系统使用
- 菜单从路由配置自动生成
- 使用 `meta.hidden` 属性控制可见性
- 使用 Element Plus 图标名称在 `meta.icon` 中添加图标
- 通过 `meta.permission` 属性实现权限控制

## 常见开发任务

### 添加新页面
1. 在 `src/views/` 中创建组件
2. 在 `src/router/index.ts` 中添加路由
3. 菜单将从路由配置自动生成

### 创建业务组件
1. 在 `src/components/` 中创建组件目录
2. 实现 `index.vue`、`types.d.ts`，可选的 `README.md`
3. 从 `src/components/index.ts` 导出
4. 遵循现有组件模式

### API 集成
1. 在 `src/types/` 中定义类型
2. 在 `src/api/` 中创建 API 函数
3. 在组件中使用并进行适当的错误处理

## 性能优化

### 内置优化
- 所有路由组件的懒加载
- 启用 Tree shaking
- 通过 Vite 进行资源优化
- 组件级代码分割

### 开发建议
- 对重型组件使用 `defineAsyncComponent`
- 为大数据集实现虚拟滚动
- 优化 ECharts 组件渲染
- 使用 Pinia 进行高效状态管理