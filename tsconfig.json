{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["src/**/*", "src/**/*.vue", "src/**/*.tsx", "src/**/*.jsx", "src/**/*.test.ts", "src/**/*.spec.ts", "src/types/index.d.ts", "src/vite-env.d.ts"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": true, "baseUrl": ".", "types": ["vitest/globals"], "paths": {"@/*": ["./src/*"], "@assets/*": ["./src/assets/*"], "@components/*": ["./src/components/*"], "@views/*": ["./src/views/*"], "@store/*": ["./src/store/*"], "@api/*": ["./src/api/*"], "@hooks/*": ["./src/hooks/*"], "@utils/*": ["./src/utils/*"], "@types/*": ["./src/types/*"], "@constants/*": ["./src/constants/*"], "@styles/*": ["./src/styles/*"], "@layout/*": ["./src/layout/*"]}}}