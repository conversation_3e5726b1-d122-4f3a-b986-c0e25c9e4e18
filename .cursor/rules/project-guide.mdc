---
description:
globs:
alwaysApply: true
---
# Vue 3 项目完整开发指南

> 本文档包含项目的所有开发规范、代码标准、Git 规范。

## 📋 目录

- [技术栈详情](mdc:#技术栈详情)
- [开发约束](mdc:#开发约束)
- [代码规范](mdc:#代码规范)
- [目录结构](mdc:#目录结构)
- [样式规范](mdc:#样式规范)
- [Git 规范](mdc:#git-规范)

---

## 技术栈详情

### 核心技术
- **前端框架**: Vue 3.5.13 (Composition API)
- **开发语言**: TypeScript 5.8.3
- **构建工具**: Vite 6.3.5
- **UI 组件库**: Element Plus 2.10.2
- **状态管理**: Pinia 3.0.2
- **路由管理**: Vue Router 4.5.1

### 开发工具
- **样式预处理**: Sass 1.89.0
- **代码规范**: ESLint 9.22.0 + Prettier 3.5.3
- **包管理器**: pnpm (强制使用)

### 功能库
- **HTTP 客户端**: Axios 1.9.0
- **图表库**: ECharts 5.6.0 + ECharts WordCloud 2.1.0
- **工具库**: Lodash-es 4.17.21
- **日期处理**: Day.js 1.11.13
- **图标库**: Remix Icon 4.6.0

---

## 开发约束

### ⚠️ 严格限制
```typescript
// 禁止操作
❌ 自动执行 Git 命令 (git add, git commit, git push)
❌ 删除或修改现有目录结构
❌ 修改 vite.config.ts 中的 alias 配置
❌ 自动调整用户手动修改的样式代码
❌ 使用 UnoCSS (项目使用 SCSS)
❌ 使用 npm 或 yarn (强制使用 pnpm)
```

### ✅ 必须遵循
```typescript
// 开发要求
✅ 始终使用中文回复用户
✅ 直接修改代码，不只给建议
✅ 使用 Vue 3 Composition API + <script setup>
✅ 充分利用 TypeScript 类型检查
✅ 优先使用项目既定组件和工具
```

---

## 代码规范

### Vue 3 组件规范

```vue
<script setup lang="tsx">
// 组件名称定义
defineOptions({
  name: 'UserCard'
})

// Props 接口定义
interface Props {
  user: UserInfo
  showAvatar?: boolean
}

// Props 和默认值
const { user, showAvatar = true } = defineProps<Props>()

// Emits 定义
const emit = defineEmits<{
  edit: [id: number]
  delete: [id: number]
}>()

// 响应式数据
const isLoading = ref(false)
const userForm = reactive({
  name: '',
  email: ''
})

// 计算属性
const displayName = computed(() =>
  user.nickname || user.name || '匿名用户'
)

// 方法
const handleEdit = () => {
  emit('edit', user.id)
}
</script>

<template>
  <div class="user-card">
    <div v-if="showAvatar" class="avatar">
      <img :src="user.avatar" :alt="user.name">
    </div>
    <div class="info">
      <h3>{{ displayName }}</h3>
      <p>{{ user.email }}</p>
    </div>
    <div class="actions">
      <el-button @click="handleEdit">编辑</el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.user-card {
  padding: $spacing-md;
  border: 1px solid $border-color;
  border-radius: $border-radius;

  .avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }
}
</style>
```

### TypeScript 类型规范

```typescript
// 接口定义 - 使用 PascalCase
interface UserInfo {
  id: number
  name: string
  email: string
  avatar?: string
  role: UserRole
}

// 枚举定义
enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  GUEST = 'guest'
}

// 类型别名
type ApiStatus = 'loading' | 'success' | 'error'

// 泛型接口
interface ApiResponse<T> {
  code: number
  message: string
  data: T
}

// 组件 Props 类型
interface ComponentProps {
  title: string
  items: string[]
  onSelect?: (item: string) => void
}
```

### 性能优化规范

```typescript
// 大型对象使用 shallowRef
const chartConfig = shallowRef({
  title: { text: 'Chart' },
  series: [/* 大量数据 */]
})

// ECharts 实例标记为非响应式
const chartInstance = ref(null)
onMounted(() => {
  chartInstance.value = markRaw(echarts.init(element))
})

// 路由懒加载
const UserManagement = () => import('@/views/user-management/index.vue')

// 异步错误处理
const fetchUserList = async () => {
  try {
    loading.value = true
    const response = await userApi.getList()
    userList.value = response.data
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取数据失败，请稍后重试')
  } finally {
    loading.value = false
  }
}
```

---

## 目录结构

### 完整项目结构
```
voc-std-ui/
├── .cursor/rules/         # Cursor 规则文档
├── docs/                  # 项目文档
├── public/               # 静态资源
├── src/                  # 源代码
│   ├── api/             # API 接口封装
│   ├── assets/          # 静态资源
│   ├── components/      # 公共组件 (目录结构)
│   ├── layout/          # 布局组件
│   ├── router/          # 路由配置
│   ├── store/modules/   # Pinia 状态模块
│   ├── styles/          # SCSS 全局样式
│   ├── types/           # TypeScript 类型
│   ├── utils/           # 工具函数
│   ├── views/           # 页面组件
│   ├── App.vue          # 根组件
│   └── main.ts          # 入口文件
├── package.json         # 项目依赖
├── vite.config.ts       # Vite 配置 (🚫 不要修改 alias)
└── README.md            # 项目说明
```

### 组件组织规范
```
# 公共组件 - 使用目录结构
src/components/
├── UserCard/
│   ├── index.vue        # 主组件文件
│   └── types.ts         # 组件类型定义
├── DataTable/
│   ├── index.vue
│   ├── TableHeader.vue  # 子组件
│   └── TableRow.vue
└── index.ts             # 统一导出

# 页面组件
src/views/
├── user-management/
│   ├── index.vue        # 页面主文件
│   ├── components/      # 页面特有组件
│   │   ├── UserForm.vue
│   │   └── UserList.vue
│   └── api.ts           # 页面 API (可选)
```

### 文件命名规范
| 文件类型 | 命名规范 | 示例 |
|---------|---------|------|
| Vue 组件 | PascalCase | `UserProfile.vue` |
| TypeScript | camelCase | `formatDate.ts` |
| 样式文件 | kebab-case | `global-variables.scss` |
| 目录名称 | PascalCase | `UserCard/` |

> 规则：所有 TypeScript 类型定义文件必须使用 `.d.ts` 结尾。

---

## 样式规范

### SCSS 使用规范

```scss
// 全局变量 (自动注入，可直接使用)
.user-card {
  padding: $spacing-md;
  background: $bg-color-secondary;
  border: 1px solid $border-color;
  border-radius: $border-radius;

  .title {
    color: $text-color-primary;
    font-size: $font-size-lg;
    font-weight: 600;
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: $spacing-sm;
  }
}

// Element Plus 组件样式覆盖
:deep(.el-button) {
  border-radius: $border-radius;
}
```

### Element Plus 响应式布局

```vue
<template>
  <!-- 使用 Element Plus 栅格系统 -->
  <el-row :gutter="20">
    <el-col :xs="24" :sm="12" :md="8" :lg="6">
      <div class="grid-content">内容</div>
    </el-col>
  </el-row>

  <!-- 表单响应式 -->
  <el-form :model="form" label-width="100px">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="用户名">
          <el-input v-model="form.username" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
```

---

## Git 规范

### 提交信息规范

```bash
# 提交格式
<type>(<scope>): <subject>

# Type 类型
feat(页面): 添加用户管理页面
fix(组件): 修复登录表单验证问题
docs(文档): 更新 README 文档
style(样式): 调整按钮样式
refactor(重构): 优化API请求逻辑
perf(性能): 优化列表渲染性能
test(测试): 添加组件单元测试
chore(工具): 更新依赖包版本

# 复杂提交示例
feat(用户管理): 实现完整的用户管理功能

- 添加用户列表页面，支持分页和搜索
- 实现用户新增、编辑、删除功能
- 添加用户状态管理和权限控制
- 集成用户头像上传功能
```

### 分支管理

```bash
# 分支命名规范
feature/user-authentication    # 功能分支
fix/login-validation-error    # 修复分支
release/v1.2.0               # 发布分支
hotfix/v1.1.1-urgent-fix     # 热修复分支

# 工作流程
1. 从 develop 创建 feature/* 分支
2. 功能完成后合并回 develop
3. 从 develop 创建 release/* 分支准备发布
4. 合并到 main 分支并打标签
5. 同步 main 到 develop
```

### 代码质量检查

```bash
# 提交前检查清单
✅ pnpm lint        # ESLint 检查通过
✅ pnpm format      # Prettier 格式化
✅ pnpm type-check  # TypeScript 检查通过
✅ 功能测试正常
✅ 无 console.log 调试代码
✅ 无未使用的导入和变量
```




---

## 常用命令

```bash
# 开发命令
pnpm install              # 安装依赖
pnpm dev                  # 启动开发服务器
pnpm build                # 构建生产版本
pnpm preview              # 预览构建结果

# 代码质量
pnpm lint                 # 代码检查
pnpm lint:fix             # 自动修复代码问题
pnpm format               # 格式化代码
pnpm type-check           # TypeScript 类型检查

# 依赖管理
pnpm outdated             # 检查过期依赖
pnpm audit                # 安全漏洞检查
```

---

**总结**: 遵循这些规范确保项目代码质量、开发效率和团队协作的一致性。所有修改都应严格按照这些标准执行。
