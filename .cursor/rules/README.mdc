---
description:
globs:
alwaysApply: true
---
# Vue 3 管理系统项目规则

> 这是 Vue 3 管理系统项目的 Cursor Rules，帮助 AI 助手理解项目结构和开发规范。

## 🚀 项目技术栈

- **Vue 3.5.13** + Composition API + TypeScript 5.8.3
- **Element Plus 2.10.2** + SCSS 样式预处理
- **Vite 6.3.5** + **Pinia 3.0.2** + **Vue Router 4.5.1**
- **ECharts 5.6.0** + **Axios 1.9.0** + **Day.js 1.11.13**

## ⚠️ 核心约束

### 关键限制
- 🚫 **禁止自动提交代码** - 所有 Git 操作由用户手动控制
- 🚫 **禁止修改目录结构** - 不删除现有目录，不修改 vite.config.ts 别名
- 🚫 **禁止自动调整样式** - 尊重用户手动修改的样式代码
- 🚫 **禁止使用 UnoCSS** - 项目使用 SCSS + Element Plus

### 开发要求
- ✅ **强制使用 pnpm** 包管理器
- ✅ **使用中文回复** 用户
- ✅ **直接修改代码** 不只给建议
- ✅ **遵循 Vue 3 Composition API** + `<script setup>`

## 📁 项目结构

```
src/
├── api/           # HTTP 请求封装
├── assets/        # 静态资源
├── components/    # 公共组件 (目录结构 + index.vue)
├── layout/        # 布局组件
├── router/        # 路由配置
├── store/         # Pinia 状态管理
├── styles/        # SCSS 全局样式
├── types/         # TypeScript 类型
├── utils/         # 工具函数
├── views/         # 页面组件
├── App.vue        # 根组件
└── main.ts        # 入口文件
```

## 🔗 路径别名

```typescript
// 可用的导入别名
import from '@/'           // → src/
import from '@/components' // → src/components/
import from '@/views'      // → src/views/
import from '@/store'      // → src/store/
import from '@/api'        // → src/api/
import from '@/utils'      // → src/utils/
import from '@/types'      // → src/types/
```

## 📋 快速开始

```bash
# 安装依赖 (强制使用 pnpm)
pnpm install

# 启动开发服务器 (http://localhost:5173)
pnpm dev

# 代码检查和格式化
pnpm lint && pnpm format

# 构建生产版本
pnpm build
```

## 📖 详细规范

详细的开发规范、代码标准、Git 规范等请查看：**[完整开发指南](mdc:.cursor/rules/project-guide.mdc)**

---

**项目状态**: 约 600 行代码，15 个文件，构建大小约 500KB (gzipped)
