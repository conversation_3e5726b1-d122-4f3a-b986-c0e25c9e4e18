{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "http://**************:8080", "description": "Generated server url"}], "tags": [{"name": "报表统计-demo", "description": "Demo样例"}, {"name": "VOC总览", "description": "VOC总览相关接口"}, {"name": "VOC声音数据管理", "description": "VOC声音数据测试数据生成和管理"}, {"name": "标签纠错", "description": "标签纠错"}, {"name": "角色信息", "description": "角色信息"}], "paths": {"/insDictItem/update": {"post": {"tags": ["数据字典项管理"], "summary": "更新数据字典项", "operationId": "updateDictItem", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsReportDictItemModel"}}}, "required": true}, "responses": {"200": {"description": "更新成功", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultInteger"}}}}}}}, "/insDictItem/insert": {"post": {"tags": ["数据字典项管理"], "summary": "新增数据字典项", "operationId": "insert", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsReportDictItemModel"}}}, "required": true}, "responses": {"200": {"description": "新增成功", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultInteger"}}}}}}}, "/insDictItem/dict-item-list": {"post": {"tags": ["数据字典项管理"], "summary": "分页查询数据字典项列表", "operationId": "getDictItemList", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsReportDictItemModel"}}}, "required": true}, "responses": {"200": {"description": "查询成功", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultPageInfoDictItemListVo"}}}}}}}, "/insDict/update": {"post": {"tags": ["数据字典管理"], "summary": "更新数据字典", "operationId": "updateDict", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsReportDictModel"}}}, "required": true}, "responses": {"200": {"description": "更新成功", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultInteger"}}}}}}}, "/insDict/insert": {"post": {"tags": ["数据字典管理"], "summary": "新增数据字典", "operationId": "insert_1", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsReportDictModel"}}}, "required": true}, "responses": {"200": {"description": "新增成功", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultInteger"}}}}}}}, "/insDict/dict-list": {"post": {"tags": ["数据字典管理"], "summary": "分页查询数据字典列表", "operationId": "getDictList", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsReportDictModel"}}}, "required": true}, "responses": {"200": {"description": "查询成功", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultPageInfoDictListVo"}}}}}}}, "/insDict/batch-delete": {"post": {"tags": ["数据字典管理"], "summary": "批量删除数据字典", "operationId": "deleteDictBatch", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "删除成功", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultInteger"}}}}}}}, "/insDictItem/dict-items-by-dict/{dictId}": {"get": {"tags": ["数据字典项管理"], "summary": "根据字典ID查询所有字典项", "operationId": "getDictItemsByDictId", "parameters": [{"name": "dictId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "查询成功", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListDictItemListVo"}}}}}}}, "/insDictItem/dict-item-detail/{id}": {"get": {"tags": ["数据字典项管理"], "summary": "根据ID查询数据字典项详情", "operationId": "getDictItemDetail", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "查询成功", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultDictItemDetailVo"}}}}}}}, "/insDict/dict-detail/{id}": {"get": {"tags": ["数据字典管理"], "summary": "根据ID查询数据字典详情", "operationId": "getDictDetail", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "查询成功", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultDictDetailVo"}}}}}}}, "/insDictItem/delete/{id}": {"delete": {"tags": ["数据字典项管理"], "summary": "删除数据字典项", "operationId": "deleteDictItem", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "删除成功", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultInteger"}}}}}}}, "/insDict/delete/{id}": {"delete": {"tags": ["数据字典管理"], "summary": "删除数据字典", "operationId": "deleteDict", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer [token]", "required": true}], "responses": {"200": {"description": "删除成功", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultInteger"}}}}}}}}, "components": {"schemas": {"ResultInteger": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "integer", "description": "返回数据对象", "format": "int32"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "ConditionDetailsVo": {"type": "object", "properties": {"key": {"type": "string"}, "value": {"type": "string"}, "code": {"type": "string"}, "img": {"type": "string"}, "startThresholdValue": {"type": "string"}, "sort": {"type": "integer", "format": "int32"}, "endThresholdValue": {"type": "string"}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/ConditionDetailsVo"}}}, "description": "条件详情列表"}, "ConditionVo": {"type": "object", "properties": {"key": {"type": "string", "description": "条件键"}, "details": {"type": "array", "description": "条件详情列表", "items": {"$ref": "#/components/schemas/ConditionDetailsVo"}}}, "description": "查询条件VO"}, "InsReportRolePermissionVo": {"type": "object", "properties": {"id": {"type": "string", "description": "主键ID"}, "parentId": {"type": "string", "description": "父级ID"}, "name": {"type": "string", "description": "名称"}, "htmlUri": {"type": "string", "description": "HTML路径"}, "apiUrl": {"type": "string", "description": "API路径"}, "permissionKey": {"type": "string", "description": "权限key"}, "buttonCode": {"type": "integer", "description": "按钮编码", "format": "int32"}, "sortNo": {"type": "integer", "description": "排序", "format": "int32"}, "icon": {"type": "string", "description": "图标"}, "lastLevel": {"type": "string", "description": "是否末级"}, "appId": {"type": "string", "description": "应用ID"}, "filterStatus": {"type": "integer", "description": "过滤状态", "format": "int32"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "permissionType": {"type": "integer", "description": "1菜单 2按钮", "format": "int32"}}}, "InsReportSystemVo": {"type": "object", "properties": {"id": {"type": "string"}, "systemWatermarking": {"type": "boolean"}, "documentWatermarking": {"type": "boolean"}, "defaultPeriod": {"type": "string"}}}, "InsUserInfoVo": {"type": "object", "properties": {"userId": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string"}, "systemIcon": {"type": "string"}, "headPortrait": {"type": "string"}, "systemName": {"type": "string"}, "clientIds": {"$ref": "#/components/schemas/ConditionVo"}, "isAdmin": {"type": "boolean"}, "defaultClientId": {"type": "string"}, "menus": {"type": "array", "items": {"$ref": "#/components/schemas/InsReportRolePermissionVo"}}, "drillDowns": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "button": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "employeeId": {"type": "string", "description": "员工编号"}, "deptName": {"type": "string", "description": "部门名称"}, "defaultBrand": {"type": "string"}, "brands": {"$ref": "#/components/schemas/ConditionVo"}, "threshold": {"$ref": "#/components/schemas/ConditionVo"}, "systemInfoVo": {"$ref": "#/components/schemas/InsReportSystemVo"}, "roleId": {"type": "string"}, "roleName": {"type": "string"}, "verbal": {"type": "object", "additionalProperties": {"type": "string"}}, "appTags": {"type": "array", "items": {"$ref": "#/components/schemas/ConditionDetailsVo"}}, "isExport": {"type": "boolean"}, "isDownload": {"type": "boolean"}}, "description": "返回数据对象"}, "ResultInsUserInfoVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/InsUserInfoVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "ReportUserInfoVo": {"type": "object", "properties": {"userId": {"type": "string"}, "username": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string"}, "employeeId": {"type": "string", "description": "员工编号"}, "deptName": {"type": "string", "description": "部门名称"}}, "description": "返回数据对象"}, "ResultReportUserInfoVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/ReportUserInfoVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "ResultObject": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "object", "description": "返回数据对象"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "InsReportSystemInfoModel": {"type": "object", "properties": {"id": {"type": "string", "description": "系统配置id"}, "clientId": {"type": "string", "description": "客户id"}, "systemWatermarking": {"type": "boolean", "description": "是否显示系统水印"}, "documentWatermarking": {"type": "boolean", "description": "是否显示文档水印"}, "defaultPeriod": {"type": "string", "description": "默认时间周期 日:d 月:m 周：w 季:q 年:y"}}}, "RoleReportAuthModel": {"required": ["areaIds", "brandCode", "channelIds", "clientId", "permissionIdList", "<PERSON><PERSON><PERSON>", "seriesIds"], "type": "object", "properties": {"id": {"type": "string", "description": "角色Id编辑时必传"}, "roleId": {"type": "string"}, "clientId": {"type": "string", "description": "客户ID不能为空"}, "roleName": {"type": "string", "description": "角色名称"}, "permissionIdList": {"type": "array", "description": "菜单IdList", "items": {"type": "string", "description": "菜单IdList"}}, "seriesIds": {"type": "array", "description": "关联车系， 传code以后英文逗号(,)分隔", "items": {"type": "string", "description": "关联车系， 传code以后英文逗号(,)分隔"}}, "channelIds": {"type": "array", "description": "关联渠道ID，以后英文逗号(,)分隔", "items": {"type": "string", "description": "关联渠道ID，以后英文逗号(,)分隔"}}, "businessTagIds": {"type": "array", "description": "关联业务标签，传code以后英文逗号(,)分隔", "items": {"type": "string", "description": "关联业务标签，传code以后英文逗号(,)分隔"}}, "serviceTagIds": {"type": "array", "description": "关联业务标签，传code以后英文逗号(,)分隔", "items": {"type": "string", "description": "关联业务标签，传code以后英文逗号(,)分隔"}}, "qualityTagIds": {"type": "array", "description": "关联质量标签，传code以后英文逗号(,)分隔", "items": {"type": "string", "description": "关联质量标签，传code以后英文逗号(,)分隔"}}, "areaIds": {"type": "array", "description": "关联业务标签，传code以后英文逗号(,)分隔", "items": {"type": "string", "description": "关联业务标签，传code以后英文逗号(,)分隔"}}, "isExport": {"type": "boolean", "description": "功能权限：是否可以导出 true为是"}, "isDownload": {"type": "boolean", "description": "功能权限：是否可以下载 true为是"}, "allPermission": {"type": "boolean", "description": "是否拥有所有权限 true:是"}, "brandCode": {"type": "string", "description": "品牌code"}, "enabled": {"type": "integer", "description": "角色状态", "format": "int32"}, "remark": {"type": "string"}}}, "RoleReportUserVo": {"required": ["clientId", "userId"], "type": "object", "properties": {"userId": {"type": "string", "description": "用户ID"}, "clientId": {"type": "string", "description": "客户ID"}, "tree": {"type": "boolean", "description": "是否树形结构，默认false"}, "admin": {"type": "boolean", "description": "是否管理员"}}, "description": "角色报表用户VO，包含用户ID、客户ID、树形标识及管理员标识"}, "ReportRoleAuthListVo": {"type": "object", "properties": {"id": {"type": "string", "description": "菜单ID"}, "pid": {"type": "string", "description": "父级ID"}, "icon": {"type": "string", "description": "icon"}, "name": {"type": "string", "description": "菜单名称"}, "path": {"type": "string", "description": "路径"}, "apiPath": {"type": "string", "description": "路径"}, "permissionKey": {"type": "string", "description": "权限key"}, "sort": {"type": "integer", "description": "排序", "format": "int32"}, "children": {"type": "array", "description": "子级菜单", "items": {"$ref": "#/components/schemas/ReportRoleAuthListVo"}}}, "description": "二级菜单权限树"}, "ResultUserReportRoleInfoVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/UserReportRoleInfoVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "UserReportRoleInfoVo": {"type": "object", "properties": {"roleAuthListVoList": {"type": "array", "description": "二级菜单权限树", "items": {"$ref": "#/components/schemas/ReportRoleAuthListVo"}}, "insRolePermissionVos": {"type": "array", "description": "所有权限集合", "items": {"$ref": "#/components/schemas/InsReportRolePermissionVo"}}}, "description": "返回数据对象"}, "InsReportRoleQueryModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "pageNum": {"type": "integer", "format": "int32"}, "order": {"type": "string"}, "enabled": {"type": "string", "description": "角色状态"}, "roleName": {"type": "string", "description": "角色名称"}, "clientId": {"type": "string", "description": "客户ID，默认0"}, "roleId": {"type": "string", "description": "角色ID"}, "searchKeyword": {"type": "string", "description": "搜索关键字"}, "brandCode": {"type": "string", "description": "品牌编码"}, "brandName": {"type": "string", "description": "品牌名称"}, "checkAdmin": {"type": "boolean", "description": "是否校验管理员，默认false"}, "selectAll": {"type": "boolean", "description": "是否全选，默认false"}, "permissionIdList": {"type": "array", "description": "权限ID列表", "items": {"type": "string", "description": "权限ID列表"}}, "tagLibType": {"type": "string", "description": "标签类型"}}, "description": "报表角色查询条件模型，包含角色状态、名称、客户ID、权限等查询条件"}, "RelationCarVo": {"type": "object", "properties": {"id": {"type": "string", "description": "主键ID"}, "carSeriesCode": {"type": "string", "description": "车型系列编码"}, "carSeriesName": {"type": "string", "description": "车型系列名称"}, "checked": {"type": "boolean", "description": "是否选中"}}, "description": "关联车型VO，包含车型系列信息及选中状态"}, "ResultListRoleReportAuthVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "array", "description": "返回数据对象", "items": {"$ref": "#/components/schemas/RoleReportAuthVo"}}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "RoleReportAuthTree": {"type": "object", "properties": {"id": {"type": "string", "description": "菜单ID"}, "code": {"type": "string", "description": "Code"}, "pid": {"type": "string", "description": "父级ID"}, "icon": {"type": "string", "description": "icon"}, "name": {"type": "string", "description": "菜单名称"}, "path": {"type": "string", "description": "路径"}, "permissionKey": {"type": "string"}, "checkButton": {"type": "boolean", "description": "是否是按钮"}, "sort": {"type": "integer", "format": "int32"}, "checked": {"type": "boolean", "description": "是否选中"}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/RoleReportAuthTree"}}, "drillDownPermissionList": {"type": "array", "items": {"$ref": "#/components/schemas/RoleReportAuthTree"}}, "drillDownPermission": {"type": "boolean"}, "apiPath": {"type": "string"}, "alwaysShow": {"type": "string"}}, "description": "应用看板"}, "RoleReportAuthVo": {"type": "object", "properties": {"dataChannel": {"type": "object", "description": "数据渠道"}, "relationBuTag": {"type": "object", "description": "关联业务标签"}, "qualityTag": {"type": "object", "description": "质量标签"}, "serviceTag": {"type": "object", "description": "服务标签"}, "relationCar": {"type": "array", "description": "关联车系", "items": {"$ref": "#/components/schemas/RelationCarVo"}}, "appKanban": {"type": "array", "description": "应用看板", "items": {"$ref": "#/components/schemas/RoleReportAuthTree"}}, "area": {"type": "object", "description": "区域"}, "seriesIds": {"type": "array", "description": "关联车系， 传code以后英文逗号(,)分隔", "items": {"type": "string", "description": "关联车系， 传code以后英文逗号(,)分隔"}}, "serviceTagIds": {"type": "array", "description": "服务标签， 传code以后英文逗号(,)分隔", "items": {"type": "string", "description": "服务标签， 传code以后英文逗号(,)分隔"}}, "channelIds": {"type": "array", "description": "关联渠道ID，以后英文逗号(,)分隔", "items": {"type": "string", "description": "关联渠道ID，以后英文逗号(,)分隔"}}, "businessTagIds": {"type": "array", "description": "关联产品标签，传code以后英文逗号(,)分隔", "items": {"type": "string", "description": "关联产品标签，传code以后英文逗号(,)分隔"}}, "qualityTagIds": {"type": "array", "description": "关联质量标签，传code以后英文逗号(,)分隔", "items": {"type": "string", "description": "关联质量标签，传code以后英文逗号(,)分隔"}}, "areaIds": {"type": "array", "description": "关联业务标签，传code以后英文逗号(,)分隔", "items": {"type": "string", "description": "关联业务标签，传code以后英文逗号(,)分隔"}}, "roleId": {"type": "string", "description": "角色ID"}, "roleName": {"type": "string", "description": "角色名称"}, "roleType": {"type": "integer", "description": "角色类型", "format": "int32"}, "brandCode": {"type": "string", "description": "品牌代码"}, "brandName": {"type": "string", "description": "品牌名称"}, "brandCodeList": {"type": "array", "description": "品牌代码列表", "items": {"type": "string", "description": "品牌代码列表"}}, "isExport": {"type": "boolean", "description": "是否导出"}, "isDownload": {"type": "boolean", "description": "是否下载"}, "allPermission": {"type": "boolean", "description": "全部权限"}, "status": {"type": "integer", "description": "状态", "format": "int32"}, "remark": {"type": "string", "description": "备注"}, "checked": {"type": "boolean", "description": "是否选中"}, "appTags": {"type": "array", "description": "应用标签", "items": {"type": "string", "description": "应用标签"}}}, "description": "角色权限信息VO"}, "PageInfoRoleReportListVo": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/RoleReportListVo"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}, "description": "返回数据对象"}, "ResultPageInfoRoleReportListVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/PageInfoRoleReportListVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "RoleReportListVo": {"type": "object", "properties": {"roleId": {"type": "string", "description": "角色ID"}, "roleName": {"type": "string", "description": "角色名称"}, "roleType": {"type": "integer", "description": "角色类型", "format": "int32"}, "brandCode": {"type": "string", "description": "品牌代码"}, "brandName": {"type": "string", "description": "品牌名称"}, "status": {"type": "string", "description": "状态"}, "roleStatusName": {"type": "string", "description": "角色状态名称"}, "remark": {"type": "string", "description": "备注"}}, "description": "角色列表VO"}, "InsReportCenterInfoModel": {"required": ["endDate", "startDate", "userId"], "type": "object", "properties": {"startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "userId": {"type": "string"}, "clientId": {"type": "string"}, "channelIds": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "riskChannelIds": {"type": "string"}, "areaIds": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "subjectList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "sentimentList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "faultLevelList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "carSeriesList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "riskCarSeriesList": {"type": "string"}, "brandCodeList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "labelTypeLevelFirstList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "riskLabelTypeLevelFirstList": {"type": "string"}, "labelTypeLevelSecondList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "riskLabelTypeLevelSecondList": {"type": "string"}, "labelTypeLevelThreeList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "labelTypeLevelFourList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "labelTypeLevelFourDisableList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "topicList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "labelTypeList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "tagType": {"type": "string", "description": "标签分类:PROD SERVICE QY  下钻页单值条件使用"}, "dlrShortNameList": {"type": "array", "items": {"type": "string"}}, "custTypeList": {"type": "array", "items": {"type": "string"}}, "genderList": {"type": "array", "items": {"type": "string"}}, "ageRanges": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "string"}}}, "vocAgeRanges": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "string"}}}, "riskLevelList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "tagLabelList": {"type": "array", "items": {"type": "array", "items": {"type": "string"}}}, "searchLabelLevel": {"type": "string"}, "ageCode": {"type": "array", "description": "车主年龄Code", "items": {"type": "string", "description": "车主年龄Code"}}, "vocAgeCode": {"type": "array", "description": "车辆年龄Code", "items": {"type": "string", "description": "车辆年龄Code"}}, "custType": {"type": "string", "description": "客户类型名称"}, "dlrShortCode": {"type": "array", "description": "专营店Code", "items": {"type": "string", "description": "专营店Code"}}, "gender": {"type": "string", "description": "客户性别名称"}, "yoy": {"type": "integer", "format": "int32"}, "scaleMagnitude": {"type": "integer", "format": "int32"}, "scaleMagnitude_": {"type": "integer", "format": "int32"}, "dateUnit": {"type": "integer", "format": "int32"}, "datePeriod": {"type": "string"}, "typeR": {"type": "integer", "format": "int32"}, "typeY": {"type": "integer", "format": "int32"}, "id": {"type": "string"}, "reportName": {"type": "string"}, "reportType": {"type": "string", "description": "报告类型"}, "brandName": {"type": "string", "description": "品牌名称"}, "brandCode": {"type": "string", "description": "品牌code"}, "carSeriesName": {"type": "array", "description": "车系名称", "items": {"type": "string", "description": "车系名称"}}, "carSeriesCode": {"type": "array", "description": "车系Code", "items": {"type": "string", "description": "车系Code"}}, "channelName": {"type": "array", "description": "渠道名称", "items": {"type": "string", "description": "渠道名称"}}, "channelCode": {"type": "array", "description": "渠道Code", "items": {"type": "string", "description": "渠道Code"}}, "dlrShortName": {"type": "array", "description": "专营店名称", "items": {"type": "string", "description": "专营店名称"}}, "labelTypeName": {"type": "string", "description": "标签类型 产品 服务 质量"}, "labelTypeCode": {"type": "string", "description": "标签类型code 产品 服务 质量"}, "labelNameList": {"type": "array", "description": "标签名称集合", "items": {"type": "string", "description": "标签名称集合"}}, "custCode": {"type": "string", "description": "客户类型Code"}, "genderCode": {"type": "string", "description": "客户性别Code"}, "age": {"type": "array", "description": "车主年龄", "items": {"type": "string", "description": "车主年龄"}}, "vocAge": {"type": "array", "description": "车辆年龄", "items": {"type": "string", "description": "车辆年龄"}}, "pageType": {"type": "integer", "description": "分页类型", "format": "int32"}, "taskId": {"type": "string"}, "tempDlrShortCodeList": {"type": "array", "items": {"type": "array", "items": {"type": "string"}}}, "userJourneyCode": {"type": "string"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "newId": {"type": "string", "description": "模型结果集ID"}, "intention": {"type": "string"}, "oneId": {"type": "string"}, "customerName": {"type": "string"}, "order": {"type": "string", "description": "分页参数"}, "bigArea": {"type": "string", "description": "大区"}, "bigAreaIds": {"type": "string", "description": "大区编码"}, "area": {"type": "string", "description": "区域 下钻页单值条件使用-省份"}, "carSeries": {"type": "string", "description": "车系 下钻页单值条件使用"}, "mentionCarSeries": {"type": "string"}}}, "DemoParamsModel": {"required": ["endDate", "startDate", "userId"], "type": "object", "properties": {"startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "userId": {"type": "string"}, "clientId": {"type": "string"}, "channelIds": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "riskChannelIds": {"type": "string"}, "areaIds": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "subjectList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "sentimentList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "faultLevelList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "carSeriesList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "riskCarSeriesList": {"type": "string"}, "brandCodeList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "labelTypeLevelFirstList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "riskLabelTypeLevelFirstList": {"type": "string"}, "labelTypeLevelSecondList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "riskLabelTypeLevelSecondList": {"type": "string"}, "labelTypeLevelThreeList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "labelTypeLevelFourList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "labelTypeLevelFourDisableList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "topicList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "labelTypeList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "tagType": {"type": "string", "description": "标签分类:PROD SERVICE QY  下钻页单值条件使用"}, "dlrShortNameList": {"type": "array", "items": {"type": "string"}}, "custTypeList": {"type": "array", "items": {"type": "string"}}, "genderList": {"type": "array", "items": {"type": "string"}}, "ageRanges": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "string"}}}, "vocAgeRanges": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "string"}}}, "riskLevelList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "tagLabelList": {"type": "array", "items": {"type": "array", "items": {"type": "string"}}}, "searchLabelLevel": {"type": "string"}, "ageCode": {"type": "array", "description": "车主年龄Code", "items": {"type": "string", "description": "车主年龄Code"}}, "vocAgeCode": {"type": "array", "description": "车辆年龄Code", "items": {"type": "string", "description": "车辆年龄Code"}}, "custType": {"type": "string", "description": "客户类型名称"}, "dlrShortCode": {"type": "array", "description": "专营店Code", "items": {"type": "string", "description": "专营店Code"}}, "gender": {"type": "string", "description": "客户性别名称"}, "yoy": {"type": "integer", "format": "int32"}, "scaleMagnitude": {"type": "integer", "format": "int32"}, "scaleMagnitude_": {"type": "integer", "format": "int32"}, "dateUnit": {"type": "integer", "format": "int32"}, "datePeriod": {"type": "string"}, "typeR": {"type": "integer", "format": "int32"}, "typeY": {"type": "integer", "format": "int32"}}}, "Result": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "object", "description": "返回数据对象"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "OverviewParamsModel": {"required": ["endDate", "startDate", "userId"], "type": "object", "properties": {"startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "userId": {"type": "string"}, "clientId": {"type": "string"}, "channelIds": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "riskChannelIds": {"type": "string"}, "areaIds": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "subjectList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "sentimentList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "faultLevelList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "carSeriesList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "riskCarSeriesList": {"type": "string"}, "brandCodeList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "labelTypeLevelFirstList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "riskLabelTypeLevelFirstList": {"type": "string"}, "labelTypeLevelSecondList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "riskLabelTypeLevelSecondList": {"type": "string"}, "labelTypeLevelThreeList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "labelTypeLevelFourList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "labelTypeLevelFourDisableList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "topicList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "labelTypeList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "tagType": {"type": "string", "description": "标签分类:PROD SERVICE QY  下钻页单值条件使用"}, "dlrShortNameList": {"type": "array", "items": {"type": "string"}}, "custTypeList": {"type": "array", "items": {"type": "string"}}, "genderList": {"type": "array", "items": {"type": "string"}}, "ageRanges": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "string"}}}, "vocAgeRanges": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "string"}}}, "riskLevelList": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "tagLabelList": {"type": "array", "items": {"type": "array", "items": {"type": "string"}}}, "searchLabelLevel": {"type": "string"}, "ageCode": {"type": "array", "description": "车主年龄Code", "items": {"type": "string", "description": "车主年龄Code"}}, "vocAgeCode": {"type": "array", "description": "车辆年龄Code", "items": {"type": "string", "description": "车辆年龄Code"}}, "custType": {"type": "string", "description": "客户类型名称"}, "dlrShortCode": {"type": "array", "description": "专营店Code", "items": {"type": "string", "description": "专营店Code"}}, "gender": {"type": "string", "description": "客户性别名称"}, "yoy": {"type": "integer", "format": "int32"}, "scaleMagnitude": {"type": "integer", "format": "int32"}, "scaleMagnitude_": {"type": "integer", "format": "int32"}, "dateUnit": {"type": "integer", "format": "int32"}, "datePeriod": {"type": "string"}, "typeR": {"type": "integer", "format": "int32"}, "typeY": {"type": "integer", "format": "int32"}, "userJourneyCode": {"type": "string"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "riskLevel": {"type": "string", "description": "风险等级"}, "riskId": {"type": "string", "description": "风险Id"}, "newId": {"type": "string", "description": "模型结果集ID"}, "intention": {"type": "string"}, "oneId": {"type": "string"}, "customerName": {"type": "string"}, "order": {"type": "string", "description": "分页参数"}, "bigArea": {"type": "string", "description": "大区"}, "bigAreaIds": {"type": "string", "description": "大区编码"}, "area": {"type": "string", "description": "区域 下钻页单值条件使用-省份"}, "carSeries": {"type": "string", "description": "车系 下钻页单值条件使用"}, "mentionCarSeries": {"type": "string"}, "year": {"type": "string"}, "dimensionality": {"type": "string", "description": "时间维度：月:12 ,周：49"}, "pageSource": {"type": "string"}, "reportType": {"type": "integer", "format": "int32"}, "riskType": {"type": "integer", "format": "int32"}}}, "InsReportOperationLogModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "pageNum": {"type": "integer", "format": "int32"}, "order": {"type": "string"}, "startDate": {"type": "string", "description": "开始时间"}, "endDate": {"type": "string", "description": "结束时间"}, "deptId": {"type": "array", "description": "所属部门", "items": {"type": "string", "description": "所属部门"}}, "searchKeyword": {"type": "string", "description": "关键词"}, "appId": {"type": "string", "description": "系统标识"}}}, "InsertReportLabelCorrectionRecordModel": {"required": ["clientId", "newId"], "type": "object", "properties": {"newId": {"type": "string", "description": "唯一ID"}, "clientId": {"type": "string", "description": "客户ID不能为空"}, "errorType": {"type": "integer", "description": "错误类型 1无效数据 2有效数据", "format": "int32"}, "topicSelect": {"type": "integer", "description": "选中状态 1正确 2错误", "format": "int32"}, "topic": {"type": "string", "description": "观点"}, "tagSelect": {"type": "integer", "description": "选中状态 1正确 2错误", "format": "int32"}, "labelTypeLevelFirst": {"type": "string", "description": "一级标签名称"}, "labelTypeLevelSecond": {"type": "string", "description": "二级标签名称"}, "labelTypeLevelThree": {"type": "string", "description": "三级级标签名称"}, "labelTypeLevelFour": {"type": "string", "description": "四级级标签名称"}, "labelTypeLevelFive": {"type": "string", "description": "五级标签名称"}, "labelTypeLevelFirstCode": {"type": "string"}, "labelTypeLevelSecondCode": {"type": "string"}, "labelTypeLevelThreeCode": {"type": "string"}, "labelTypeLevelFourCode": {"type": "string"}, "labelTypeLevelFiveCode": {"type": "string"}, "sentimentSelect": {"type": "integer", "description": "选中状态 1正确 2错误", "format": "int32"}, "sentiment": {"type": "string", "description": "情感"}, "intentionSelect": {"type": "integer", "description": "选中状态 1正确 2错误", "format": "int32"}, "intention": {"type": "string", "description": "意图"}, "operateUser": {"type": "string", "description": "纠错人"}, "labelType": {"type": "string"}}}, "InsDataSourceModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "pageNum": {"type": "integer", "format": "int32"}, "order": {"type": "string"}, "id": {"type": "string", "description": "id"}, "dataSourceName": {"type": "string", "description": "数据源名称"}, "dataSourceType": {"type": "string", "description": "数据源类型"}, "dataSourceAccessWay": {"type": "string", "description": "数据源接入方式"}, "fileName": {"type": "string", "description": "文件名称"}, "clientId": {"type": "string", "description": "所属客户id"}, "dataName": {"type": "string", "description": "数据名称"}, "batchId": {"type": "string", "description": "数据源批次id"}, "dataSourceId": {"type": "string", "description": "数据源id"}, "status": {"type": "string", "description": "数据状态"}, "dataSource": {"type": "string", "description": "数据源"}, "workIdList": {"uniqueItems": true, "type": "array", "description": "数据链路id集合", "items": {"type": "string", "description": "数据链路id集合"}}, "startTime": {"type": "string", "description": "开始时间"}, "workId": {"type": "string", "description": "数据链路id"}, "endTime": {"type": "string", "description": "结束时间"}, "channelIdList": {"type": "array", "description": "渠道ID", "items": {"type": "string", "description": "渠道ID"}}, "dataStatus": {"type": "array", "description": "数据状态", "items": {"type": "string", "description": "数据状态"}}, "keywords": {"type": "string", "description": "关键词"}, "sentiment": {"type": "array", "description": "情感", "items": {"type": "string", "description": "情感"}}, "intention": {"type": "array", "description": "意图", "items": {"type": "string", "description": "意图"}}, "brandCode": {"type": "array", "description": "品牌", "items": {"type": "string", "description": "品牌"}}, "carSeries": {"type": "array", "description": "车系", "items": {"type": "string", "description": "车系"}}, "businessEndTag": {"type": "array", "description": "业务末级标签", "items": {"type": "string", "description": "业务末级标签"}}, "qualityEndTag": {"type": "array", "description": "质量末级标签", "items": {"type": "string", "description": "质量末级标签"}}, "labelType": {"type": "array", "description": "标签类型", "items": {"type": "string", "description": "标签类型"}}, "modelType": {"type": "string", "description": "处理模型"}, "labelTypeList": {"type": "array", "description": "标签类型", "items": {"type": "string", "description": "标签类型"}}, "requestId": {"type": "string"}, "total": {"type": "string"}, "currentBatchTotal": {"type": "string"}, "batchPageTotal": {"type": "string"}, "currentBatchPage": {"type": "string"}, "data": {"type": "object"}, "createUser": {"type": "string"}, "errorIds": {"type": "array", "items": {"type": "string"}}, "dataValidity": {"type": "string"}, "cityCodeList": {"type": "array", "items": {"type": "string"}}, "projectId": {"type": "string"}, "ownCarSeries": {"type": "array", "description": "本品车系", "items": {"type": "string", "description": "本品车系"}}, "competitorsCarSeries": {"type": "array", "description": "竞品车系", "items": {"type": "string", "description": "竞品车系"}}, "mentionCarSeriesList": {"type": "array", "description": "同时提及车系", "items": {"type": "string", "description": "同时提及车系"}}, "riskType": {"type": "string", "description": "风险类型"}, "statisticType": {"type": "array", "description": "洞察周期", "items": {"type": "string", "description": "洞察周期"}}, "riskLevel": {"type": "array", "description": "风险等级", "items": {"type": "string", "description": "风险等级"}}, "brand": {"type": "string", "description": "品牌"}, "metaDataType": {"type": "array", "description": "数据类型", "items": {"type": "string", "description": "数据类型"}}, "contentType": {"type": "array", "items": {"type": "string"}}, "dateType": {"type": "string"}, "executeSuccessCount": {"type": "integer", "format": "int32"}, "executeFailCount": {"type": "integer", "format": "int32"}, "date": {"type": "string"}, "showType": {"type": "string"}, "dateList": {"type": "array", "items": {"type": "string"}}}}, "InsReportDictItemModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "pageNum": {"type": "integer", "format": "int32"}, "order": {"type": "string"}, "id": {"type": "string", "description": "字典项ID"}, "dictId": {"type": "string", "description": "字典ID"}, "itemText": {"type": "string", "description": "字典项文本"}, "itemTextEn": {"type": "string", "description": "字典项英文文本"}, "itemKey": {"type": "string", "description": "字典项键"}, "itemValue": {"type": "string", "description": "字典项值"}, "description": {"type": "string", "description": "字典项描述"}, "sortOrder": {"type": "integer", "description": "排序顺序", "format": "int32"}, "status": {"type": "integer", "description": "状态，1-启用，0-不启用", "format": "int32"}, "operator": {"type": "string", "description": "操作人"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}}, "description": "数据字典项查询模型"}, "DictItemListVo": {"type": "object", "properties": {"id": {"type": "string", "description": "字典项ID"}, "dictId": {"type": "string", "description": "字典ID"}, "itemText": {"type": "string", "description": "字典项文本"}, "itemTextEn": {"type": "string", "description": "字典项英文文本"}, "itemKey": {"type": "string", "description": "字典项键"}, "itemValue": {"type": "string", "description": "字典项值"}, "description": {"type": "string", "description": "描述"}, "sortOrder": {"type": "integer", "description": "排序", "format": "int32"}, "status": {"type": "integer", "description": "状态（1启用 0不启用）", "format": "int32"}, "operator": {"type": "string", "description": "创建人"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}}, "description": "数据字典项列表VO"}, "PageInfoDictItemListVo": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/DictItemListVo"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}, "description": "返回数据对象"}, "ResultPageInfoDictItemListVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/PageInfoDictItemListVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "InsReportDictModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32", "refType": null}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "order": {"type": "string", "refType": null}, "id": {"type": "string", "description": "字典ID", "refType": null}, "type": {"type": "integer", "description": "字典类型，0-string类型，1-number类型，2-boolean类型", "format": "int32", "refType": null}, "dictName": {"type": "string", "description": "字典名称", "refType": null}, "dictCode": {"type": "string", "description": "字典编码", "refType": null}, "description": {"type": "string", "description": "字典描述", "refType": null}, "delFlag": {"type": "integer", "description": "删除状态，0-未删除，1-已删除", "format": "int32", "refType": null}, "operator": {"type": "string", "description": "创建人", "refType": null}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "refType": null}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time", "refType": null}}, "description": "数据字典查询模型"}, "DictListVo": {"type": "object", "properties": {"id": {"type": "string", "description": "字典ID", "refType": null}, "dictName": {"type": "string", "description": "字典名称", "refType": null}, "dictCode": {"type": "string", "description": "字典编码", "refType": null}, "type": {"type": "integer", "description": "字典类型", "format": "int32", "refType": null}, "description": {"type": "string", "description": "描述", "refType": null}, "operator": {"type": "string", "description": "创建人", "refType": null}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time", "refType": null}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time", "refType": null}, "itemCount": {"type": "integer", "description": "字典项数量", "format": "int32", "refType": null}}, "description": "数据字典列表VO"}, "PageInfoDictListVo": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64", "refType": null}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/DictListVo"}, "refType": "DictListVo"}, "pageNum": {"type": "integer", "format": "int32", "refType": null}, "pageSize": {"type": "integer", "format": "int32", "refType": null}, "size": {"type": "integer", "format": "int32", "refType": null}, "startRow": {"type": "integer", "format": "int64", "refType": null}, "endRow": {"type": "integer", "format": "int64", "refType": null}, "pages": {"type": "integer", "format": "int32", "refType": null}, "prePage": {"type": "integer", "format": "int32", "refType": null}, "nextPage": {"type": "integer", "format": "int32", "refType": null}, "isFirstPage": {"type": "boolean", "refType": null}, "isLastPage": {"type": "boolean", "refType": null}, "hasPreviousPage": {"type": "boolean", "refType": null}, "hasNextPage": {"type": "boolean", "refType": null}, "navigatePages": {"type": "integer", "format": "int32", "refType": null}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}, "refType": "integer"}, "navigateFirstPage": {"type": "integer", "format": "int32", "refType": null}, "navigateLastPage": {"type": "integer", "format": "int32", "refType": null}}, "description": "返回数据对象"}, "ResultPageInfoDictListVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/PageInfoDictListVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "FocusDistributionVo": {"type": "object", "properties": {"title": {"type": "string"}, "tagSumC": {"type": "number"}, "date": {"type": "string", "description": "日期"}, "tagName": {"type": "string", "description": "聚焦观点标签名称"}, "tagC": {"type": "number", "description": "聚焦观点标签数值"}, "tagP": {"type": "number", "description": "聚焦观点标签百分比"}, "tagRp": {"type": "number", "description": "聚焦观点标签环比百分比"}, "tagYp": {"type": "number", "description": "聚焦观点标签同"}, "labelType": {"type": "string"}, "level": {"type": "integer", "description": "标签等级", "format": "int32"}, "tagFirstLevelName": {"type": "string", "description": "一级标签名称"}, "secondCode": {"type": "string", "description": "二级标签code"}}, "description": "渠道分布"}, "PageInfoUserVoiceVo": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/UserVoiceVo"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}, "description": "返回数据对象"}, "ResultPageInfoUserVoiceVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/PageInfoUserVoiceVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "UserVoiceVo": {"type": "object", "properties": {"originalTextScene": {"type": "string", "description": "原始声音"}, "opinion": {"type": "string", "description": "观点"}, "newId": {"type": "string", "description": "声音id"}, "userId": {"type": "string", "description": "用户id"}, "username": {"type": "string", "description": "用户名"}, "nsrC": {"type": "number", "description": "指标数值"}, "channelC": {"type": "number", "description": "渠道数值"}, "billC": {"type": "number", "description": "单据数值"}, "complainC": {"type": "number", "description": "抱怨数值"}, "complaintC": {"type": "number", "description": "投诉数值"}, "consultC": {"type": "number", "description": "咨询数值"}, "praiseC": {"type": "number", "description": "表扬数值"}, "suggestionC": {"type": "number", "description": "建议数值"}, "statementC": {"type": "number", "description": "陈述数值"}, "opinionC": {"type": "number", "description": "观点数值"}, "channelAttention": {"type": "array", "description": "渠道分布", "items": {"$ref": "#/components/schemas/FocusDistributionVo"}}, "channelName": {"type": "string", "description": "渠道名称"}, "bizCreateTime": {"type": "string", "description": "业务产生的时间"}, "channelBiz": {"type": "string", "description": "渠道业务"}, "carSeriesName": {"type": "string", "description": "车系名称"}, "provinceName": {"type": "string", "description": "省份名称"}, "focus": {"type": "string", "description": "焦点"}, "nsrG": {"type": "string"}, "channelCode": {"type": "string"}, "yearTime": {"type": "string"}}}, "ResultUserVoiceVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/UserVoiceVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "ResultListUserExperienceIndexVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "array", "description": "返回数据对象", "items": {"$ref": "#/components/schemas/UserExperienceIndexVo"}}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "UserExperienceIndexVo": {"type": "object", "properties": {"title": {"type": "string", "description": "标题"}, "tagName": {"type": "string", "description": "标签名称"}, "nsrC": {"type": "number", "description": "体验指数值"}, "nsrRp": {"type": "number", "description": "体验指数环比百分比"}, "nsrYp": {"type": "number", "description": "体验指数同比百分比"}, "nsrG": {"type": "string", "description": "图标"}, "firstCode": {"type": "string"}}, "description": "返回数据对象"}, "DataPresentationVo": {"type": "object", "properties": {"eiTitle": {"type": "string", "description": "体验指数标题"}, "eiNsrC": {"type": "number", "description": "体验指数值"}, "eiNsrRp": {"type": "number", "description": "体验指数环比值"}, "eiNsrYp": {"type": "number", "description": "体验指数同比值"}, "eiNsrG": {"type": "string", "description": "体验指数表情"}, "prodTitle": {"type": "string", "description": "产品体验指数标题"}, "prodNsr": {"type": "number", "description": "产品体验指数值"}, "prodNsrRp": {"type": "number", "description": "产品体验指数环比百分比"}, "prodNsrYp": {"type": "number", "description": "产品体验指数同比百分比"}, "prodNsrG": {"type": "string", "description": "产品体验指数表情"}, "servTitle": {"type": "string", "description": "服务体验指数标题"}, "servNsr": {"type": "number", "description": "服务体验指数值"}, "servNsrRp": {"type": "number", "description": "服务体验指数环比百分比"}, "servNsrYp": {"type": "number", "description": "服务体验指数同比百分比"}, "servNsrG": {"type": "string", "description": "服务体验指数表情"}, "positiveTitle": {"type": "string", "description": "观点数标题"}, "positiveNsrC": {"type": "number", "description": "观点数值"}, "positiveNsrRp": {"type": "number", "description": "观点数环比百分比"}, "positiveNsrYp": {"type": "number", "description": "观点数同比百分比"}, "positiveNsrG": {"type": "string", "description": "图标"}, "userTitle": {"type": "string", "description": "用户数标题"}, "userNsrC": {"type": "number", "description": "用户数值"}, "userNsrRp": {"type": "number", "description": "用户数环比百分比"}, "userNsrYp": {"type": "number", "description": "用户数同比百分比"}, "userNsrG": {"type": "string", "description": "图标"}, "serverOrderTitle": {"type": "string", "description": "单据数标题"}, "serverOrderNsrC": {"type": "number", "description": "单据数值"}, "serverOrderNsrRp": {"type": "number", "description": "单据数环比百分比"}, "serverOrderNsrYp": {"type": "number", "description": "单据数同比百分比"}, "serverOrderNsrG": {"type": "string", "description": "图标"}, "labelType": {"type": "string"}, "date": {"type": "string", "description": "日期"}}, "description": "返回数据对象"}, "ResultListDataPresentationVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "array", "description": "返回数据对象", "items": {"$ref": "#/components/schemas/DataPresentationVo"}}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "FocusVo": {"type": "object", "properties": {"focusName": {"type": "string", "description": "焦点问题名称"}, "focusC": {"type": "string", "description": "焦点问题值"}}, "description": "焦点问题"}, "ResultTrendVariationVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/TrendVariationVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "RiskDetailVo": {"type": "object", "properties": {"date": {"type": "string"}, "title": {"type": "string", "description": "风险标题"}, "riskLevelS": {"type": "string", "description": "风险等级"}, "riskC": {"type": "number", "description": "风险值"}, "risk": {"type": "string", "description": "风险名称"}, "riskSumC": {"type": "number", "description": "风险总数"}, "negativeC": {"type": "number", "description": "负面观点值"}, "focusName": {"type": "string", "description": "焦点问题"}, "userC": {"type": "number", "description": "用户数值"}, "carSeriesName": {"type": "string", "description": "涉及车系"}, "complainC": {"type": "number", "description": "投诉数"}, "emotionR": {"type": "number", "description": "情感值"}, "labelType": {"type": "string"}, "riskId": {"type": "string", "description": "风险Id"}, "riskNsrC": {"type": "number", "description": "体验指数"}, "riskRp": {"type": "number", "description": "风险值环比百分比"}, "riskP": {"type": "number", "description": "风险值占比"}, "riskYp": {"type": "number", "description": "风险值同比百分比"}, "focusList": {"type": "array", "description": "焦点问题", "items": {"$ref": "#/components/schemas/FocusVo"}}, "riskType": {"type": "string", "description": "风险类型"}}, "description": "预警趋势"}, "TrendVariationVo": {"type": "object", "properties": {"riskNum": {"type": "array", "description": "预警次数", "items": {"$ref": "#/components/schemas/RiskDetailVo"}}, "riskRecord": {"type": "array", "description": "预警记录", "items": {"$ref": "#/components/schemas/RiskDetailVo"}}, "riskTrend": {"type": "array", "description": "预警趋势", "items": {"$ref": "#/components/schemas/RiskDetailVo"}}}, "description": "返回数据对象"}, "FocusAttentionVo": {"type": "object", "properties": {"title": {"type": "string", "description": "聚焦观点标题"}, "tagSumC": {"type": "number", "description": "聚焦观点数值"}, "focusDistribution": {"type": "array", "description": "聚焦关注环图", "items": {"$ref": "#/components/schemas/FocusDistributionVo"}}, "tagDistribution": {"type": "array", "description": "聚焦关注标签分布图", "items": {"$ref": "#/components/schemas/TagDistributionVo"}}}, "description": "返回数据对象"}, "ResultFocusAttentionVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/FocusAttentionVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "TagDistributionVo": {"type": "object", "properties": {"tagName": {"type": "string", "description": "标签名称"}, "tagC": {"type": "number", "description": "标签数量值"}, "tagRp": {"type": "number", "description": "标签环比百分比"}, "tagYp": {"type": "number", "description": "标签同比百分比"}, "firstCode": {"type": "string"}, "secondCode": {"type": "string"}}, "description": "聚焦关注标签分布图"}, "ResultListTagDistributionVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "array", "description": "返回数据对象", "items": {"$ref": "#/components/schemas/TagDistributionVo"}}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "ResultListServiceExperienceIndexVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "array", "description": "返回数据对象", "items": {"$ref": "#/components/schemas/ServiceExperienceIndexVo"}}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "ServiceExperienceIndexVo": {"type": "object", "properties": {"area": {"type": "string", "description": "区域"}, "nsrC": {"type": "number", "description": "体验指数值"}, "nsrRp": {"type": "number", "description": "体验指数环比百分比"}, "nsrYp": {"type": "number", "description": "体验指数同比百分比"}, "nsrG": {"type": "string", "description": "图标"}, "areaCode": {"type": "string", "description": "区域编码"}, "provinceCode": {"type": "string", "description": "省份编码"}, "provinceCodes": {"uniqueItems": true, "type": "array", "description": "省份编码集合", "items": {"type": "string", "description": "省份编码集合"}}}, "description": "返回数据对象"}, "ResultRiskEarlyEventWarningVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/RiskEarlyEventWarningVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "RiskEarlyEventWarningVo": {"type": "object", "properties": {"productSumC": {"type": "number", "description": "产品风险预警总数"}, "productTitle": {"type": "string", "description": "风险标题"}, "productRiskEarly": {"type": "array", "description": "产品风险预警", "items": {"$ref": "#/components/schemas/RiskEarlyVo"}}, "serviceSumC": {"type": "number", "description": "产品风险预警总数"}, "serviceTitle": {"type": "string", "description": "风险标题"}, "serviceRiskEarly": {"type": "array", "description": "服务风险预警", "items": {"$ref": "#/components/schemas/RiskEarlyVo"}}, "qualitySumC": {"type": "number", "description": "品质风险预警总数"}, "qualityTitle": {"type": "string", "description": "品质风险标题"}, "qualityRiskEarly": {"type": "array", "description": "品质风险预警", "items": {"$ref": "#/components/schemas/RiskEarlyVo"}}, "userSumC": {"type": "number", "description": "用户风险预警总数"}, "userTitle": {"type": "string", "description": "用户风险标题"}, "userRiskEarly": {"type": "array", "description": "用户风险预警", "items": {"$ref": "#/components/schemas/RiskEarlyUserWarningVo"}}, "complaintsClassify": {"type": "array", "description": "投诉问题分布", "items": {"$ref": "#/components/schemas/RiskEarlyVo"}}, "complaintsDetail": {"type": "array", "description": "投诉问题详情", "items": {"$ref": "#/components/schemas/RiskDetailVo"}}, "levelDistribution": {"$ref": "#/components/schemas/RiskEarlyVo"}}, "description": "返回数据对象"}, "RiskEarlyUserWarningVo": {"type": "object", "properties": {"levelName": {"type": "string"}, "levelCode": {"type": "string"}, "title": {"type": "string", "description": "风险用户预警标题"}, "nsrC": {"type": "number", "description": "风险用户预警值"}, "nsrSumC": {"type": "number", "description": "风险预警总数值"}, "nsrRp": {"type": "number", "description": "风险用户预警环比百分比"}, "nsrP": {"type": "number", "description": "风险预警占比值"}, "nsrYp": {"type": "number", "description": "风险用户预警同比百分比"}, "riskDetail": {"type": "array", "description": "风险用户预警详情", "items": {"$ref": "#/components/schemas/RiskDetailVo"}}}, "description": "用户风险预警"}, "RiskEarlyVo": {"type": "object", "properties": {"title": {"type": "string"}, "levelName": {"type": "string"}, "nsrC": {"type": "number", "description": "风险预警值"}, "nsrSumC": {"type": "number", "description": "风险预警总数值"}, "nsrRp": {"type": "number", "description": "风险预警环比百分比"}, "nsrYp": {"type": "number", "description": "风险预警同比百分比"}, "nsrP": {"type": "number", "description": "风险预警占比值"}, "labelType": {"type": "string"}, "levelCode": {"type": "string"}, "riskDetail": {"type": "array", "description": "风险详情", "items": {"$ref": "#/components/schemas/RiskDetailVo"}}}, "description": "投诉等级分布"}, "ProductExperienceIndexVo": {"type": "object", "properties": {"name": {"type": "string", "description": "名称"}, "nsrC": {"type": "number", "description": "体验指数值"}, "nsrRp": {"type": "number", "description": "体验指数环比百分比"}, "nsrYp": {"type": "number", "description": "体验指数同比百分比"}, "nsrG": {"type": "string", "description": "图标"}, "imgUrl": {"type": "string", "description": "图片地址"}, "core": {"type": "string", "description": "是否关注"}, "haltSales": {"type": "string"}}, "description": "返回数据对象"}, "ResultListProductExperienceIndexVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "array", "description": "返回数据对象", "items": {"$ref": "#/components/schemas/ProductExperienceIndexVo"}}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "HighFrequencyWordVo": {"type": "object", "properties": {"tagName": {"type": "string", "description": "标签名称"}, "tagC": {"type": "number", "description": "标签值"}, "tagR": {"type": "number", "description": "标签环比"}, "tagY": {"type": "number", "description": "标签同比"}, "tagYp": {"type": "number", "description": "标签同比百分比"}, "tagRp": {"type": "number", "description": "标签环比百分比"}}, "description": "返回数据对象"}, "ResultListHighFrequencyWordVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "array", "description": "返回数据对象", "items": {"$ref": "#/components/schemas/HighFrequencyWordVo"}}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "EmotionIntentionTrendsVo": {"type": "object", "properties": {"date": {"type": "string", "description": "时间"}, "complainC": {"type": "number", "description": "抱怨数值"}, "complainR": {"type": "number", "description": "抱怨环比"}, "complainY": {"type": "number", "description": "抱怨同比"}, "negativeC": {"type": "number", "description": "负面数值"}, "negativeR": {"type": "number", "description": "负面环比"}, "negativeY": {"type": "number", "description": "负面同比"}, "complaintC": {"type": "number", "description": "投诉数值"}, "complaintR": {"type": "number", "description": "投诉环比"}, "complaintY": {"type": "number", "description": "投诉同比"}}, "description": "返回数据对象"}, "ResultListEmotionIntentionTrendsVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "array", "description": "返回数据对象", "items": {"$ref": "#/components/schemas/EmotionIntentionTrendsVo"}}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "ResultListUserVoiceVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "array", "description": "返回数据对象", "items": {"$ref": "#/components/schemas/UserVoiceVo"}}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "ResultDataPresentationVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/DataPresentationVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "BriefReportVo": {"type": "object", "properties": {"dateUnit": {"type": "string", "description": "时间维度"}, "startDate": {"type": "string", "description": "开始时间"}, "endDate": {"type": "string", "description": "结束时间"}, "negativeC": {"type": "number", "description": "负面观点数值"}, "userC": {"type": "number", "description": "用户数值"}, "riskLeve": {"type": "string", "description": "风险等级"}, "carSeriesName": {"type": "string", "description": "涉及车系"}, "opinionWord": {"type": "array", "description": "观点热词", "items": {"$ref": "#/components/schemas/OpinionWordsVo"}}, "negativeRp": {"type": "number", "description": "负面数环比百分比"}, "userRp": {"type": "number", "description": "用户数环比"}, "opinionWords": {"type": "string"}, "complainC": {"type": "string"}, "emotionC": {"type": "string"}, "complainRp": {"type": "string"}, "emotionRp": {"type": "string"}, "warningTime": {"type": "string"}, "datePeriod": {"type": "string"}, "focusNames": {"type": "string"}}, "description": "返回数据对象"}, "OpinionWordsVo": {"type": "object", "properties": {"name": {"type": "string"}, "value": {"type": "string"}}, "description": "观点热词"}, "ResultBriefReportVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/BriefReportVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "ExtendComQueryModel": {"type": "object", "properties": {"dateUnit": {"type": "integer", "description": "时间维度 -1:日, 0:周, 1:月, 2:季, 3:年", "format": "int32"}, "startDate": {"type": "string", "description": "开始时间", "format": "date"}, "endDate": {"type": "string", "description": "结束时间", "format": "date"}, "brandCodeSet": {"uniqueItems": true, "type": "array", "description": "品牌编码，可多选", "items": {"type": "string", "description": "品牌编码，可多选"}}, "sentimentSet": {"uniqueItems": true, "type": "array", "description": "情感，可多选", "items": {"type": "string", "description": "情感，可多选"}}, "intentionSet": {"uniqueItems": true, "type": "array", "description": "意图，可多选", "items": {"type": "string", "description": "意图，可多选"}}, "orderBy": {"type": "string", "description": "排序字段"}, "orderType": {"type": "string", "description": "排序方式，asc/desc"}, "pageNum": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "每页大小", "format": "int32"}, "vehicleSeriesCodeSet": {"uniqueItems": true, "type": "array", "description": "车系编码，可多选", "items": {"type": "string", "description": "车系编码，可多选"}}, "vehicleModelCodeSet": {"uniqueItems": true, "type": "array", "description": "车型编码，可多选", "items": {"type": "string", "description": "车型编码，可多选"}}, "channelCodeSet": {"uniqueItems": true, "type": "array", "description": "渠道编码，可多选", "items": {"type": "string", "description": "渠道编码，可多选"}}, "oneIdSet": {"uniqueItems": true, "type": "array", "description": "oneId，可多选", "items": {"type": "string", "description": "oneId，可多选"}}, "idSet": {"uniqueItems": true, "type": "array", "description": "声音ID，可多选", "items": {"type": "string", "description": "声音ID，可多选"}}, "dataIdSet": {"uniqueItems": true, "type": "array", "description": "数据唯一标识(原文id)，可多选", "items": {"type": "string", "description": "数据唯一标识(原文id)，可多选"}}, "keywords": {"uniqueItems": true, "type": "array", "description": "关键词，可多选", "items": {"type": "string", "description": "关键词，可多选"}}, "cjTagFirstCodeSet": {"uniqueItems": true, "type": "array", "description": "全旅程客户标签编码1级，可多选", "items": {"type": "string", "description": "全旅程客户标签编码1级，可多选"}}, "cjTagSecondCodeSet": {"uniqueItems": true, "type": "array", "description": "全旅程客户标签编码2级，可多选", "items": {"type": "string", "description": "全旅程客户标签编码2级，可多选"}}, "cjTagThreeCodeSet": {"uniqueItems": true, "type": "array", "description": "全旅程客户标签编码3级，可多选", "items": {"type": "string", "description": "全旅程客户标签编码3级，可多选"}}, "adbTagFirstCodeSet": {"uniqueItems": true, "type": "array", "description": "全领域业务标签编码1级，可多选", "items": {"type": "string", "description": "全领域业务标签编码1级，可多选"}}, "adbTagSecondCodeSet": {"uniqueItems": true, "type": "array", "description": "全领域业务标签编码2级，可多选", "items": {"type": "string", "description": "全领域业务标签编码2级，可多选"}}, "adbTagThreeCodeSet": {"uniqueItems": true, "type": "array", "description": "全领域业务标签编码3级，可多选", "items": {"type": "string", "description": "全领域业务标签编码3级，可多选"}}, "comTagFirstCodeSet": {"uniqueItems": true, "type": "array", "description": "商品化属性标签编码1级，可多选", "items": {"type": "string", "description": "商品化属性标签编码1级，可多选"}}, "comTagSecondCodeSet": {"uniqueItems": true, "type": "array", "description": "商品化属性标签编码2级，可多选", "items": {"type": "string", "description": "商品化属性标签编码2级，可多选"}}, "comTagThreeCodeSet": {"uniqueItems": true, "type": "array", "description": "商品化属性标签编码3级，可多选", "items": {"type": "string", "description": "商品化属性标签编码3级，可多选"}}, "vtrTagFirstCodeSet": {"uniqueItems": true, "type": "array", "description": "VRT标签编码1级，可多选", "items": {"type": "string", "description": "VRT标签编码1级，可多选"}}, "vtrTagSecondCodeSet": {"uniqueItems": true, "type": "array", "description": "VRT标签编码2级，可多选", "items": {"type": "string", "description": "VRT标签编码2级，可多选"}}, "vtrTagThreeCodeSet": {"uniqueItems": true, "type": "array", "description": "VRT标签编码3级，可多选", "items": {"type": "string", "description": "VRT标签编码3级，可多选"}}}, "description": "扩展公共查询条件Model"}, "ResultListWordCloudVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "array", "description": "返回数据对象", "items": {"$ref": "#/components/schemas/WordCloudVo"}}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "WordCloudVo": {"type": "object", "properties": {"mentions": {"type": "integer", "description": "提及量", "format": "int64"}, "hotWordName": {"type": "string", "description": "热词名称"}, "positiveMentions": {"type": "integer", "description": "正面提及量", "format": "int64"}, "neutralMentions": {"type": "integer", "description": "中性提及量", "format": "int64"}, "negativeMentions": {"type": "integer", "description": "负面提及量", "format": "int64"}}, "description": "词云图VO"}, "ResultListVocExperienceTrendVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "array", "description": "返回数据对象", "items": {"$ref": "#/components/schemas/VocExperienceTrendVo"}}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "VocExperienceTrendVo": {"type": "object", "properties": {"date": {"type": "string", "description": "日期"}, "totalMentions": {"type": "integer", "description": "总提及量", "format": "int64"}, "positiveMentions": {"type": "integer", "description": "正面提及量", "format": "int64"}, "neutralMentions": {"type": "integer", "description": "中性提及量", "format": "int64"}, "negativeMentions": {"type": "integer", "description": "负面提及量", "format": "int64"}, "experienceValue": {"type": "number", "description": "体验值"}, "experienceValueMoM": {"type": "number", "description": "体验值环比"}}, "description": "VOC体验值趋势VO"}, "ResultListTopIssuesVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "array", "description": "返回数据对象", "items": {"$ref": "#/components/schemas/TopIssuesVo"}}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "TopIssuesVo": {"type": "object", "properties": {"keyword": {"type": "string", "description": "关键词"}, "sentiment": {"type": "string", "description": "情感"}, "mentions": {"type": "integer", "description": "提及量", "format": "int64"}, "mentionsChange": {"type": "integer", "description": "提及量变化", "format": "int64"}, "mentionsMoM": {"type": "number", "description": "提及量环比"}, "mentionRate": {"type": "number", "description": "提及率"}}, "description": "全旅程top问题VO"}, "RegionalAnalysisVo": {"type": "object", "properties": {"provinceName": {"type": "string", "description": "省份名称"}, "mentions": {"type": "integer", "description": "提及量", "format": "int64"}, "mentionsMoM": {"type": "number", "description": "提及量环比"}, "experienceValue": {"type": "number", "description": "体验值"}, "experienceValueMoM": {"type": "number", "description": "体验值环比"}}, "description": "地域分析VO"}, "ResultListRegionalAnalysisVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "array", "description": "返回数据对象", "items": {"$ref": "#/components/schemas/RegionalAnalysisVo"}}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "MentionTrendVo": {"type": "object", "properties": {"mentions": {"type": "integer", "description": "提及量", "format": "int64"}, "experienceValue": {"type": "number", "description": "体验值"}, "date": {"type": "string", "description": "日期"}, "positiveMentions": {"type": "integer", "description": "正面提及量", "format": "int64"}, "neutralMentions": {"type": "integer", "description": "中性提及量", "format": "int64"}, "negativeMentions": {"type": "integer", "description": "负面提及量", "format": "int64"}}, "description": "提及量趋势VO"}, "ResultListMentionTrendVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "array", "description": "返回数据对象", "items": {"$ref": "#/components/schemas/MentionTrendVo"}}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "AgeDistributionVo": {"type": "object", "properties": {"ageRange": {"type": "string", "description": "年龄段"}, "proportion": {"type": "number", "description": "占比"}}, "description": "年龄分布VO"}, "CarAgeDistributionVo": {"type": "object", "properties": {"carAge": {"type": "string", "description": "车龄"}, "proportion": {"type": "number", "description": "占比"}}, "description": "车龄分布VO"}, "CustomerTypeDistributionVo": {"type": "object", "properties": {"customerType": {"type": "string", "description": "客户类型"}, "proportion": {"type": "number", "description": "占比"}}, "description": "客户类型分布VO"}, "DemographicsVo": {"type": "object", "properties": {"totalUsers": {"type": "integer", "description": "人群总人数", "format": "int64"}, "ageDistributions": {"type": "array", "description": "年龄段占比top5", "items": {"$ref": "#/components/schemas/AgeDistributionVo"}}, "carAgeDistributions": {"type": "array", "description": "最近一次购车车龄占比top5", "items": {"$ref": "#/components/schemas/CarAgeDistributionVo"}}, "customerTypeDistributions": {"type": "array", "description": "客户分类占比top5", "items": {"$ref": "#/components/schemas/CustomerTypeDistributionVo"}}, "provinceDistributions": {"type": "array", "description": "客户常驻所在省份占比top5", "items": {"$ref": "#/components/schemas/ProvinceDistributionVo"}}, "educationDistributions": {"type": "array", "description": "最高学历占比top5", "items": {"$ref": "#/components/schemas/EducationDistributionVo"}}, "genderDistributions": {"type": "array", "description": "性别占比", "items": {"$ref": "#/components/schemas/GenderDistributionVo"}}}, "description": "人群特征VO"}, "EducationDistributionVo": {"type": "object", "properties": {"education": {"type": "string", "description": "学历"}, "proportion": {"type": "number", "description": "占比"}}, "description": "学历分布VO"}, "GenderDistributionVo": {"type": "object", "properties": {"gender": {"type": "string", "description": "性别"}, "proportion": {"type": "number", "description": "占比"}}, "description": "性别分布VO"}, "ProvinceDistributionVo": {"type": "object", "properties": {"provinceName": {"type": "string", "description": "省份名称"}, "proportion": {"type": "number", "description": "占比"}}, "description": "省份分布VO"}, "ResultDemographicsVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/DemographicsVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "DataSourceVo": {"type": "object", "properties": {"mentions": {"type": "integer", "description": "提及量", "format": "int64"}, "dataSourceName": {"type": "string", "description": "数据源名称(渠道名称)"}, "dataSourceCode": {"type": "string", "description": "数据源编码(渠道编码)"}, "positiveMentions": {"type": "integer", "description": "正面提及量", "format": "int64"}, "neutralMentions": {"type": "integer", "description": "中性提及量", "format": "int64"}, "negativeMentions": {"type": "integer", "description": "负面提及量", "format": "int64"}}, "description": "数据来源VO"}, "ResultListDataSourceVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "array", "description": "返回数据对象", "items": {"$ref": "#/components/schemas/DataSourceVo"}}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "CustomerJourneyExperienceVo": {"type": "object", "properties": {"customerTagExperienceList": {"type": "array", "description": "全旅程客户标签体验值列表", "items": {"$ref": "#/components/schemas/CustomerTagExperienceVo"}}, "totalExperienceValue": {"type": "number", "description": "体验值(所有标签的)"}, "totalExperienceValueMoM": {"type": "number", "description": "体验值环比(所有标签的)"}, "totalMentions": {"type": "integer", "description": "提及量(所有标签的)", "format": "int64"}, "totalMentionsMoM": {"type": "number", "description": "提及量环比(所有标签的)"}}, "description": "全旅程客户体验值VO"}, "CustomerTagExperienceVo": {"type": "object", "properties": {"customerTagName": {"type": "string", "description": "全旅程客户标签名称"}, "experienceValue": {"type": "number", "description": "全旅程客户标签体验值"}, "positiveMentions": {"type": "integer", "description": "正面提及量", "format": "int64"}, "neutralMentions": {"type": "integer", "description": "中性提及量", "format": "int64"}, "negativeMentions": {"type": "integer", "description": "负面提及量", "format": "int64"}, "totalMentions": {"type": "integer", "description": "总提及量", "format": "int64"}}, "description": "客户标签体验值VO"}, "ResultCustomerJourneyExperienceVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/CustomerJourneyExperienceVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "AccountModel": {"type": "object", "properties": {"id": {"type": "string"}, "userId": {"type": "string"}, "identityType": {"type": "string"}, "identifier": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "expireDate": {"type": "string", "format": "date-time"}, "startExpireDate": {"type": "string", "format": "date-time"}, "operator": {"type": "string"}, "appId": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}, "admin": {"type": "boolean"}, "nonExpired": {"type": "boolean"}, "nonLocked": {"type": "boolean"}, "enabled": {"type": "boolean"}, "loginCounts": {"type": "integer", "format": "int64"}, "lastLoginTime": {"type": "string", "format": "date-time"}}}, "UserModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "pageNum": {"type": "integer", "format": "int32"}, "order": {"type": "string"}, "id": {"type": "string"}, "userId": {"type": "string"}, "firstname": {"type": "string"}, "lastname": {"type": "string"}, "email": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "expireDate": {"type": "string", "format": "date-time"}, "startExpireDate": {"type": "string", "format": "date-time"}, "tokenKey": {"type": "string"}, "operator": {"type": "string"}, "nonExpired": {"type": "boolean"}, "enabled": {"type": "boolean"}, "status": {"type": "string"}, "phone": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}, "smscode": {"type": "string"}, "captcha": {"type": "string"}, "checkKey": {"type": "string"}, "uniconId": {"type": "string"}, "appId": {"type": "string"}, "admin": {"type": "string"}, "clientId": {"type": "string"}, "type": {"type": "string"}, "labelstudToken": {"type": "string"}, "nonLocked": {"type": "boolean"}, "loginTime": {"type": "string", "format": "date-time"}, "accounts": {"type": "array", "items": {"$ref": "#/components/schemas/AccountModel"}}, "clientIds": {"type": "array", "items": {"type": "string"}}, "employeeId": {"type": "string"}, "position": {"type": "string"}, "remark": {"type": "string"}, "officePhone": {"type": "string", "description": "办公电话"}, "homePhone": {"type": "string", "description": "家庭电话"}, "userIds": {"type": "array", "items": {"type": "string"}}, "userNameList": {"type": "array", "items": {"type": "string"}}}}, "BaseLoginModel": {"type": "object", "properties": {"username": {"type": "string", "description": "登陆账号", "example": "admin"}, "password": {"type": "string", "description": "登陆口令", "example": "Passw0rd@!"}, "captcha": {"type": "string", "description": "验证码", "example": "2587"}, "checkKey": {"type": "string", "description": "验证码key", "example": "123"}}}, "AuthenticationResponse": {"type": "object", "properties": {"appId": {"type": "string", "description": "登陆账号标识", "example": "voc"}, "type": {"type": "string", "description": "登陆账号标识", "example": "base"}, "username": {"type": "string", "description": "登陆账号", "example": "admin"}, "userid": {"type": "string", "description": "登陆账号标识", "example": "1"}, "access_token": {"type": "string", "description": "登陆账号TOKEN", "example": "eyJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoiMSIsImlkZW50aXR5X3R5cGUiOiJiYXNlIiwiYXBwX2lkIjoiaW5zaWdodHMiLCJ1c2VybmFtZSI6IklDeTMvdUhmMGJUMDdoUVFYaUFPd1hSUEY2cC9nbEJWd0NTWno2MUlhSC9LNUIvdDRzc29jeEI2dEpoUWhRZEYiLCJzdWIiOiIxIiwiaWF0IjoxNzA5NTQwNjc4LCJleHAiOjE3MTIxMzI2Nzh9.zMidSM2L7wD4NFWqwwjJ5XdjUT5jo7GwqeccSsYAt2c"}}, "description": "返回数据对象"}, "ResultAuthenticationResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/AuthenticationResponse"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "InsReportAccountInfoModel": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "pageNum": {"type": "integer", "format": "int32"}, "order": {"type": "string"}, "userId": {"type": "string", "description": "用户id"}, "deptId": {"type": "array", "description": "部门id", "items": {"type": "string", "description": "部门id"}}, "roleId": {"type": "string", "description": "角色ID"}, "employeeId": {"type": "string", "description": "员工编号"}, "accountName": {"type": "string", "description": "账号名称"}, "accountPwd": {"type": "string", "description": "账号密码"}, "userName": {"type": "string", "description": "用户名"}, "contact": {"type": "string", "description": "联系方式"}, "position": {"type": "string", "description": "职位"}, "email": {"type": "string", "description": "邮箱"}, "remark": {"type": "string", "description": "备注"}, "status": {"type": "string", "description": "停用/启用状态 停用:0 启用:1 默认启用"}, "clientId": {"type": "string", "description": "客户id"}, "enable": {"type": "string"}, "loginType": {"type": "string", "description": "登录类型 表单:base 邮箱:email 默认为表单类型"}}}, "ResultVoid": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "object", "description": "返回数据对象"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "ResultListStaSysDepartModel": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "array", "description": "返回数据对象", "items": {"$ref": "#/components/schemas/StaSysDepartModel"}}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "StaSysDepartModel": {"type": "object", "properties": {"name": {"type": "string", "description": "部门名称"}, "value": {"type": "string", "description": "部门值"}}, "description": "部门信息模型"}, "InsReportAccountInfoVo": {"type": "object", "properties": {"userId": {"type": "string", "description": "用户id"}, "accountName": {"type": "string", "description": "账号名称"}, "userName": {"type": "string", "description": "用户名"}, "employeeId": {"type": "string", "description": "员工编号"}, "deptName": {"type": "string", "description": "部门名称"}, "deptId": {"type": "string", "description": "部门id"}, "roleName": {"type": "string", "description": "角色名称"}, "roleId": {"type": "string", "description": "角色id"}, "status": {"type": "string", "description": "停用/启用状态 停用:0 启用:1 默认启用"}, "loginCounts": {"type": "integer", "description": "登录次数", "format": "int64"}, "lastLoginTime": {"type": "string", "description": "最后一次登录时间", "format": "date-time"}, "contact": {"type": "string", "description": "联系方式"}, "position": {"type": "string", "description": "职位"}, "email": {"type": "string", "description": "邮箱"}, "remark": {"type": "string", "description": "备注"}, "officePhone": {"type": "string", "description": "办公电话"}, "homePhone": {"type": "string", "description": "家庭电话"}, "phone": {"type": "string"}}, "description": "返回数据对象"}, "ResultInsReportAccountInfoVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/InsReportAccountInfoVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "PageInfoInsReportAccountInfoVo": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/InsReportAccountInfoVo"}}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int64"}, "endRow": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "nextPage": {"type": "integer", "format": "int32"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}}, "description": "返回数据对象"}, "ResultPageInfoInsReportAccountInfoVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/PageInfoInsReportAccountInfoVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "ResultVocSoundsVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/VocSoundsVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "VocSoundsVo": {"type": "object", "properties": {"id": {"type": "string", "description": "声音ID"}, "dataId": {"type": "string", "description": "数据唯一标识"}, "channelCode": {"type": "string", "description": "渠道编码"}, "channel": {"type": "string", "description": "渠道名称"}, "brandCode": {"type": "string", "description": "品牌编码"}, "brand": {"type": "string", "description": "品牌名称"}, "vehicleSeriesCode": {"type": "string", "description": "车系编码"}, "vehicleSeries": {"type": "string", "description": "车系名称"}, "vehicleModelCode": {"type": "string", "description": "车型编码"}, "vehicleModel": {"type": "string", "description": "车型名称"}, "labelType": {"type": "string", "description": "数据类型"}, "sentiment": {"type": "string", "description": "情感"}, "intention": {"type": "string", "description": "意图"}, "hotWord": {"type": "string", "description": "热词"}, "userJourney": {"type": "string", "description": "用户旅程"}, "keywords": {"type": "string", "description": "关键词"}, "dataCreateTime": {"type": "string", "description": "数据产生时间", "format": "date-time"}, "createTime": {"type": "string", "description": "数据抓取时间", "format": "date-time"}, "dealerId": {"type": "integer", "description": "经销商ID", "format": "int64"}, "dealerCode": {"type": "string", "description": "经销商编码"}, "dealerName": {"type": "string", "description": "经销商全称"}, "dealerProvinceCode": {"type": "string", "description": "经销商所在省编码"}, "dealerProvince": {"type": "string", "description": "经销商所在省"}, "dealerRegionalCode": {"type": "string", "description": "经销商所在大区编码"}, "dealerRegional": {"type": "string", "description": "经销商所在大区"}, "dealerCityCode": {"type": "string", "description": "经销商所在市编码"}, "dealerCity": {"type": "string", "description": "经销商所在市"}, "vehiclePurchaseDate": {"type": "string", "description": "车辆购买日期", "format": "date"}, "vehicleProductionDate": {"type": "string", "description": "车辆生产日期", "format": "date"}, "vehicleFactoryReleaseDate": {"type": "string", "description": "车辆出厂日期", "format": "date"}, "vehicleVin": {"type": "string", "description": "车辆车架号"}, "oneId": {"type": "string", "description": "oneId"}, "custName": {"type": "string", "description": "客户姓名"}, "custMobile": {"type": "string", "description": "客户手机号"}, "custAge": {"type": "integer", "description": "客户年龄", "format": "int32"}, "custGender": {"type": "string", "description": "客户性别"}, "custProvinceCode": {"type": "string", "description": "客户常驻省份编码"}, "custProvince": {"type": "string", "description": "客户常驻省份"}, "custCityCode": {"type": "string", "description": "客户常驻市编码"}, "custCity": {"type": "string", "description": "客户常驻市"}, "custDistrictCode": {"type": "string", "description": "客户常驻区编码"}, "custDistrict": {"type": "string", "description": "客户常驻区"}, "custHighestEdu": {"type": "string", "description": "客户最高学历"}, "custMonthlyIncome": {"type": "string", "description": "客户月收入"}, "custLastPurchaseDate": {"type": "string", "description": "客户最近一次购车时间", "format": "date"}, "custType": {"type": "string", "description": "客户类型"}, "isVehicleOwner": {"type": "boolean", "description": "是否车主"}, "contentType": {"type": "string", "description": "原文内容类型"}, "workOrderId": {"type": "integer", "description": "工单ID", "format": "int64"}, "isMainPost": {"type": "boolean", "description": "是否主贴"}, "postTitle": {"type": "string", "description": "帖子标题"}, "postOriginalLink": {"type": "string", "description": "帖子原文链接"}, "postOriginalContent": {"type": "string", "description": "帖子原文详情"}, "questType": {"type": "string", "description": "问卷类型（问卷类型拥有）"}, "questQuestionContent": {"type": "string", "description": "问卷题目/内容（问卷类型拥有）"}, "questAnswerScore": {"type": "integer", "description": "问卷答案分数（问卷类型拥有）", "format": "int32"}, "questBusinessType": {"type": "string", "description": "问卷业务类型（问卷类型拥有）"}, "questBusinessScenario": {"type": "string", "description": "问卷业务场景（问卷类型拥有）"}, "topic": {"type": "string", "description": "观点"}, "vtrTagFirstCode": {"type": "string", "description": "VRT标签编码1级"}, "vtrTagSecondCode": {"type": "string", "description": "VRT标签编码2级"}, "vtrTagThreeCode": {"type": "string", "description": "VRT标签编码3级"}, "vtrTagFirst": {"type": "string", "description": "VRT标签1级"}, "vtrTagSecond": {"type": "string", "description": "VRT标签2级"}, "vtrTagThree": {"type": "string", "description": "VRT标签3级"}, "comTagFirstCode": {"type": "string", "description": "商品化属性标签编码1级"}, "comTagSecondCode": {"type": "string", "description": "商品化属性标签编码2级"}, "comTagThreeCode": {"type": "string", "description": "商品化属性标签编码3级"}, "comTagFirst": {"type": "string", "description": "商品化属性标签1级"}, "comTagSecond": {"type": "string", "description": "商品化属性标签2级"}, "comTagThree": {"type": "string", "description": "商品化属性标签3级"}, "adbTagFirstCode": {"type": "string", "description": "全领域业务标签编码1级"}, "adbTagSecondCode": {"type": "string", "description": "全领域业务标签编码2级"}, "adbTagThreeCode": {"type": "string", "description": "全领域业务标签编码3级"}, "adbTagFirst": {"type": "string", "description": "全领域业务标签1级"}, "adbTagSecond": {"type": "string", "description": "全领域业务标签2级"}, "adbTagThree": {"type": "string", "description": "全领域业务标签3级"}, "womTagFirstCode": {"type": "string", "description": "口碑评价指标编码1级"}, "womTagSecondCode": {"type": "string", "description": "口碑评价指标编码2级"}, "womTagThreeCode": {"type": "string", "description": "口碑评价指标编码3级"}, "womTagFirst": {"type": "string", "description": "口碑评价指标1级"}, "womTagSecond": {"type": "string", "description": "口碑评价指标2级"}, "womTagThree": {"type": "string", "description": "口碑评价指标3级"}, "cxTagFirstCode": {"type": "string", "description": "客户体验指标编码1级"}, "cxTagSecondCode": {"type": "string", "description": "客户体验指标编码2级"}, "cxTagThreeCode": {"type": "string", "description": "客户体验指标编码3级"}, "cxTagFirst": {"type": "string", "description": "客户体验指标1级"}, "cxTagSecond": {"type": "string", "description": "客户体验指标2级"}, "cxTagThree": {"type": "string", "description": "客户体验指标3级"}, "cjTagFirstCode": {"type": "string", "description": "全旅程客户签编码1级"}, "cjTagSecondCode": {"type": "string", "description": "全旅程客户签编码2级"}, "cjTagThreeCode": {"type": "string", "description": "全旅程客户签编码3级"}, "cjTagFirst": {"type": "string", "description": "全旅程客户签1级"}, "cjTagSecond": {"type": "string", "description": "全旅程客户签2级"}, "cjTagThree": {"type": "string", "description": "全旅程客户签3级"}, "slTagFirstCode": {"type": "string", "description": "销售线索编码1级"}, "slTagSecondCode": {"type": "string", "description": "销售线索编码2级"}, "slTagThreeCode": {"type": "string", "description": "销售线索编码3级"}, "slTagFirst": {"type": "string", "description": "销售线索1级"}, "slTagSecond": {"type": "string", "description": "销售线索2级"}, "slTagThree": {"type": "string", "description": "销售线索3级"}, "omTagFirstCode": {"type": "string", "description": "全媒体指标编码1级"}, "omTagSecondCode": {"type": "string", "description": "全媒体指标编码2级"}, "omTagThreeCode": {"type": "string", "description": "全媒体指标编码3级"}, "omTagFirst": {"type": "string", "description": "全媒体指标1级"}, "omTagSecond": {"type": "string", "description": "全媒体指标2级"}, "omTagThree": {"type": "string", "description": "全媒体指标3级"}, "dataCreateWeek": {"type": "string", "description": "数据产生周期-周"}, "dataCreateMonth": {"type": "string", "description": "数据产生周期-月"}, "dataCreateQuarter": {"type": "string", "description": "数据产生周期-季"}, "dataCreateYear": {"type": "string", "description": "数据产生周期-年"}}, "description": "VOC声音数据VO"}, "ResultString": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "string", "description": "返回数据对象"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "ResultLong": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "integer", "description": "返回数据对象", "format": "int64"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "ResultListDictItemListVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"type": "array", "description": "返回数据对象", "items": {"$ref": "#/components/schemas/DictItemListVo"}}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "DictItemDetailVo": {"type": "object", "properties": {"id": {"type": "string", "description": "字典项ID"}, "dictId": {"type": "string", "description": "字典ID"}, "itemText": {"type": "string", "description": "字典项文本"}, "itemTextEn": {"type": "string", "description": "字典项英文文本"}, "itemKey": {"type": "string", "description": "字典项键"}, "itemValue": {"type": "string", "description": "字典项值"}, "description": {"type": "string", "description": "描述"}, "sortOrder": {"type": "integer", "description": "排序", "format": "int32"}, "status": {"type": "integer", "description": "状态（1启用 0不启用）", "format": "int32"}, "operator": {"type": "string", "description": "创建人"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}}, "description": "数据字典项详情VO"}, "ResultDictItemDetailVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/DictItemDetailVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "DictDetailVo": {"type": "object", "properties": {"id": {"type": "string", "description": "字典ID"}, "dictName": {"type": "string", "description": "字典名称"}, "dictCode": {"type": "string", "description": "字典编码"}, "type": {"type": "integer", "description": "字典类型", "format": "int32"}, "description": {"type": "string", "description": "描述"}, "operator": {"type": "string", "description": "创建人"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time"}, "dictItems": {"type": "array", "description": "字典项列表", "items": {"$ref": "#/components/schemas/DictItemDetailVo"}}}, "description": "数据字典详情VO"}, "ResultDictDetailVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"$ref": "#/components/schemas/DictDetailVo"}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}, "ResultSetConditionVo": {"type": "object", "properties": {"success": {"type": "boolean", "description": "成功标志", "example": true}, "message": {"type": "string", "description": "返回处理消息", "example": "操作成功！"}, "code": {"type": "string", "description": "返回代码", "example": "200"}, "result": {"uniqueItems": true, "type": "array", "description": "返回数据对象", "items": {"$ref": "#/components/schemas/ConditionVo"}}, "tid": {"type": "string", "description": "请求标识", "example": "634b859b92854c9ea8dd6c8c06c32aa7.134.*****************"}}}}}, "x-openapi": {"x-setting": {"customCode": 200, "language": "zh-CN", "enableSwaggerModels": true, "swaggerModelName": "实体类列表", "enableReloadCacheParameter": false, "enableAfterScript": true, "enableDocumentManage": true, "enableVersion": false, "enableRequestCache": true, "enableFilterMultipartApis": false, "enableFilterMultipartApiMethodType": "POST", "enableHost": false, "enableHostText": "", "enableDynamicParameter": true, "enableDebug": true, "enableFooter": true, "enableFooterCustom": false, "enableSearch": true, "enableOpenApi": true, "enableHomeCustom": false, "enableGroup": true, "enableResponseCode": true}, "x-markdownFiles": []}}