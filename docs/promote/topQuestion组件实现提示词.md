# Top问题组件实现提示词

## 实现目标

在其他系统中实现一个功能完整的TOP问题排行榜组件，支持数据展示、筛选、排序、分页、导出等功能。

## 技术栈选择

### 推荐技术栈

- **前端框架**: Vue.js 2.x / 3.x 或 React
- **UI组件库**: Element UI / Ant Design / Material-UI
- **状态管理**: Vuex / Redux / Pinia
- **HTTP客户端**: Axios / Fetch
- **构建工具**: Vue CLI / Vite / Webpack

### 替代方案

- **纯原生**: HTML + CSS + JavaScript
- **其他框架**: Angular / Svelte
- **移动端**: React Native / Flutter

## 核心功能实现

### 1. 组件结构设计

```vue
<!-- Vue.js 实现 -->
<template>
  <div class="top-question-container">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-left">
        <span class="filter-label">情感筛选</span>
        <select v-model="selectedEmotion" @change="onEmotionChange">
          <option value="all">全部</option>
          <option value="positive">正面</option>
          <option value="negative">负面</option>
          <option value="neutral">中性</option>
        </select>
      </div>

      <div class="filter-right">
        <select v-model="pageSize" @change="onPageSizeChange">
          <option value="10">10条</option>
          <option value="20">20条</option>
          <option value="50">50条</option>
        </select>

        <select v-model="sortType" @change="onSortChange">
          <option value="mention">按提及量排序</option>
          <option value="rate">按提及率排序</option>
        </select>
      </div>
    </div>

    <!-- 数据表格（el-table重构） -->
    <el-table
      :data="displayData"
      border
      style="width: 100%"
      :header-cell-style="{ background: '#fafafa', fontWeight: 500, color: '#333' }"
    >
      <el-table-column label="排名" width="60" align="center">
        <template #default="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="标准关键词" min-width="120">
        <template #default="scope">
          <span class="keyword" @click="onKeywordClick(scope.row.keyword)">
            {{ scope.row.keyword }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="情感" width="80" align="center">
        <template #default="scope">
          <span :class="getEmotionClass(scope.row.emotionAttribute)">
            {{ getEmotionLabel(scope.row.emotionAttribute) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="提及量" min-width="100" align="center">
        <template #default="scope">
          <div class="mention-value">{{ formatNumber(scope.row.totalMentionValue) }}</div>
          <div class="mention-change" :class="getChangeClass(scope.row.momTotalMentionValue)">
            {{ formatChange(scope.row.momTotalMentionValue) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="提及量环比" min-width="100" align="center">
        <template #default="scope">
          <span :class="getRateClass(scope.row.momTotalMentionValueRate)">
            {{ formatRate(scope.row.momTotalMentionValueRate) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="提及率" min-width="80" align="center">
        <template #default="scope"> {{ formatPercent(scope.row.mentionRate) }}% </template>
      </el-table-column>
    </el-table>

    <!-- 导出按钮 -->
    <div class="export-section">
      <button @click="exportData">导出Excel</button>
    </div>
  </div>
</template>
```

### 2. 数据处理逻辑

```javascript
export default {
  name: 'TopQuestion',

  props: {
    // 数据源
    data: {
      type: Array,
      default: () => []
    },
    // 配置参数
    config: {
      type: Object,
      default: () => ({})
    },
    // 是否显示标题
    showTitle: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      selectedEmotion: 'all',
      pageSize: 10,
      sortType: 'mention',
      loading: false
    }
  },

  computed: {
    // 过滤后的数据
    filteredData() {
      if (this.selectedEmotion === 'all') {
        return this.data
      }
      return this.data.filter(item => item.emotionAttribute === this.selectedEmotion)
    },

    // 排序后的数据
    sortedData() {
      const data = [...this.filteredData]

      if (this.sortType === 'mention') {
        return data.sort((a, b) => b.totalMentionValue - a.totalMentionValue)
      } else if (this.sortType === 'rate') {
        return data.sort((a, b) => b.mentionRate - a.mentionRate)
      }

      return data
    },

    // 分页显示数据
    displayData() {
      return this.sortedData.slice(0, this.pageSize)
    }
  },

  methods: {
    // 情感筛选变化
    onEmotionChange() {
      this.$emit('filter-change', {
        emotion: this.selectedEmotion,
        sortType: this.sortType
      })
    },

    // 分页大小变化
    onPageSizeChange() {
      this.$emit('page-size-change', this.pageSize)
    },

    // 排序方式变化
    onSortChange() {
      this.$emit('sort-change', {
        emotion: this.selectedEmotion,
        sortType: this.sortType
      })
    },

    // 关键词点击
    onKeywordClick(keyword) {
      this.$emit('keyword-click', keyword)
    },

    // 导出数据
    exportData() {
      this.$emit('export', {
        type: 'topQuestion',
        data: this.displayData
      })
    },

    // 格式化数字
    formatNumber(num) {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万'
      }
      return num.toString()
    },

    // 格式化变化值
    formatChange(change) {
      if (change > 0) {
        return '+' + this.formatNumber(change)
      } else if (change < 0) {
        return '-' + this.formatNumber(Math.abs(change))
      }
      return '-'
    },

    // 格式化比率
    formatRate(rate) {
      return (rate * 100).toFixed(1) + '%'
    },

    // 格式化百分比
    formatPercent(percent) {
      return (percent * 100).toFixed(2)
    },

    // 获取情感样式类
    getEmotionClass(emotion) {
      const classes = {
        positive: 'emotion-positive',
        negative: 'emotion-negative',
        neutral: 'emotion-neutral'
      }
      return classes[emotion] || 'emotion-neutral'
    },

    // 获取情感标签
    getEmotionLabel(emotion) {
      const labels = {
        positive: '正面',
        negative: '负面',
        neutral: '中性'
      }
      return labels[emotion] || '中性'
    },

    // 获取变化样式类
    getChangeClass(change) {
      if (change > 0) return 'change-positive'
      if (change < 0) return 'change-negative'
      return 'change-neutral'
    },

    // 获取比率样式类
    getRateClass(rate) {
      if (rate > 0) return 'rate-positive'
      if (rate < 0) return 'rate-negative'
      return 'rate-neutral'
    }
  }
}
```

### 3. 样式设计

```css
/* 容器样式 */
.top-question-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

/* 筛选区域 */
.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px 0;
}

.filter-left,
.filter-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* 表格样式 */
.table-container {
  margin-top: 16px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #e8e8e8;
}

.data-table th,
.data-table td {
  padding: 12px 8px;
  text-align: left;
  border-bottom: 1px solid #e8e8e8;
  height: 60px;
  vertical-align: middle;
}

.data-table th {
  background: #fafafa;
  font-weight: 500;
  color: #333;
}

.sub-header {
  font-size: 12px;
  color: #666;
  font-weight: normal;
}

/* 排名样式 */
.rank {
  text-align: center;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

/* 关键词样式 */
.keyword {
  color: #0077ff;
  cursor: pointer;
  font-size: 14px;
  line-height: 22px;
}

.keyword:hover {
  text-decoration: underline;
}

/* 情感标签样式 */
.emotion {
  text-align: center;
}

.emotion-positive {
  background: #f6ffed;
  color: #52c41a;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.emotion-negative {
  background: #fff2f0;
  color: #ff4d4f;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.emotion-neutral {
  background: #f5f5f5;
  color: #666;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

/* 提及量样式 */
.mention {
  text-align: center;
}

.mention-value {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  line-height: 22px;
}

.mention-change {
  font-size: 14px;
  line-height: 22px;
  margin-top: 4px;
}

.change-positive {
  color: #52c41a;
}

.change-negative {
  color: #ff4d4f;
}

.change-neutral {
  color: #666;
}

/* 比率样式 */
.mention-rate {
  text-align: center;
}

.rate-positive {
  color: #52c41a;
}

.rate-negative {
  color: #ff4d4f;
}

.rate-neutral {
  color: #666;
}

.rate {
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

/* 导出按钮 */
.export-section {
  margin-top: 20px;
  text-align: right;
}

.export-section button {
  background: #0077ff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.export-section button:hover {
  background: #0056cc;
}
```

## 数据接口设计

### 1. 数据格式规范

```javascript
// 输入数据格式
const questionData = [
  {
    id: 'q001',
    keyword: '发动机异响',
    emotionAttribute: 'negative',
    totalMentionValue: 1234,
    momTotalMentionValue: 123,
    momTotalMentionValueRate: 0.1,
    mentionRate: 0.05,
    category: '质量问题',
    priority: 'high'
  }
]

// 配置参数格式
const config = {
  showTitle: true,
  title: 'TOP问题排行榜',
  pageSize: 10,
  sortType: 'mention',
  emotionFilter: 'all',
  exportEnabled: true,
  keywordClickEnabled: true
}
```

### 2. 事件接口设计

```javascript
// 组件事件
const events = {
  // 筛选条件变化
  'filter-change': filters => {
    // filters: { emotion, sortType }
  },

  // 分页大小变化
  'page-size-change': pageSize => {
    // pageSize: number
  },

  // 排序方式变化
  'sort-change': sortConfig => {
    // sortConfig: { emotion, sortType }
  },

  // 关键词点击
  'keyword-click': keyword => {
    // keyword: string
  },

  // 数据导出
  export: exportConfig => {
    // exportConfig: { type, data }
  }
}
```

## 推荐组件目录结构

```text
src/components/TopQuestion/
├── index.vue         # 主组件文件
├── types.d.ts        # 组件类型定义（如有）
└── index.ts          # 组件导出（可选，便于全局注册）
```

> 建议：如需复用，可将 TopQuestion 目录放入 src/components/，并按上述结构组织，便于维护和扩展。

## 总结

通过以上详细的实现指导，您可以在其他系统中成功实现一个功能完整、性能优化的TOP问题排行榜组件。关键要点包括：

1. **模块化设计**: 将功能拆分为独立的模块，便于维护和扩展
2. **数据驱动**: 通过props和事件实现组件间的数据通信
3. **性能优化**: 使用虚拟滚动、缓存机制等技术提升性能
4. **用户体验**: 提供丰富的交互功能和友好的界面设计
5. **可扩展性**: 支持自定义配置和功能扩展
6. **质量保证**: 完善的测试用例和错误处理机制

这个组件可以作为数据展示类组件的标准模板，适用于各种排行榜、数据列表等场景。
