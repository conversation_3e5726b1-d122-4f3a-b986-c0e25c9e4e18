# 布局系统设计规范

## 🎯 设计目标

实现一个现代化的后台管理系统布局，提供良好的用户体验和开发体验。

## 📐 布局结构

### 整体布局

```
┌─────────────┬───────────────────────────────────────────┐
│             │              Header (顶部导航栏)            │
│   Sidebar   ├───────────────────────────────────────────┤
│  (侧边栏)    │                                           │
│             │              Main Content                 │
│  - Logo     │              (主内容区)                   │
│  - Menu     │                                           │
│             │                                           │
└─────────────┴───────────────────────────────────────────┘
```

### 详细规格

#### 1. 侧边栏 (Sidebar)

- **位置**: 左侧固定
- **宽度**: 240px (可折叠至 64px)
- **背景色**: #212B36
- **内容结构**:
  - 顶部: Logo区域 (高度 60px)
  - 中间: 导航菜单 (自适应高度)

#### 2. 顶部导航栏 (Header)

- **位置**: 右侧顶部
- **高度**: 60px
- **背景色**: #212B36

#### 3. 主内容区 (Main Content)

- **位置**: 右侧底部 (<PERSON><PERSON>下方)
- **背景色**: #f5f5f5
- **内容**: 页面路由内容

## 🎨 视觉设计

### 颜色规范

```scss
// 主色调
$primary-color: #409eff;
$sidebar-bg: #212b36;
$header-bg: #212b36;
$content-bg: #f5f5f5;

// 文字颜色
$text-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;
$text-white: #ffffff;

// 边框颜色
$border-color: #dcdfe6;
$border-light: #e4e7ed;
```

## 🔧 技术实现

### 组件结构

```
src/layout/
├── index.vue              # 主布局组件
├── components/
│   ├── Sidebar.vue        # 侧边栏组件
│   ├── Header.vue         # 顶部导航组件
│   ├── Logo.vue           # Logo组件
│   └── Menu.vue           # 菜单组件
└── types.d.ts             # 布局相关类型定义
```

### 状态管理

- 侧边栏折叠状态
- 当前激活菜单

### 路由配置

- 使用嵌套路由实现布局
- 支持路由元信息配置菜单

## 🚀 实施计划

### 第一阶段：基础布局

1. 创建布局组件结构
2. 实现左侧侧边栏 (Logo + 菜单)
3. 实现右侧顶部Header
4. 实现右侧主内容区
5. 配置路由使用布局
6. 添加Logo资源

### 第二阶段：菜单系统

1. 实现菜单组件
2. 配置菜单数据
3. 添加菜单交互

### 第三阶段：功能完善

1. 添加折叠功能
2. 优化交互体验
3. 添加动画效果

## 📋 检查清单

### 基础功能

- [ ] 布局组件创建完成
- [ ] 侧边栏和顶部导航实现
- [ ] 路由配置使用布局
- [ ] Logo资源添加
- [ ] 基础样式实现

### 交互功能

- [ ] 菜单高亮显示
- [ ] 侧边栏折叠
