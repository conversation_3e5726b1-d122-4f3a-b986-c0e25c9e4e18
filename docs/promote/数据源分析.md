# 数据来源分析组件开发需求

## 参考组件

- 路径：docs/exampleCode/meicloud-changan-voc-main_v3/src/components/pages_components/businessComponents/analysis/datasourceAnalysis.vue

## 需求描述

请基于上述参考组件，实现一个"数据来源"可视化组件，具体要求如下：

### 1. 功能要求

- 展示"数据来源"主图（柱状+折线混合图），支持数据联动和详情查看。
- 展示当前选中数据源的"提及量趋势"图（柱状/折线图），支持点击查看详情。
- 展示当前数据源的"词云图"，支持点击词条触发事件。
- 支持下载当前图表数据。
- 支持数据源切换联动分析，自动高亮最大提及量数据源。

### 2. 交互要求

- 主图、趋势图、词云图均需支持点击事件，分别触发对应的详情或词条事件。
- 鼠标悬浮时，tooltip 显示详细数据说明（包括体验值、正/中/负面提及量、总提及量等）。

### 3. 数据处理

- 对传入数据进行排序、负值处理、最大值提取等预处理。
- 支持多种数据格式（data、remarkData、wordCloudData），并能根据数据变化自动刷新图表。

### 4. 可配置性

- 组件需支持外部传入 chartId、loading、data、remarkData、attr、attrName、preDivId 等参数，便于复用和扩展。

### 5. 样式与布局

- 采用左右两栏布局，标题高亮当前数据源，整体风格与参考组件保持一致。
- 支持响应式布局，适配不同屏幕宽度。

### 6. 技术要求

- 使用 Vue 3 + TypeScript + Composition API + <script setup> 语法。
- 图表部分优先复用项目现有的 ECharts 封装组件。
- 代码需包含详细注释，便于后续维护和二次开发。

### 7. 扩展建议

- 组件应预留扩展接口，便于后续增加更多分析维度或图表类型。

---

如需进一步细化每个功能点的实现细节或接口定义，请补充说明。

## 详细配置讲义

### 1. 组件依赖与复用

- **主图（柱状+散点/折线混合）**
  使用 `src/components/Charts/BarAndPointChart/index.vue` 组件，已封装专业 ECharts 图表，支持数据联动、详情点击等。
- **趋势图（柱状/折线图）**
  使用 `src/components/Charts/BarOrLineChart/index.vue` 组件，支持趋势数据展示与交互。
- **词云图**
  本项目暂无现成词云组件。建议新建 `WordCloudChart` 组件，后续可复用。可参考 ECharts 官方词云插件实现，或根据业务需求自定义。

### 2. 推荐目录结构

```
src/components/DataSourceAnalysis/
├── index.vue           # 主组件文件
├── WordCloudChart.vue  # 词云图子组件（如需新建）
├── types.d.ts          # TypeScript 类型定义
└── README.md           # 组件说明文档
```

### 3. 详细 Props 配置建议

| 参数       | 说明                    | 类型             | 是否必填 | 默认值 |
| ---------- | ----------------------- | ---------------- | -------- | ------ |
| chartId    | 图表唯一标识            | string           | 否       | ''     |
| loading    | 加载状态                | boolean          | 否       | false  |
| data       | 主图数据                | DataSourceItem[] | 是       | []     |
| remarkData | 趋势/词云等补充数据     | DataSourceRemark | 否       | {}     |
| attr       | 显示的属性              | string           | 否       | ''     |
| attrName   | 显示的属性名称          | string           | 否       | ''     |
| preDivId   | dom前缀，便于多实例区分 | string           | 否       | ''     |

#### 类型定义示例（types.d.ts）

```typescript
export interface DataSourceItem {
  dataSource: string
  totalMentionValue: number
  positiveMentionValue: number
  neutralMentionValue: number
  negativeMentionValue: number
}

export interface TrendItem {
  keyWord: string
  positiveMentionValue: number
  neutralMentionValue: number
  negativeMentionValue: number
}

export interface WordCloudItem {
  name: string
  value: number
}

export interface DataSourceRemark {
  trend?: TrendItem[]
  wordCloud?: WordCloudItem[]
}
```

### 4. 事件与交互

- `@download`：下载当前图表数据
- `@changeDatasource`：切换数据源时触发
- `@datasourceSeeDetail`：趋势图详情点击
- `@wordCloudChartClick`：词云词条点击

### 5. 组件说明文档（README.md）

- 说明各 props、事件、数据结构
- 提供典型用例代码
- 说明依赖的子组件（如 BarAndPointChart、BarOrLineChart、WordCloudChart）

### 6. 开发建议

- 主组件 `index.vue` 负责整体布局、数据流转、事件分发。
- 词云图建议单独封装为 `WordCloudChart.vue`，便于后续复用。
- 类型定义集中在 `types.d.ts`，便于类型检查和维护。
- 说明文档详细描述用法、参数、事件、示例。

如需词云图实现建议或详细代码模板，请随时告知！

# 接口联调

## 文件路径

- 主页面：src/views/home/<USER>
- API定义：src/api/common/index.ts
- 类型定义：src/api/common/index.d.ts
- mock文件：src/mock/index.ts
- 数据源分析: src/components/DataSourceAnalysis/index.vue

1. 在主页面封装函数，调用接口获取数据，数据为空或异常时使用mock数据

- getDataSourceAnalysis 获取数据来源
- getMentionTrend 获取提及量趋势
- getWordCloud 获取词云图

2. 数据迁移及mock数据

- 在主页面将dataSourceList、dataSourceRemark数据迁移至mock页面并命名dataSourceListMock、dataSourceRemarkMock
- 根据接口返回值重构mock数据

3. 更新DataSourceAnalysis组件中的props入参类型，及接口定义类型更新数据处理逻辑、字段

## 联调完成状态

✅ **已完成** (2025-01-27)

### 实现内容

1. **数据迁移**

   - ✅ 将原有硬编码数据迁移到mock文件中
   - ✅ 创建 `dataSourceListMock` 和 `dataSourceRemarkMock`

2. **接口封装**

   - ✅ 在主页面添加三个接口调用函数：
     - `fetchDataSourceAnalysis()` - 获取数据源列表
     - `fetchMentionTrend(dataSource)` - 获取指定数据源的提及量趋势
     - `fetchWordCloud(dataSource)` - 获取指定数据源的词云数据

3. **数据转换**

   - ✅ 实现数据转换函数，将接口返回数据转换为组件需要的格式：
     - `transformDataSourceList()` - 转换数据源列表
     - `transformMentionTrend()` - 转换趋势数据
     - `transformWordCloud()` - 转换词云数据

4. **错误处理**

   - ✅ 接口调用失败时自动回退到mock数据
   - ✅ 接口返回空数据时使用mock数据
   - ✅ 添加用户友好的错误提示

5. **交互逻辑**

   - ✅ 页面初始化时自动加载数据源分析数据
   - ✅ 切换数据源时重新获取对应的趋势和词云数据
   - ✅ 标签页切换时加载相应数据

6. **Loading状态**
   - ✅ 添加 `dataSourceLoading` 状态管理
   - ✅ 组件loading属性绑定到实际加载状态

### 技术实现

- **接口调用**: 使用async/await模式，确保数据获取的可靠性
- **错误处理**: try-catch机制，失败时自动降级到mock数据
- **类型安全**: 完整的TypeScript类型定义，确保数据结构一致性
- **用户体验**: 平滑的loading状态和错误提示

### 测试验证

- ✅ 接口正常返回数据时显示真实数据
- ✅ 接口异常时自动使用mock数据
- ✅ 数据源切换功能正常
- ✅ Loading状态显示正常
