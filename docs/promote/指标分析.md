# 指标分析组件实现指南

## 参考组件分析

参考组件：`docs/exampleCode/meicloud-changan-voc-main_v3/src/components/pages_components/businessComponents/analysis/indexAnalysis.vue`

## 组件功能概述

指标分析组件是一个综合性的数据展示组件，包含以下核心功能：

### 🎯 核心功能

1. **拓扑图展示** - 使用 ECharts 树形图展示指标层级关系
2. **数据表格** - 使用 Element Plus 表格展示详细排名数据
3. **排名对比** - 显示当前排名和上期排名对比
4. **环比分析** - 支持提及量、体验值、负面提及率的环比显示
5. **交互功能** - 支持点击查看详情、下载、排序等

### 📊 数据结构

#### 表格数据格式

```typescript
interface IndexAnalysisData {
  detail: Array<{
    indexId: string           // 指标ID
    keyWord: string           // 关键词
    totalMentionValue: number // 提及量
    experienceValue?: number  // 体验值（可选）
    negativeMentionRate?: number // 负面提及率（可选）
    momTotalMentionValueRate: number // 提及量环比
    momExperienceValueRate?: number  // 体验值环比
    momNegativeMentionRate?: number  // 负面提及率环比
    nowIndex: number          // 当前排名
    momIndex: number | string // 上期排名
  }>
  detailMom: Array<{...}>     // 上期数据（用于排名对比）
}
```

#### 拓扑图数据格式

```typescript
interface TopologicalData {
  name: string // 节点名称
  value: number // 节点值
  indexId: string // 指标ID
  children?: TopologicalData[] // 子节点
  itemStyle?: {
    // 高亮样式
    color: string
  }
}
```

### 🔧 技术实现要点

#### 1. 组件结构

```vue
<template>
  <div class="index-analysis">
    <!-- 标题栏 -->
    <div class="chart-header">
      <h3>指标排名</h3>
      <div class="actions">
        <el-button @click="handleDownload">下载</el-button>
      </div>
    </div>

    <!-- 拓扑图 -->
    <div class="topological-chart">
      <!-- 使用 ECharts 树形图 -->
    </div>

    <!-- 数据表格 -->
    <div class="data-table">
      <el-table :data="tableData" @sort-change="handleSortChange">
        <!-- 排名列 -->
        <!-- 关键词列 -->
        <!-- 提及量列 -->
        <!-- 环比列 -->
        <!-- 动态列（体验值/负面提及率） -->
      </el-table>
    </div>
  </div>
</template>
```

#### 2. 核心 Props

```typescript
interface Props {
  chartId?: string // 图表ID
  loading?: boolean // 加载状态
  data: IndexAnalysisData // 表格数据
  remarkData: TopologicalData[] // 拓扑图数据
  remarkData2?: string // 关键词列标题
  remarkData3?: string // 高亮节点ID
  attr?: string // 显示属性（experienceValue/negativeMentionRate）
  attrName?: string // 属性名称
}
```

#### 3. 核心方法

```typescript
// 数据处理
const dataHandle = () => {
  // 1. 排序数据
  // 2. 计算排名对比
  // 3. 更新表格数据
}

// 拓扑图数据处理
const remarkDataHandle = () => {
  // 1. 转换数据格式
  // 2. 设置高亮样式
  // 3. 更新图表数据
}

// 排序处理
const sortChange = (sortData: any) => {
  // 1. 更新排序字段
  // 2. 重新处理数据
  // 3. 更新表格
}
```

### 🎨 样式设计

#### 1. 布局结构

- 响应式设计，支持不同屏幕尺寸
- 拓扑图和表格垂直排列
- 表格支持横向滚动

#### 2. 交互样式

- 关键词点击高亮
- 排序图标状态变化
- 环比数据颜色区分（上升绿色，下降红色）

#### 3. 主题色彩

- 主色调：#0077FF（蓝色）
- 成功色：#52C718（绿色）
- 警告色：#FFAA00（橙色）
- 错误色：#FF4A4D（红色）

### 📋 实现步骤

#### 第一步：创建组件目录结构

```
src/components/IndexAnalysis/
├── index.vue          # 主组件文件
├── types.d.ts         # 类型定义
├── README.md          # 组件说明
└── components/        # 子组件（可选）
    ├── TopologicalChart.vue
    └── IndexTable.vue
```

#### 第二步：实现核心功能

1. **数据处理逻辑** - 排名计算、环比分析
2. **拓扑图组件** - 基于 ECharts 的树形图
3. **表格组件** - 基于 Element Plus 的数据表格
4. **交互功能** - 点击、排序、下载等

#### 第三步：集成到 Home 页面

1. 在 `src/views/home/<USER>
2. 导入并使用 IndexAnalysis 组件
3. 准备测试数据
4. 处理组件事件

### 🔗 依赖组件

#### 必需组件

- `ShowCompare` - 环比数据显示组件（已存在）
- `TopologicalChart` - 拓扑图组件（需要创建）

### 📝 注意事项

1. **数据格式兼容** - 确保与参考组件的数据格式完全兼容
2. **性能优化** - 大数据量时的渲染性能
3. **响应式设计** - 支持不同屏幕尺寸
4. **类型安全** - 完整的 TypeScript 类型定义
5. **错误处理** - 数据异常时的友好提示

### 🎯 验收标准

- [ ] 拓扑图正确显示层级关系
- [ ] 表格数据排序功能正常
- [ ] 环比数据显示正确
- [ ] 点击交互功能完整
- [ ] 响应式布局适配
- [ ] TypeScript 类型检查通过
- [ ] 样式与设计稿一致

---

**实现优先级**: 高
**预计工作量**: 2-3天
**技术难度**: 中等
