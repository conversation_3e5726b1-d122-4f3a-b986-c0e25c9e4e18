# 用户管理与角色管理系统 - 完整实现方案

> 基于 RBAC（Role-Based Access Control）模型的企业级权限管理系统

## 📋 项目概述

### 技术架构

- **前端框架**: Vue 3.5.13 + TypeScript + Element Plus
- **状态管理**: Pinia
- **数据模拟**: Mock.js
- **权限模型**: RBAC（用户-角色-权限）

### 核心模块

1. **用户管理** - 用户信息的增删改查、状态管理
2. **角色管理** - 角色定义、权限分配、层级管理
3. **权限管理** - 菜单权限、按钮权限、数据权限
4. **登录认证** - JWT Token、会话管理

---

## 🎯 功能需求详细说明

### 1. 用户管理模块

#### 1.1 核心功能

- [x] **用户列表展示** - 分页查询、搜索过滤
- [ ] **用户信息管理** - 新增、编辑、删除用户
- [ ] **密码管理** - 密码重置、强制修改密码
- [ ] **用户状态控制** - 启用/禁用、锁定/解锁
- [ ] **角色分配** - 为用户分配一个或多个角色
- [ ] **批量操作** - 批量删除、批量状态修改

#### 1.2 数据字段设计

```typescript
interface User {
  id: number // 用户ID
  username: string // 用户名（登录账号）
  nickname: string // 显示名称
  email: string // 邮箱
  phone?: string // 手机号
  avatar?: string // 头像URL
  status: UserStatus // 用户状态
  roles: Role[] // 关联角色列表
  lastLoginTime?: Date // 最后登录时间
  createTime: Date // 创建时间
  updateTime: Date // 更新时间
  remark?: string // 备注
}

enum UserStatus {
  ACTIVE = 'active', // 正常
  DISABLED = 'disabled', // 禁用
  LOCKED = 'locked' // 锁定
}
```

#### 1.3 UI界面要求

- **列表页面**: 支持搜索栏（用户名、邮箱）、状态筛选、分页
- **表单对话框**: 响应式布局、表单验证、图片上传
- **操作权限**: 根据当前用户角色显示对应操作按钮

### 2. 角色管理模块

#### 2.1 核心功能

- [x] **角色列表展示** - 显示所有角色信息
- [ ] **角色权限配置** - 树形菜单权限分配
- [ ] **角色层级管理** - 支持角色继承关系
- [ ] **数据权限控制** - 部门级别、个人级别权限
- [ ] **角色复制功能** - 快速创建相似角色

#### 2.2 数据字段设计

```typescript
interface Role {
  id: number // 角色ID
  name: string // 角色名称
  code: string // 角色编码（唯一）
  description?: string // 角色描述
  level: number // 角色层级（1-超级管理员，2-管理员，3-普通用户）
  permissions: Permission[] // 权限列表
  menus: Menu[] // 菜单权限
  dataScope: DataScope // 数据权限范围
  status: RoleStatus // 角色状态
  createTime: Date // 创建时间
  updateTime: Date // 更新时间
}

enum DataScope {
  ALL = 'all', // 全部数据
  DEPT = 'dept', // 本部门
  DEPT_AND_SUB = 'dept_sub', // 本部门及下级
  SELF = 'self' // 仅本人
}

enum RoleStatus {
  ENABLE = 'enable', // 启用
  DISABLE = 'disable' // 禁用
}
```

### 3. 权限管理模块

#### 3.1 权限类型

- **菜单权限**: 控制页面访问
- **按钮权限**: 控制操作按钮显示
- **接口权限**: 控制API调用
- **数据权限**: 控制数据访问范围

#### 3.2 数据字段设计

```typescript
interface Permission {
  id: number // 权限ID
  name: string // 权限名称
  code: string // 权限标识码
  type: PermissionType // 权限类型
  parentId?: number // 父权限ID
  path?: string // 路由路径
  component?: string // 组件路径
  icon?: string // 图标
  sort: number // 排序
  status: PermissionStatus // 状态
}

enum PermissionType {
  MENU = 'menu', // 菜单
  BUTTON = 'button', // 按钮
  API = 'api' // 接口
}
```

---

## 🎨 UI/UX 设计规范

### 1. 整体布局

- **卡片式设计**: 使用 `el-card` 组件包装内容
- **响应式布局**: 适配桌面端和平板端
- **统一间距**: 使用项目 SCSS 变量统一间距

### 2. 交互设计

- **确认对话框**: 删除操作需二次确认
- **加载状态**: 异步操作显示 loading 效果
- **消息提示**: 操作成功/失败的用户反馈
- **表单验证**: 实时验证 + 提交验证

### 3. 视觉设计

```scss
// 推荐的样式变量使用
.user-management {
  .search-form {
    margin-bottom: $spacing-lg;
    padding: $spacing-md;
    background: $bg-color-secondary;
    border-radius: $border-radius;
  }

  .action-buttons {
    margin-bottom: $spacing-md;

    .el-button + .el-button {
      margin-left: $spacing-sm;
    }
  }
}
```

---

## 💾 Mock 数据设计

### 创建文件结构

```
src/mock/
├── system/
│   ├── user.ts          # 用户相关Mock数据
│   ├── role.ts          # 角色相关Mock数据
│   ├── permission.ts    # 权限相关Mock数据
│   └── index.ts         # 统一导出
```

### Mock数据示例

#### 用户Mock数据

```typescript
// src/mock/system/user.ts
export const userListMock: User[] = [
  {
    id: 1,
    username: 'admin',
    nickname: '超级管理员',
    email: '<EMAIL>',
    phone: '13800138000',
    avatar: 'https://picsum.photos/100/100?random=1',
    status: UserStatus.ACTIVE,
    roles: [{ id: 1, name: '超级管理员', code: 'SUPER_ADMIN' }],
    lastLoginTime: new Date('2025-01-10 09:30:00'),
    createTime: new Date('2024-01-01'),
    updateTime: new Date('2025-01-10'),
    remark: '系统超级管理员账号'
  },
  {
    id: 2,
    username: 'manager',
    nickname: '部门经理',
    email: '<EMAIL>',
    phone: '13800138001',
    status: UserStatus.ACTIVE,
    roles: [{ id: 2, name: '部门经理', code: 'DEPT_MANAGER' }],
    lastLoginTime: new Date('2025-01-09 16:45:00'),
    createTime: new Date('2024-03-15'),
    updateTime: new Date('2025-01-09')
  }
  // ... 更多用户数据
]
```

#### 角色Mock数据

```typescript
// src/mock/system/role.ts
export const roleListMock: Role[] = [
  {
    id: 1,
    name: '超级管理员',
    code: 'SUPER_ADMIN',
    description: '拥有系统全部权限的超级管理员',
    level: 1,
    permissions: [], // 全部权限
    menus: [], // 全部菜单
    dataScope: DataScope.ALL,
    status: RoleStatus.ENABLE,
    createTime: new Date('2024-01-01'),
    updateTime: new Date('2024-01-01')
  },
  {
    id: 2,
    name: '部门经理',
    code: 'DEPT_MANAGER',
    description: '部门管理人员，负责部门数据管理',
    level: 2,
    permissions: [
      { id: 1, code: 'user:view', name: '查看用户' },
      { id: 2, code: 'user:add', name: '新增用户' },
      { id: 3, code: 'data:view', name: '查看数据' }
    ],
    menus: [
      { id: 1, name: '数据分析', path: '/data' },
      { id: 2, name: '用户管理', path: '/system/user' }
    ],
    dataScope: DataScope.DEPT_AND_SUB,
    status: RoleStatus.ENABLE,
    createTime: new Date('2024-02-01'),
    updateTime: new Date('2024-02-01')
  }
]
```

---

## 🔧 技术实现要点

### 1. API接口设计

```typescript
// src/api/system/user.ts
export interface UserAPI {
  // 获取用户列表
  getUserList(params: UserQueryParams): Promise<PageResult<User>>
  // 获取用户详情
  getUserById(id: number): Promise<User>
  // 创建用户
  createUser(user: CreateUserRequest): Promise<User>
  // 更新用户
  updateUser(id: number, user: UpdateUserRequest): Promise<User>
  // 删除用户
  deleteUser(id: number): Promise<void>
  // 重置密码
  resetPassword(id: number): Promise<void>
  // 分配角色
  assignRoles(userId: number, roleIds: number[]): Promise<void>
}
```

### 2. 状态管理 (Pinia Store)

```typescript
// src/store/modules/system.ts
export const useSystemStore = defineStore('system', {
  state: () => ({
    users: [] as User[],
    roles: [] as Role[],
    permissions: [] as Permission[],
    loading: false
  }),

  getters: {
    activeUsers: state => state.users.filter(u => u.status === UserStatus.ACTIVE),
    enabledRoles: state => state.roles.filter(r => r.status === RoleStatus.ENABLE)
  },

  actions: {
    async fetchUsers() {
      this.loading = true
      try {
        // Mock API 调用
        this.users = userListMock
      } finally {
        this.loading = false
      }
    }
  }
})
```

### 3. 权限控制组件

```vue
<!-- src/components/PermissionGuard/index.vue -->
<template>
  <div v-if="hasPermission">
    <slot />
  </div>
</template>

<script setup lang="ts">
interface Props {
  permission: string | string[]
  mode?: 'any' | 'all' // 多权限时的判断模式
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'any'
})

// 权限检查逻辑
const hasPermission = computed(() => {
  // 实现权限检查
  return true
})
</script>
```

---

## ✅ 开发检查清单

### Phase 1: 数据层建设

- [ ] 创建 TypeScript 接口定义
- [ ] 建立 Mock 数据文件
- [ ] 实现 API 接口封装
- [ ] 配置 Pinia Store

### Phase 2: 用户管理功能

- [ ] 用户列表页面（搜索、分页、筛选）
- [ ] 用户表单组件（新增、编辑）
- [ ] 用户删除功能（批量删除）
- [ ] 用户状态管理
- [ ] 角色分配功能

### Phase 3: 角色管理功能

- [ ] 角色列表页面
- [ ] 角色表单组件
- [ ] 权限分配树形组件
- [ ] 数据权限配置
- [ ] 角色复制功能

### Phase 4: 权限控制

- [ ] 路由权限守卫
- [ ] 菜单权限过滤
- [ ] 按钮权限组件
- [ ] API接口权限拦截

### Phase 5: 用户体验优化

- [ ] 加载状态处理
- [ ] 错误边界处理
- [ ] 操作确认对话框
- [ ] 表单验证优化
- [ ] 响应式布局适配

---

## 🚀 快速开始

### 第一步：创建Mock数据

```bash
# 在 src/mock/ 目录下创建 system 文件夹
mkdir src/mock/system
```

### 第二步：实现基础API

```bash
# 在 src/api/ 目录下创建 system 文件夹
mkdir src/api/system
```

### 第三步：完善页面功能

按照检查清单依次实现各个功能模块

---

## 📖 参考资源

### RBAC最佳实践

- [用户权限管理设计模式](https://patterns.dev/posts/rbac-pattern)
- [Element Plus 表格组件最佳实践](https://element-plus.org/zh-CN/component/table.html)
- [Vue 3 权限管理完整方案](https://vue3js.cn/interview/vue3/permission.html)

### 相关文档

- [项目技术栈文档](../framework/)
- [组件使用指南](../components/)
- [开发规范](../../.cursor/rules/project-guide.mdc)
