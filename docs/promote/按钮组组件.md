# 按钮组组件

## 组件简介

按钮组组件用于在页面中展示一组功能相关的按钮，常用于切换视图、筛选数据等场景。最简实现支持单选和选中状态展示。

## 典型场景

- 数据分析页面的视图切换（如：概览、TOP问题、人群特征等）
- 过滤条件选择

## 最简用法

```vue
<ButtonGroup
  :options="[
    { label: '概览', value: 'overview' },
    { label: 'TOP问题', value: 'top' },
    { label: '人群特征', value: 'crowd' }
  ]"
  v-model="activeTab"
  @change="handleTabChange"
/>

<script setup lang="ts">
const activeTab = ref('overview')
const handleTabChange = (val: string) => {
  // 这里可以处理切换逻辑
  console.log('当前选中：', val)
}
</script>
```

## 参数说明

| 参数名  | 类型     | 说明               | 是否必填 | 默认值 |
| ------- | -------- | ------------------ | -------- | ------ |
| options | Option[] | 按钮选项数组       | 是       | —      |
| v-model | string   | 当前选中值（单选） | 是       | —      |

### Option 类型定义

```ts
interface Option {
  label: string // 按钮显示文本
  value: string // 按钮值
}
```

## 事件说明

| 事件名 | 说明           | 回调参数          |
| ------ | -------------- | ----------------- |
| change | 按钮切换时触发 | 当前选中值 string |

## 交互说明

- 仅支持单选模式，点击按钮切换选中状态。
- 当前选中按钮高亮显示。
- 切换按钮后会抛出 change 事件，参数为当前选中值。

## 样式规范

### 按钮默认样式

```css
.button {
  background: #f9f9f9;
  border: 1.05px solid #dadada;
  border-radius: 4.2px 4.2px 0px 0px;
  color: rgba(0, 0, 0, 0.6);
  font-weight: 400;
  text-align: center;
  display: inline-block;
  min-width: 100px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 14px;
  position: relative;
  padding: 8px 0;
}
```

### 按钮间隔

多个按钮之间建议使用 `margin-right: 10px;`，最后一个按钮不需要间隔。

### 按钮选中样式

```css
.button.selected {
  background: #ffffff;
  color: #0077ff;
}
```

### 选中按钮下划线效果

```css
.button.selected::after {
  content: '';
  display: block;
  position: absolute;
  width: 100%;
  height: 2px;
  background: #0077ff;
  left: 0;
  bottom: 0;
  border-radius: 4px;
}
```

> 注意：可根据实际组件类名进行样式调整，上述为推荐实现方式。

## 常见问题

- 如何自定义按钮样式？
  可通过外部 class 或 style 覆盖默认样式，或在组件内扩展样式变量。
- 如何监听按钮切换事件？
  通过 v-model 绑定变量，变量变化即为选项切换。

---

如需进一步定制或有特殊需求，请补充说明！

## 组件目录结构

推荐将按钮组组件放在 `src/components/ButtonGroup/` 目录下，结构如下：

```
src/components/ButtonGroup/
├── index.vue        # 按钮组主组件
├── types.d.ts       # 组件类型定义（如 Option 类型）
└── index.ts         # 组件导出（可选，便于全局注册）
```

- `index.vue`：按钮组的核心实现，包含模板、逻辑和样式。
- `types.d.ts`：定义 Option 类型、事件类型等 TypeScript 类型。
- `index.ts`：统一导出组件，便于在项目中按需或全局引入。

如需扩展，可在该目录下增加单元测试、文档等文件。
