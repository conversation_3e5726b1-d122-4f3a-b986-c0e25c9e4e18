# 原文明细组件开发需求

## 📋 需求概述

基于参考组件 `docs/exampleCode/meicloud-changan-voc-main_v3/src/components/pages_components/businessComponents/analysis/textDetails.vue` 实现现代化的**原文明细组件**，用于展示VOC（客户之声）数据的详细信息。

## 🎯 核心功能

### 1. 数据表格展示

- **表格结构**：序号 + 内容 + 操作（三列布局）
- **内容列显示**：
  - 文章/工单标题（加粗显示）
  - 详细内容（2行截断显示，超出省略号）
  - 数据源信息（数据来源、品牌车系、创建时间、VIN码等）
  - 情感标签（正面=绿色、中性=蓝色、负面=红色）
- **操作列功能**：
  - "查看用户详情"按钮（如有用户ID）
  - "查看原文"按钮

### 2. 分页与搜索

- Element Plus 分页组件
- 支持页面大小切换：10/20/30条
- 数据总数显示："共查询到 XXX 条数据"
- 响应式布局适配

## 🛠️ 技术规范

### 技术栈要求

- **Vue 3.5.13** + Composition API + `<script setup>`
- **Element Plus 2.10.2** 组件库
- **TypeScript 5.8.3** 类型定义
- **SCSS** 样式预处理

### 组件结构

```
src/components/OriginalDetails/
├── index.vue           # 主组件
├── index.ts           # 统一导出
├── types.d.ts         # TypeScript类型定义
└── components/        # 子组件
    ├── KeywordTags.vue    # 关键词标签
    └── TextInfos.vue      # 文本信息
```

## 📊 数据源类型支持

需要支持以下数据源类型及其字段映射：

### 1. 帖子评论 (post_comments)

- 标题字段：`postsTitle`
- 内容字段：`postsContent` / `comment`
- 特有字段：分享量、浏览量、评论量

### 2. 工单 (work_order)

- 标题字段：`title`
- 内容字段：`content`
- 特有字段：工单ID、分类层级

### 3. 意见反馈 (feedback)

- 标题字段：`title`
- 内容字段：`content`
- 特有字段：业务来源、目录分类

### 4. 咨询服务 (consulting_service)

- 标题字段：`title`
- 内容字段：`content`
- 特有字段：咨询类型

### 5. 问卷调研 (questionnaire)

- 标题字段：`title`
- 内容字段：`answer_content`
- 特有字段：答案分数、业务场景

## 🎨 UI/UX设计要求

### 视觉风格

- 遵循项目设计规范
- 使用项目全局SCSS变量
- 响应式布局设计
- 现代化扁平风格

### 交互体验

- 表格行悬停效果
- 按钮点击反馈动画
- 加载状态显示

### 关键词标签样式

```scss
.keyword-tag {
  &.positive {
    background: rgba(1, 198, 142, 0.08);
    color: #01c68e;
  }
  &.neutral {
    background: rgba(0, 119, 255, 0.08);
    color: #0077ff;
  }
  &.negative {
    background: rgba(247, 61, 71, 0.08);
    color: #f73d47;
  }
}
```

## 🔧 组件接口设计

### Props定义

```typescript
interface Props {
  data: OriginalTextItem[] // 表格数据
  total: number // 数据总数
  loading?: boolean // 加载状态
  pageSize?: number // 每页条数
  currentPage?: number // 当前页码
  indexTypeName?: string // 指标类型名称
  title?: string // 组件标题
}
```

### Events定义

```typescript
interface Emits {
  'page-change': [{ pageNum: number; pageSize?: number }] // 分页变化
  'user-detail': [{ oneId: string; userName: string }] // 查看用户详情
}
```

### 数据类型定义

```typescript
interface OriginalTextItem {
  id: string | number
  dataSourceName: string // 数据源名称
  brandName?: string // 品牌名称
  seriesName?: string // 车系名称
  createTime?: string // 创建时间
  vin?: string // 车架号
  isCarOwner?: string // 是否车主
  oneId?: string // 用户ID
  user?: string // 用户名
  name?: string // 姓名
  analysisResult?: {
    // 分析结果
    extractedinfo?: Array<{
      standardkeyword: string
      extractedsense: '正面' | '中性' | '负面'
      extracteddomain: string
    }>
  }
  // 动态字段（根据数据源类型变化）
  [key: string]: any
}
```

## 📍 集成说明

### 添加到Home页面

```vue
<template>
  <div class="home-page">
    <!-- 其他组件 -->

    <!-- 原文明细组件 -->
    <OriginalDetails
      :data="originalData"
      :total="totalCount"
      :loading="loading"
      :page-size="pageSize"
      :current-page="currentPage"
      title="原文明细"
      @page-change="handlePageChange"
      @user-detail="handleUserDetail"
    />
  </div>
</template>
```

## ✅ 验收标准

1. **功能完整性**：所有核心功能正常工作
2. **类型安全**：完整的TypeScript类型定义
3. **响应式设计**：适配移动端和桌面端
4. **性能优化**：大数据量流畅渲染
5. **代码质量**：通过ESLint和Prettier检查
6. **组件复用性**：可在其他页面复用

## 📝 开发注意事项

1. **样式隔离**：使用scoped样式避免污染
2. **错误处理**：妥善处理数据异常情况
3. **无障碍性**：添加适当的aria标签
4. **测试覆盖**：确保组件功能稳定可靠

---

**预期交付**：完整可用的原文明细组件，集成到Home页面，符合项目技术规范和设计标准。
