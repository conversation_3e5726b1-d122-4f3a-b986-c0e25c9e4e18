# 组件开发提示词优化模板

## 模板说明

本模板用于优化组件开发任务的提示词，确保开发任务描述清晰、可操作、易理解。

## 标准提示词结构

### 1. 任务标题和概述

```markdown
# [组件名称] 开发任务

## 任务目标

[简洁描述要完成的具体任务]

## 业务背景

[可选：说明为什么需要这个组件，解决什么问题]
```

### 2. 参考资源

```markdown
## 参考资源

**参考组件**: [文件路径]
**设计稿**: [如果有设计稿，提供链接]
**相关文档**: [相关的业务文档或API文档]
```

### 3. 功能需求分析

```markdown
## 功能需求分析

### 核心功能

1. **[功能1]** - [功能描述]
2. **[功能2]** - [功能描述]
3. **[功能3]** - [功能描述]

### 数据字段

- `field1`: [字段说明]
- `field2`: [字段说明]
- `field3`: [字段说明]

### 交互需求

- [交互行为1]
- [交互行为2]
- [交互行为3]
```

### 4. 技术实现要求

```markdown
## 技术实现要求

### 组件结构
```

src/components/[ComponentName]/
├── index.vue # 主组件文件
├── types.d.ts # TypeScript类型定义
├── README.md # 组件说明文档
└── [子组件]/ # 可选：子组件目录

````

### 技术要求
1. **Vue 3 Composition API** - 使用 `<script setup>` 语法
2. **TypeScript** - 完整的类型定义和类型检查
3. **Element Plus** - 使用项目既定的UI组件
4. **响应式设计** - 支持不同屏幕尺寸
5. **性能优化** - 考虑大数据量下的渲染性能

### 组件接口设计
```typescript
interface ComponentProps {
  // Props 定义
}

interface ComponentEmits {
  // Events 定义
}
````

````

### 5. 集成要求
```markdown
## 集成要求

### 页面集成
1. 在目标页面中导入组件
2. 注册组件并配置路由
3. 添加相应的数据和方法

### 数据准备
```typescript
// 示例数据结构
const componentData = ref<DataType[]>([
  // 示例数据
])
````

````

### 6. 开发步骤
```markdown
## 开发步骤

### 第一步：创建组件结构
1. 创建组件目录和文件
2. 定义基础类型接口
3. 实现组件基础结构

### 第二步：实现核心功能
1. [具体功能实现步骤]
2. [具体功能实现步骤]
3. [具体功能实现步骤]

### 第三步：样式和优化
1. 添加响应式样式
2. 实现加载状态
3. 添加错误处理
4. 性能优化

### 第四步：集成测试
1. 在目标页面中集成组件
2. 测试各种交互功能
3. 验证数据展示正确性
4. 检查响应式效果
````

### 7. 质量要求

```markdown
## 质量要求

### 代码质量

- 遵循Vue 3最佳实践
- 完整的TypeScript类型定义
- 清晰的组件接口设计
- 良好的代码注释

### 用户体验

- 流畅的交互体验
- 清晰的数据展示
- 直观的操作反馈
- 响应式布局适配

### 性能要求

- 支持大数据量渲染
- 流畅的交互操作
- 合理的组件更新策略
- 内存使用优化
```

### 8. 验收标准

```markdown
## 验收标准

1. ✅ 组件功能完整，符合需求描述
2. ✅ 代码质量良好，符合项目规范
3. ✅ 在目标页面中正常显示和使用
4. ✅ 响应式设计，适配不同屏幕
5. ✅ 交互功能正常，用户体验良好
```

## 使用指南

### 何时使用此模板

- 开发新的Vue组件
- 重构现有组件
- 添加新功能模块
- 集成第三方组件

### 如何填写模板

1. **替换占位符** - 将 `[占位符]` 替换为具体内容
2. **保持简洁** - 每个部分内容要简洁明了
3. **突出重点** - 重点功能要详细描述
4. **提供示例** - 关键的数据结构和接口要提供示例

### 模板优化建议

1. **根据项目特点调整** - 根据具体项目技术栈调整技术要求
2. **添加项目特定规范** - 包含项目特定的代码规范和约定
3. **考虑团队协作** - 确保提示词便于团队成员理解和执行
4. **保持更新** - 根据项目发展持续优化模板内容

## 示例应用

### 简单组件

对于简单的展示组件，可以简化模板，重点描述：

- 数据接口
- 展示效果
- 集成方式

### 复杂组件

对于复杂的交互组件，需要详细描述：

- 状态管理
- 事件处理
- 性能优化
- 错误处理

### 业务组件

对于业务逻辑组件，需要重点关注：

- 业务规则
- 数据验证
- 权限控制
- 异常处理
