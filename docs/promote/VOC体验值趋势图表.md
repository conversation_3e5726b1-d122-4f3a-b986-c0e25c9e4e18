# VOC体验值趋势图表

## 目录

1. [项目目录结构建议](#项目目录结构建议)
2. [图表概述](#图表概述)
3. [图表类型](#图表类型)
4. [数据结构](#数据结构)
   - [输入数据格式](#输入数据格式)
   - [图表配置数据](#图表配置数据)
5. [图表配置项](#图表配置项)
   - [基础配置](#基础配置)
   - [提示框配置](#提示框配置)
   - [自定义提示框格式化函数](#自定义提示框格式化函数)
   - [提示框HTML结构](#提示框html结构)
   - [提示框CSS样式](#提示框css样式)
   - [数据格式化函数](#数据格式化函数)
   - [X轴配置](#x轴配置)
   - [双Y轴配置](#双y轴配置)
   - [系列配置](#系列配置)
6. [交互功能](#交互功能)
   - [时间类型切换](#时间类型切换)
   - [点击交互](#点击交互)
   - [提示框交互](#提示框交互)
7. [数据处理要点](#数据处理要点)
   - [数据预处理](#数据预处理)
   - [数据格式化](#数据格式化)
8. [样式规范](#样式规范)
   - [颜色规范](#颜色规范)
   - [字体规范](#字体规范)
   - [间距规范](#间距规范)
9. [响应式设计](#响应式设计)
   - [自适应处理](#自适应处理)
   - [空数据处理](#空数据处理)
10. [性能优化](#性能优化)
    - [图表实例管理](#图表实例管理)
    - [数据更新优化](#数据更新优化)
11. [使用示例](#使用示例)
    - [完整配置示例](#完整配置示例)
    - [Tooltip格式化函数示例](#tooltip格式化函数示例)
12. [注意事项](#注意事项)
13. [兼容性要求](#兼容性要求)

---

## 项目目录结构建议

建议将VOC体验值趋势图表组件作为独立业务组件，放在 `src/components/VocTrendChart/` 目录下，结构如下：

```
src/
└── components/
    └── VocTrendChart/
        ├── index.vue           # 组件主文件（实现图表渲染、交互等）
        ├── types.d.ts          # TypeScript 类型定义（如数据结构、props等）
        └── README.md           # 组件说明文档（用法、参数、示例等）
```

**说明：**

- `index.vue`：主组件文件，负责ECharts渲染、props接收、交互处理等。
- `types.d.ts`：定义图表数据结构、props类型，保证类型安全。
- `README.md`：说明该组件的用途、参数、示例代码，方便团队成员快速上手。
- **tooltip相关样式已全局维护，详见下方样式规范。**

---

## 图表概述

这是一个复合图表，包含堆叠柱状图（显示正面、中性、负面提及量）和折线图（显示体验值趋势），用于展示VOC（Voice of Customer）体验值的时间趋势变化。

## 图表类型

- **主图表类型**: 复合图表（柱状图 + 折线图）
- **柱状图**: 堆叠柱状图，显示三种情感提及量
- **折线图**: 平滑折线图，显示体验值趋势
- **双Y轴**: 左侧Y轴显示提及量，右侧Y轴显示体验值

## 数据结构

### 输入数据格式

```javascript
;[
  {
    keyWord: '2024-01-01', // 日期（X轴标签）
    experienceValue: 75.6, // 体验值（右侧Y轴）
    positiveMentionValue: 1200, // 正面提及量
    neutralMentionValue: 800, // 中性提及量
    negativeMentionValue: 400, // 负面提及量（注意：显示为负值）
    totalMentionValue: 2400 // 总提及量
  }
  // ... 更多日期数据
]
```

### 图表配置数据

```javascript
{
  data: trendData,                    // 原始数据数组
  xDataKey: 'keyWord',               // X轴数据键名
  seriesDataKey: [                   // 系列配置
    {
      name: '中性提及量',             // 系列名称
      key: 'neutralMentionValue',    // 数据键名
      type: 'bar',                   // 图表类型：柱状图
      stack: 'total',                // 堆叠组名
      color: '#0C92E0'               // 颜色：蓝色
    },
    {
      name: '正面提及量',
      key: 'positiveMentionValue',
      type: 'bar',
      stack: 'total',
      color: '#3ED4A9'               // 颜色：绿色
    },
    {
      name: '负面提及量',
      key: 'negativeMentionValue',
      type: 'bar',
      stack: 'total',
      color: '#FF4A4D'               // 颜色：红色
    },
    {
      name: '体验值',                // 或自定义名称如 'VOC体验值'
      key: 'experienceValue',        // 数据键名
      type: 'line',                  // 图表类型：折线图
      yAxisIndex: 1,                 // 使用右侧Y轴
      color: '#5D7092',              // 颜色：灰色
      smooth: true                   // 平滑曲线
    }
  ]
}
```

## 图表配置项

### 基础配置

```javascript
{
  color: ['#0077FF', '#3ED4A9', '#5D7092', '#FFC157', '#7163FD', '#95D8F2', '#BA70CA', '#FA9C78', '#11999C', '#FEBAD6'],
  legend: {
    show: true,
    bottom: 0,
    itemWidth: 14,
    data: ['中性提及量', '正面提及量', '负面提及量', '体验值']
  },
  grid: {
    top: 55,
    right: 10,
    bottom: 40,
    left: 10,
    containLabel: true
  }
}
```

### 提示框配置

```javascript
tooltip: {
  trigger: 'axis',
  padding: 0,
  axisPointer: {
    type: 'cross',
    shadowStyle: {
      color: 'rgba(41, 148, 255, 0.1)'
    }
  },
  borderColor: '#0077FF',
  formatter: function(value) {
    // 自定义提示框内容
    // 格式：日期 + 各系列数值 + 总提及量
  }
}
```

### 自定义提示框格式化函数

```javascript
// 完整的tooltip.formatter实现
tooltipFormatter(value) {
  value = JSON.parse(JSON.stringify(value));

  // 定义系列显示顺序
  var order = ['正面提及量', '中性提及量', '负面提及量'];
  if (this.attrName) {
    order.unshift(this.attrName); // 将体验值放在最前面
  }

  var StrArr = [];
  let total = 0;
  var axisValueLabel = '';

  // 开始构建HTML结构
  var str = '<div class="public-tooltip-div">';

  for (var i = 0; i < value.length; i++) {
    // 日期标签（只显示一次）
    axisValueLabel = '<div class="axis-name">' + value[i].axisValueLabel + '</div>';

    // 根据系列名称确定显示顺序
    var index = order.indexOf(value[i].seriesName);

    // 构建每个系列的数据行
    StrArr[index] = '<div class="each-series"><span class="each-series-name">' +
                    value[i].marker + value[i].seriesName + '：</span>';

    // 根据系列类型格式化数值
    var showData;
    if (value[i].seriesName == '体验值') {
      showData = this.$publicHandle.formatNum(value[i].value);
    } else if (value[i].seriesName == '负面提及率') {
      showData = this.$publicHandle.formatPercent(value[i].value) + '%';
    } else {
      showData = this.$publicHandle.makeDataUnit(value[i].value);
    }

    // 计算总提及量（只计算提及量系列，不包括体验值）
    total += (value[i].seriesName == '正面提及量' ||
              value[i].seriesName == '中性提及量' ||
              value[i].seriesName == '负面提及量') ? Math.abs(value[i].value) : 0;

    StrArr[index] += '<span class="each-series-value">' + showData + '</span></div>';
  }

  // 拼接所有内容
  str += axisValueLabel + StrArr.join('');

  // 添加总提及量
  str += '<div class="each-series"><span class="each-series-name">&nbsp;&nbsp;&nbsp;总提及量：</span>' +
         '<span class="each-series-value">' + this.$publicHandle.makeDataUnit(total) + '</span></div></div>';

  // 如果需要显示点击提示
  if (this.needDetails == true) {
    str += '<div class="public-tooltip-click-tips">点击图形可查看分析</div>';
  }

  return str;
}
```

### 提示框HTML结构

```html
<!-- 完整的提示框HTML结构 -->
<div class="public-tooltip-div">
  <!-- 日期标题 -->
  <div class="axis-name">2024-01-01</div>

  <!-- 体验值行 -->
  <div class="each-series">
    <span class="each-series-name">● 体验值：</span>
    <span class="each-series-value">75.60</span>
  </div>

  <!-- 正面提及量行 -->
  <div class="each-series">
    <span class="each-series-name">● 正面提及量：</span>
    <span class="each-series-value">1.20K</span>
  </div>

  <!-- 中性提及量行 -->
  <div class="each-series">
    <span class="each-series-name">● 中性提及量：</span>
    <span class="each-series-value">800</span>
  </div>

  <!-- 负面提及量行 -->
  <div class="each-series">
    <span class="each-series-name">● 负面提及量：</span>
    <span class="each-series-value">400</span>
  </div>

  <!-- 总提及量行 -->
  <div class="each-series">
    <span class="each-series-name"> 总提及量：</span>
    <span class="each-series-value">2.40K</span>
  </div>
</div>

<!-- 点击提示（可选） -->
<div class="public-tooltip-click-tips">点击图形可查看分析</div>
```

### 提示框CSS样式

```css
/* 提示框容器样式 */
.public-tooltip-div {
  padding-top: 11px;
  padding-left: 17px;
  padding-right: 15px;
}

/* 日期标题样式 */
.public-tooltip-div .axis-name {
  margin-bottom: 10px;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 600;
}

/* 每个系列行样式 */
.public-tooltip-div .each-series {
  margin-bottom: 10px;
  color: rgba(0, 0, 0, 0.95);
  font-weight: 500;
}

/* 系列名称样式 */
.public-tooltip-div .each-series-name {
  width: 100px;
  margin-right: 10px;
  display: inline-block;
  text-align: left;
}

/* 系列数值样式 */
.public-tooltip-div .each-series-value {
  display: inline-block;
  min-width: 40px;
  text-align: right;
}

/* 点击提示样式 */
.public-tooltip-click-tips {
  padding: 8px 0;
  text-align: center;
  background: rgba(0, 0, 0, 0.05);
  color: rgba(135, 135, 135, 0.85);
}
```

### 数据格式化函数

```javascript
// 数据格式化工具函数
const formatUtils = {
  // 格式化数值（保留两位小数，添加千分位）
  formatNum(data) {
    data = parseFloat(data)
    if (isNaN(data)) return '-'
    data = data.toFixed(2)
    return this.Thousandth(data)
  },

  // 格式化百分比
  formatPercent(data) {
    data = parseFloat(data)
    if (isNaN(data)) return '-'
    data = data * 100
    data = data.toFixed(2)
    return this.Thousandth(data)
  },

  // 格式化数据单位（K、万、千万、亿）
  makeDataUnit(data) {
    data = Math.abs(data)
    let unit = ''
    let isNeedFixed = false

    if (data > 10000) {
      data = data / 10000
      unit = '万'
      isNeedFixed = true

      if (data >= 1000) {
        data = data / 1000
        unit = '千万'
        isNeedFixed = true

        if (data >= 10) {
          data = data / 10
          unit = '亿'
          isNeedFixed = true
        }
      }
    }

    if (isNeedFixed) {
      data = data.toFixed(2)
    }

    data = this.Thousandth(data)
    if (data.toString() === 'NaN') return '-'
    return data + unit
  },

  // 千分位格式化
  Thousandth(num) {
    if (num !== undefined) {
      const reg = /\d{1,3}(?=(\d{3})+$)/g
      return (num + '').replace(reg, '$&,')
    }
    return '-'
  }
}
```

### X轴配置

```javascript
xAxis: {
  name: '',                          // X轴名称
  axisLabel: {
    margin: 20,
    fontSize: 14,
    rotate: xdata.length >= 5 ? 35 : 0,  // 数据点≥5个时旋转35度
    width: 100
  },
  triggerEvent: true,
  type: 'category',
  data: xdata,                       // 日期数组
  axisPointer: { type: 'shadow' },
  splitLine: { show: true },
  axisTick: { show: false },
  axisLine: { show: false },
  nameTextStyle: { color: 'rgba(0,0,0,0.45)' }
}
```

### 双Y轴配置

```javascript
yAxis: [
  {
    name: '提及量', // 左侧Y轴名称
    show: true,
    axisLabel: {
      show: true,
      formatter: function (value) {
        // 格式化提及量显示（如：1.2K, 1.5M等）
        return formatDataUnit(value)
      }
    },
    splitLine: {
      show: true,
      lineStyle: { color: '#F0F0F0' }
    },
    splitArea: {
      interval: 1,
      show: true,
      areaStyle: {
        color: ['rgba(255, 255, 255, 0)', 'rgba(250, 250, 250, 1)']
      }
    },
    axisTick: { show: false },
    axisLine: { show: false },
    nameTextStyle: { color: 'rgba(0,0,0,0.45)' }
  },
  {
    name: '体验值', // 右侧Y轴名称
    show: true,
    axisLabel: {
      show: true,
      formatter: function (value) {
        return value // 直接显示数值
      }
    },
    splitLine: {
      show: true,
      lineStyle: { color: '#F0F0F0' }
    },
    splitArea: {
      interval: 1,
      show: true,
      areaStyle: {
        color: ['rgba(255, 255, 255, 0)', 'rgba(250, 250, 250, 1)']
      }
    },
    axisTick: { show: false },
    axisLine: { show: false },
    nameTextStyle: { color: 'rgba(0,0,0,0.45)' }
  }
]
```

### 系列配置

```javascript
series: [
  {
    name: '中性提及量',
    type: 'bar',
    stack: 'total',
    data: neutralData,
    itemStyle: {
      borderRadius: 1,
      borderColor: '#fff',
      borderWidth: 0.3,
      emphasis: {
        shadowBlur: 10,
        shadowColor: '#0077FF'
      }
    },
    barMaxWidth: 25
  },
  {
    name: '正面提及量',
    type: 'bar',
    stack: 'total',
    data: positiveData,
    itemStyle: {
      borderRadius: 1,
      borderColor: '#fff',
      borderWidth: 0.3,
      emphasis: {
        shadowBlur: 10,
        shadowColor: '#0077FF'
      }
    },
    barMaxWidth: 25
  },
  {
    name: '负面提及量',
    type: 'bar',
    stack: 'total',
    data: negativeData, // 注意：负面数据需要转为负值
    itemStyle: {
      borderRadius: 1,
      borderColor: '#fff',
      borderWidth: 0.3,
      emphasis: {
        shadowBlur: 10,
        shadowColor: '#0077FF'
      }
    },
    barMaxWidth: 25
  },
  {
    name: '体验值',
    type: 'line',
    yAxisIndex: 1, // 使用右侧Y轴
    data: experienceData,
    smooth: true, // 平滑曲线
    lineStyle: {
      color: '#5D7092'
    },
    itemStyle: {
      color: '#5D7092'
    }
  }
]
```

## 交互功能

### 时间类型切换

- 支持"日"和"月"两种时间维度切换
- 切换时重新请求数据并更新图表

### 点击交互

- 点击图表区域可查看该时间点的详细分析
- 支持图表点击事件监听

### 提示框交互

- 鼠标悬停显示详细数据
- 自定义提示框格式，包含：
  - 日期
  - 各系列数值（格式化显示）
  - 总提及量
  - 点击提示文字

## 数据处理要点

### 数据预处理

1. **负面提及量处理**: 将负面提及量转为负值显示

```javascript
data[i].negativeMentionValue = data[i].negativeMentionValue ? -data[i].negativeMentionValue : 0
```

2. **日期排序**: 确保日期按时间顺序排列

```javascript
xdataArr.sort((a, b) => {
  return moment(a).format('x') - moment(b).format('x')
})
```

3. **图例排序**: 按固定顺序排列图例

```javascript
var legendDataSort = ['正面提及量', '中性提及量', '负面提及量']
legendData.sort((a, b) => {
  var indexa = legendDataSort.indexOf(a) == -1 ? 100 : legendDataSort.indexOf(a)
  var indexb = legendDataSort.indexOf(b) == -1 ? 100 : legendDataSort.indexOf(b)
  return indexa - indexb
})
```

### 数据格式化

- **提及量**: 使用单位格式化（K、M等）
- **体验值**: 直接显示数值
- **百分比**: 显示为百分比格式

## 样式规范

### 组件样式

- 组件本地仅保留容器、loading、empty等结构样式，全部采用 `<style scoped lang="scss">`，保证样式隔离。
- tooltip（提示框）相关样式已提取到全局 `src/styles/base.scss`，全项目可复用。
- 如需自定义 tooltip 样式，请在 `base.scss` 统一维护。

#### 全局 tooltip 样式示例

```scss
// src/styles/base.scss
.public-tooltip-div {
  padding-top: 11px;
  padding-left: 17px;
  padding-right: 15px;
  .axis-name {
    margin-bottom: 10px;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 600;
  }
  .each-series {
    margin-bottom: 10px;
    color: rgba(0, 0, 0, 0.95);
    font-weight: 500;
    .each-series-name {
      width: 100px;
      margin-right: 10px;
      display: inline-block;
      text-align: left;
    }
    .each-series-value {
      display: inline-block;
      min-width: 40px;
      text-align: right;
    }
  }
}
.public-tooltip-click-tips {
  padding: 8px 0;
  text-align: center;
  background: rgba(0, 0, 0, 0.05);
  color: rgba(135, 135, 135, 0.85);
}
```

### 颜色规范

- 正面提及量: `#3ED4A9` (绿色)
- 中性提及量: `#0C92E0` (蓝色)
- 负面提及量: `#FF4A4D` (红色)
- 体验值: `#5D7092` (灰色)

### 字体规范

- 轴标签字体大小: 14px
- 图例字体大小: 默认
- 提示框字体大小: 默认

### 间距规范

- 图表上边距: 55px
- 图表下边距: 40px
- 图表左右边距: 10px
- 轴标签边距: 20px

## 响应式设计

### 自适应处理

- 监听窗口大小变化，自动调整图表尺寸
- 数据点较多时自动旋转X轴标签

### 空数据处理

- 当没有数据时显示"暂无数据"提示
- 数据为空时隐藏图表容器

## 性能优化

### 图表实例管理

- 销毁旧的图表实例，避免内存泄漏
- 使用防抖处理窗口大小变化事件

### 数据更新优化

- 监听数据变化，避免不必要的重绘
- 使用Vue的watch机制优化更新

## 使用示例

### 完整配置示例

```javascript
// 初始化图表
const chartOption = {
  color: ['#0077FF', '#3ED4A9', '#5D7092', '#FFC157'],
  legend: {
    show: true,
    bottom: 0,
    itemWidth: 14,
    data: ['中性提及量', '正面提及量', '负面提及量', '体验值']
  },
  grid: {
    top: 55,
    right: 10,
    bottom: 40,
    left: 10,
    containLabel: true
  },
  tooltip: {
    trigger: 'axis',
    padding: 0,
    axisPointer: {
      type: 'cross',
      shadowStyle: {
        color: 'rgba(41, 148, 255, 0.1)'
      }
    },
    borderColor: '#0077FF',
    formatter: tooltipFormatter // 使用自定义格式化函数
  },
  xAxis: {
    type: 'category',
    data: ['2024-01-01', '2024-01-02', '2024-01-03'],
    axisLabel: {
      margin: 20,
      fontSize: 14,
      rotate: 0,
      width: 100
    },
    triggerEvent: true,
    axisPointer: { type: 'shadow' },
    splitLine: { show: true },
    axisTick: { show: false },
    axisLine: { show: false },
    nameTextStyle: { color: 'rgba(0,0,0,0.45)' }
  },
  yAxis: [
    {
      name: '提及量',
      show: true,
      axisLabel: {
        show: true,
        formatter: function (value) {
          return formatUtils.makeDataUnit(value)
        }
      },
      splitLine: {
        show: true,
        lineStyle: { color: '#F0F0F0' }
      },
      splitArea: {
        interval: 1,
        show: true,
        areaStyle: {
          color: ['rgba(255, 255, 255, 0)', 'rgba(250, 250, 250, 1)']
        }
      },
      axisTick: { show: false },
      axisLine: { show: false },
      nameTextStyle: { color: 'rgba(0,0,0,0.45)' }
    },
    {
      name: '体验值',
      show: true,
      axisLabel: {
        show: true,
        formatter: function (value) {
          return value
        }
      },
      splitLine: {
        show: true,
        lineStyle: { color: '#F0F0F0' }
      },
      splitArea: {
        interval: 1,
        show: true,
        areaStyle: {
          color: ['rgba(255, 255, 255, 0)', 'rgba(250, 250, 250, 1)']
        }
      },
      axisTick: { show: false },
      axisLine: { show: false },
      nameTextStyle: { color: 'rgba(0,0,0,0.45)' }
    }
  ],
  series: [
    {
      name: '中性提及量',
      type: 'bar',
      stack: 'total',
      data: [800, 750, 820],
      itemStyle: {
        borderRadius: 1,
        borderColor: '#fff',
        borderWidth: 0.3,
        emphasis: {
          shadowBlur: 10,
          shadowColor: '#0077FF'
        }
      },
      barMaxWidth: 25
    },
    {
      name: '正面提及量',
      type: 'bar',
      stack: 'total',
      data: [1200, 1100, 1300],
      itemStyle: {
        borderRadius: 1,
        borderColor: '#fff',
        borderWidth: 0.3,
        emphasis: {
          shadowBlur: 10,
          shadowColor: '#0077FF'
        }
      },
      barMaxWidth: 25
    },
    {
      name: '负面提及量',
      type: 'bar',
      stack: 'total',
      data: [-400, -350, -450], // 注意：负面数据为负值
      itemStyle: {
        borderRadius: 1,
        borderColor: '#fff',
        borderWidth: 0.3,
        emphasis: {
          shadowBlur: 10,
          shadowColor: '#0077FF'
        }
      },
      barMaxWidth: 25
    },
    {
      name: '体验值',
      type: 'line',
      yAxisIndex: 1,
      data: [75.6, 76.2, 74.8],
      smooth: true,
      lineStyle: {
        color: '#5D7092'
      },
      itemStyle: {
        color: '#5D7092'
      }
    }
  ]
}

// 创建图表实例
const chart = echarts.init(document.getElementById('chartContainer'))
chart.setOption(chartOption)

// 监听窗口大小变化
window.addEventListener('resize', () => {
  chart.resize()
})

// 监听点击事件
chart.on('click', params => {
  if (params.componentType === 'series') {
    // 处理系列点击
    console.log('点击系列:', params.seriesName, '数据:', params.name)
  } else if (params.componentType === 'xAxis') {
    // 处理X轴点击
    console.log('点击X轴:', params.value)
  }
})
```

### Tooltip格式化函数示例

```javascript
// 完整的tooltip格式化函数
function tooltipFormatter(value) {
  value = JSON.parse(JSON.stringify(value))

  // 定义系列显示顺序
  var order = ['正面提及量', '中性提及量', '负面提及量']
  var attrName = '体验值' // 可根据实际情况设置
  if (attrName) {
    order.unshift(attrName)
  }

  var StrArr = []
  let total = 0
  var axisValueLabel = ''

  // 开始构建HTML结构
  var str = '<div class="public-tooltip-div">'

  for (var i = 0; i < value.length; i++) {
    // 日期标签（只显示一次）
    axisValueLabel = '<div class="axis-name">' + value[i].axisValueLabel + '</div>'

    // 根据系列名称确定显示顺序
    var index = order.indexOf(value[i].seriesName)

    // 构建每个系列的数据行
    StrArr[index] =
      '<div class="each-series"><span class="each-series-name">' +
      value[i].marker +
      value[i].seriesName +
      '：</span>'

    // 根据系列类型格式化数值
    var showData
    if (value[i].seriesName == '体验值') {
      showData = formatUtils.formatNum(value[i].value)
    } else if (value[i].seriesName == '负面提及率') {
      showData = formatUtils.formatPercent(value[i].value) + '%'
    } else {
      showData = formatUtils.makeDataUnit(value[i].value)
    }

    // 计算总提及量（只计算提及量系列，不包括体验值）
    total +=
      value[i].seriesName == '正面提及量' ||
      value[i].seriesName == '中性提及量' ||
      value[i].seriesName == '负面提及量'
        ? Math.abs(value[i].value)
        : 0

    StrArr[index] += '<span class="each-series-value">' + showData + '</span></div>'
  }

  // 拼接所有内容
  str += axisValueLabel + StrArr.join('')

  // 添加总提及量
  str +=
    '<div class="each-series"><span class="each-series-name">&nbsp;&nbsp;&nbsp;总提及量：</span>' +
    '<span class="each-series-value">' +
    formatUtils.makeDataUnit(total) +
    '</span></div></div>'

  // 如果需要显示点击提示
  var needDetails = true // 可根据实际情况设置
  if (needDetails) {
    str += '<div class="public-tooltip-click-tips">点击图形可查看分析</div>'
  }

  return str
}
```

## 注意事项

1. **数据格式**: 确保输入数据包含所有必需字段
2. **负值处理**: 负面提及量必须转为负值才能正确显示
3. **日期格式**: 日期格式应为 'YYYY-MM-DD'
4. **颜色一致性**: 严格按照颜色规范设置，保持视觉一致性
5. **交互体验**: 确保所有交互功能正常工作
6. **性能考虑**: 大数据量时考虑数据分页或虚拟滚动

## 兼容性要求

- 支持现代浏览器（Chrome、Firefox、Safari、Edge）
- 支持移动端响应式显示
- 兼容ECharts 4.x及以上版本
