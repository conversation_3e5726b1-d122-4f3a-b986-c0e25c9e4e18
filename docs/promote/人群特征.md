# 人群特征组件实现指南

## 📋 组件概述

**人群特征组件**用于展示用户群体特征分析，采用左右对称的固定布局，中间展示人群总数，两侧显示各类特征分布。

### 参考组件

- **路径**: `docs/exampleCode/meicloud-changan-voc-main_v3/src/components/pages_components/voc_board/insights/populationCharacteristics.vue`
- **功能**: 展示人群特征分析数据，包括性别、年龄、客户类型等维度的分布情况

## 🎯 功能特性

### 核心功能

1. **人群总数展示**: 中央显示总人数，带有人群图标
2. **特征分布展示**: 左右两侧展示各类特征的主要分布
3. **详细列表**: 底部展示各维度的TOP5详细数据
4. **颜色标识**: 每个维度使用不同颜色进行区分
5. **固定布局**: 采用固定的左右对称布局设计

### 交互功能

- 加载状态显示
- 空数据状态处理
- 工具提示显示

## 📊 数据结构

### 输入数据格式

```typescript
interface PopulationData {
  total: number // 人群总数
  details: Array<{
    type: string // 特征类型（如：性别、年龄、客户类型）
    secondType: string // 具体分类（如：男性、25-35岁、VIP客户）
    mentionRate: number // 提及率（0-1之间的小数）
  }>
}
```

### 示例数据

```typescript
const sampleData = {
  total: 125000,
  details: [
    { type: '性别', secondType: '男性', mentionRate: 0.65 },
    { type: '性别', secondType: '女性', mentionRate: 0.35 },
    { type: '年龄', secondType: '25-35岁', mentionRate: 0.45 },
    { type: '年龄', secondType: '35-45岁', mentionRate: 0.32 },
    { type: '客户类型', secondType: 'VIP客户', mentionRate: 0.28 },
    { type: '客户类型', secondType: '普通客户', mentionRate: 0.72 }
  ]
}
```

## 🏗️ 技术实现

### 组件结构

```
src/components/PopulationCharacteristics/
├── index.vue          # 主组件文件
├── types.d.ts         # TypeScript类型定义
└── README.md          # 组件说明文档
```

### 依赖工具函数

1. **格式化函数**: 从ShowCompare组件中提取formatPercent、Thousandth等函数到utils中
2. **样式系统**: 使用项目统一的SCSS样式规范

### 关键技术点

1. **Vue 3 Composition API**: 使用`<script setup>`语法
2. **TypeScript**: 完整的类型定义和类型检查
3. **Element Plus**: 使用Tooltip、Loading等组件
4. **SCSS**: 使用项目统一的样式变量和规范

## 🎨 样式设计

### 布局结构

- **整体布局**: 左右对称的三栏固定布局
- **中央区域**: 人群图标 + 总数显示 + 装饰性圆圈
- **左右区域**: 特征分布卡片，带颜色标识
- **底部区域**: 详细列表，按维度分组显示

### 颜色方案

```scss
$colors: ('#DAF0FF', '#C7CEDA', '#CFF4EA', '#DACEED', '#C7CEDA', '#CFF4EA');

$borderColors: ('#0077FF', '#5D7092', '#3ED4A9', '#BA70CA', '#C7CEDA', '#CFF4EA');
```

## 📝 实现步骤

### 第一步：创建组件目录结构

```bash
mkdir -p src/components/PopulationCharacteristics
```

### 第二步：创建类型定义文件

- 定义Props、Events、Instance接口
- 定义数据结构类型

### 第三步：实现主组件

- 使用Vue 3 Composition API
- 实现数据格式化逻辑（调用utils中的formatPercent、Thousandth等）
- 实现布局和样式

### 第四步：提取工具函数

- 将ShowCompare组件中的formatPercent、Thousandth等函数提取到src/utils/index.ts
- 主组件直接import使用

### 第五步：集成到项目

- 更新组件导出文件
- 添加路由配置（如有需要）
- 测试功能完整性

## 🔧 注意事项

### 技术适配

1. **Vue 2 → Vue 3**: 注意语法差异，使用Composition API
2. **Less → SCSS**: 样式文件需要转换格式
3. **JavaScript → TypeScript**: 添加完整的类型定义
4. **Element UI → Element Plus**: 注意组件API变化

### 性能优化

1. **数据缓存**: 对格式化后的数据进行缓存
2. **虚拟滚动**: 大量数据时使用虚拟滚动
3. **懒加载**: 图片资源使用懒加载
4. **防抖处理**: 窗口大小变化时防抖处理

### 兼容性考虑

1. **浏览器兼容**: 支持主流浏览器
2. **无障碍访问**: 添加ARIA标签

## 📚 相关文档

- [Vue 3 开发规范](../framework/vue3-development-guide.md)
- [组件开发标准](../framework/component-development-standards.md)
- [样式规范](../framework/style-guide.md)
- [TypeScript 类型定义](../framework/typescript-types.md)

## 🎯 验收标准

### 功能验收

- [ ] 数据展示正确，格式符合要求
- [ ] 加载状态和空数据状态正确显示
- [ ] 工具提示功能正常

### 代码验收

- [ ] 使用Vue 3 Composition API
- [ ] TypeScript类型定义完整
- [ ] 代码注释清晰，符合规范
- [ ] 样式使用SCSS，符合项目规范

### 性能验收

- [ ] 组件渲染性能良好
- [ ] 内存使用合理
- [ ] 无内存泄漏问题

# 接口联调

## 文件路径

- 主页面：src/views/home/<USER>
- API定义：src/api/common/index.ts
- 类型定义：src/api/common/index.d.ts
- mock文件：src/mock/index.ts
- 人群特征组件：src/components/PopulationCharacteristics/index.vue

1. 数据迁移及mock数据

- 在主页面将populationData数据迁移至mock页面并命名populationDataMock
- populationData使用“PopulationCharacteristicsResponse”类型
- 将mock数据中的populationDataMock，根据PopulationCharacteristicsResponse类型重构数据结构

2. 在主页面封装函数，调用getDemographics方法获取数据，数据为空或异常时使用mock数据

3. 更新PopulationCharacteristics组件中的props入参类型，及根据PopulationCharacteristicsResponse类型更新数据处理逻辑、字段
