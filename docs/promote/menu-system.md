# 菜单系统使用指南

## 概述

本项目的菜单系统采用动态路由生成的方式，通过读取路由配置自动生成菜单，避免了硬编码菜单数据的问题。

## 核心特性

- ✅ **动态生成**: 根据路由配置自动生成菜单
- ✅ **权限控制**: 支持基于权限的菜单过滤
- ✅ **类型安全**: 完整的 TypeScript 类型支持
- ✅ **灵活配置**: 通过路由 meta 信息控制菜单显示

## 路由配置规范

### 基础配置

在路由配置中，通过 `meta` 属性控制菜单的显示：

```typescript
{
  path: '/system',
  name: 'System',
  meta: {
    title: '系统管理',     // 菜单标题
    icon: 'Setting',      // 菜单图标
    hidden: false,        // 是否隐藏菜单项
    permission: 'system'  // 权限标识（可选）
  },
  children: [
    {
      path: '/system/user',
      name: 'UserManagement',
      component: () => import('@/views/system/user/index.vue'),
      meta: {
        title: '用户管理',
        icon: 'User',
        permission: 'system:user'
      }
    }
  ]
}
```

### Meta 属性说明

| 属性         | 类型    | 必填 | 说明                         |
| ------------ | ------- | ---- | ---------------------------- |
| `title`      | string  | 是   | 菜单显示标题                 |
| `icon`       | string  | 否   | Element Plus 图标名称        |
| `hidden`     | boolean | 否   | 是否在菜单中隐藏，默认 false |
| `permission` | string  | 否   | 权限标识，用于权限控制       |

### 特殊路由处理

以下路由会被自动过滤，不会显示在菜单中：

- `/` (根路径)
- `/login` (登录页)
- `/404` (错误页)
- `meta.hidden: true` 的路由

## 使用方法

### 1. 基础菜单生成

```typescript
import { generateMenuFromRoutes } from '@/utils/menu'
import router from '@/router'

// 生成菜单数据
const menuItems = generateMenuFromRoutes(router.getRoutes())
```

### 2. 权限过滤菜单

```typescript
import { filterMenuByPermissions } from '@/utils/menu'

// 用户权限列表
const userPermissions = ['system', 'system:user', 'data:analysis']

// 过滤后的菜单
const filteredMenu = filterMenuByPermissions(menuItems, userPermissions)
```

### 3. 在组件中使用

```vue
<script setup lang="ts">
import { computed } from 'vue'
import { generateMenuFromRoutes } from '@/utils/menu'
import router from '@/router'

// 动态生成菜单
const menuItems = computed(() => {
  const routes = router.getRoutes()
  return generateMenuFromRoutes(routes)
})
</script>
```

## 菜单组件

### Menu.vue 组件

`src/layout/components/Menu.vue` 是主要的菜单组件，具有以下特性：

- 自动读取路由配置生成菜单
- 支持多级菜单嵌套
- 响应式设计，支持折叠/展开
- 自动高亮当前激活菜单项

### 组件属性

| 属性             | 类型    | 默认值 | 说明                 |
| ---------------- | ------- | ------ | -------------------- |
| `default-active` | string  | -      | 当前激活的菜单项     |
| `collapse`       | boolean | false  | 是否折叠菜单         |
| `unique-opened`  | boolean | true   | 是否只展开一个子菜单 |

## 扩展功能

### 1. 添加新菜单项

只需要在路由配置中添加新的路由即可：

```typescript
// 在 src/router/index.ts 中添加
{
  path: '/new-feature',
  name: 'NewFeature',
  component: () => import('@/views/new-feature/index.vue'),
  meta: {
    title: '新功能',
    icon: 'Star'
  }
}
```

### 2. 自定义菜单过滤逻辑

可以扩展 `filterMenuRoutes` 函数来实现自定义的过滤逻辑：

```typescript
// 在 src/utils/menu.ts 中修改
export const filterMenuRoutes = (routes: RouteRecordRaw[]): MenuItem[] => {
  // 添加自定义过滤逻辑
  return routes.filter(route => {
    // 你的过滤条件
    return true
  })
}
```

### 3. 权限系统集成

菜单系统已经预留了权限控制的接口，可以轻松集成权限系统：

```typescript
// 在用户登录后获取权限
const userPermissions = await getUserPermissions()

// 过滤菜单
const authorizedMenu = filterMenuByPermissions(menuItems, userPermissions)
```

## 最佳实践

1. **路由命名规范**: 使用 PascalCase 命名路由
2. **图标选择**: 优先使用 Element Plus 内置图标
3. **权限设计**: 权限标识使用层级结构，如 `system:user:create`
4. **组件懒加载**: 所有页面组件都应该使用懒加载
5. **类型安全**: 充分利用 TypeScript 类型检查

## 常见问题

### Q: 如何隐藏某个菜单项？

A: 在路由的 meta 中设置 `hidden: true`

### Q: 如何添加权限控制？

A: 在路由的 meta 中设置 `permission` 属性，然后使用 `filterMenuByPermissions` 函数

### Q: 如何自定义菜单图标？

A: 在路由的 meta 中设置 `icon` 属性，使用 Element Plus 图标名称

### Q: 菜单不显示怎么办？

A: 检查路由配置是否正确，确保有 `component` 或 `children` 属性
