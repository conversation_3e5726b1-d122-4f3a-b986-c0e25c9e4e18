# 数据字典维护功能 - 优化提示词

## 📋 功能概述

在数据管理模块下实现完整的数据字典维护系统，包含字典类型管理和字典项明细管理两级架构。

## 🎯 核心需求

### 主要功能模块

1. **数据字典类型管理** - 字典主表的增删改查
2. **数据字典项管理** - 字典明细项的增删改查
3. **双层级联操作** - 点击字典类型查看对应明细项

### UI界面设计要求

- **主界面布局**：左侧字典类型列表，右侧字典项详情面板
- **操作方式**：表格行操作 + 弹窗编辑
- **交互特性**：点击字典名称展开右侧面板显示明细项
- **响应式设计**：适配不同屏幕尺寸

## 🔧 技术实现规范

### 目录结构

```
src/
├── views/data/dictionary/           # 数据字典页面
│   ├── index.vue                   # 主页面
│   ├── components/
│   │   ├── DictTypeTable.vue       # 字典类型表格
│   │   ├── DictItemPanel.vue       # 字典项面板
│   │   ├── DictFormDialog.vue      # 字典类型表单弹窗
│   │   └── DictItemFormDialog.vue  # 字典项表单弹窗
│   └── api.ts                      # 页面专用API
├── api/dictionary/                 # API接口封装
│   ├── index.ts                    # 接口实现
│   └── index.d.ts                  # 类型定义
```

### 数据模型设计

#### 字典类型模型 (DictType)

```typescript
interface DictType {
  id?: string // 字典ID
  dictName: string // 字典名称 *必填
  dictCode: string // 字典编码 *必填，唯一
  type: 0 | 1 | 2 // 数据类型：0-string，1-number，2-boolean
  description?: string // 字典描述
  operator?: string // 创建人
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}
```

#### 字典项模型 (DictItem)

```typescript
interface DictItem {
  id?: string // 字典项ID
  dictId: string // 字典ID *关联字段
  itemText: string // 显示文本 *必填
  itemKey: string // 字典项键 *必填
  itemValue: string // 字典项值 *必填
  description?: string // 描述信息
  sortOrder: number // 排序值 *必填，默认1
  status: 0 | 1 // 启用状态：1-启用，0-停用
  operator?: string // 操作人
  createTime?: string // 创建时间
}
```

### API接口映射

#### 字典类型接口

```typescript
// 查询字典列表
POST /insDict/dict-list
// 新增字典
POST /insDict/insert
// 更新字典
POST /insDict/update
// 删除字典
DELETE /insDict/delete/{id}
// 批量删除字典
POST /insDict/batch-delete
```

#### 字典项接口

```typescript
// 查询字典项列表
POST /insDictItem/dict-item-list
// 新增字典项
POST /insDictItem/insert
// 更新字典项
POST /insDictItem/update
// 删除字典项
DELETE /insDictItem/delete/{id}
```

## 🎨 UI交互设计

### 主界面布局

- **顶部搜索区**：字典名称、字典编号输入框 + 查询/重置按钮
- **左侧表格区**：数据字典类型列表，支持分页
- **右侧面板区**：选中字典的明细项管理（可折叠）

### 表格功能特性

- ✅ **可点击行**：点击表格行选中并展开右侧面板
- ✅ **操作列**：编辑、查看、删除按钮
- ✅ **状态显示**：字典类型标签化显示
- ✅ **分页控制**：支持每页条数切换

### 弹窗表单设计

- **字典类型表单**：名称、编码、类型选择、描述
- **字典项表单**：名称、数据值、描述、排序值、启用状态切换
- **表单验证**：必填校验、编码唯一性校验、数值范围校验

## 💡 开发提示

### 状态管理建议

```typescript
// 使用 reactive 管理复杂表单状态
const dictFormState = reactive({
  visible: false,
  mode: 'add' as 'add' | 'edit',
  formData: {} as DictType,
  loading: false
})

// 选中状态管理
const selectedDict = ref<DictType | null>(null)
const dictItemList = ref<DictItem[]>([])
```

### 关键交互逻辑

1. **联动加载**：选择字典类型时自动加载对应明细项
2. **数据同步**：新增/编辑字典项后刷新右侧面板
3. **状态保持**：切换字典类型时保持右侧面板展开状态

### 性能优化

- 字典项列表使用虚拟滚动（当数据量大时）
- 防抖搜索避免频繁请求
- 缓存已加载的字典项数据

## 📋 验收标准

### 功能完整性

- ✅ 字典类型的完整CRUD操作
- ✅ 字典项的完整CRUD操作
- ✅ 搜索过滤和分页功能
- ✅ 表单验证和错误提示
- ✅ 数据联动显示

### 用户体验

- ✅ 操作流畅，响应及时
- ✅ 界面美观，符合设计规范
- ✅ 错误提示明确，用户友好
- ✅ 支持键盘快捷操作

### 代码质量

- ✅ TypeScript 类型完整
- ✅ 代码结构清晰，组件复用性好
- ✅ 接口封装规范，错误处理完善
- ✅ 遵循项目编码规范

---

## 接口文档

文件目录： docs/api/default_OpenAPI.json

1. 读取接口文档： 数据字典项管理模块为数据字典类型。数据字典项管理模块为数据类型对应的明细
2. 在src/api目录下新增目录，创建接口文件与类型文件，
3. 根据文档实现对应接口了入参，返回值类型
