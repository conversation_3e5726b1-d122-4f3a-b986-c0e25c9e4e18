# 布局系统使用指南

## 🎯 概述

本项目的布局系统基于 Vue 3 + TypeScript + Element Plus 构建，提供了完整的后台管理系统布局解决方案。

## 📐 布局结构

### 整体布局

```
┌─────────────┬───────────────────────────────────────────┐
│             │              Header (顶部导航栏)            │
│   Sidebar   ├───────────────────────────────────────────┤
│  (侧边栏)    │                                           │
│             │              Main Content                 │
│  - Logo     │              (主内容区)                   │
│  - Menu     │                                           │
│             │                                           │
└─────────────┴───────────────────────────────────────────┘
```

### 组件结构

```
src/layout/
├── index.vue              # 主布局组件
├── components/
│   ├── Sidebar.vue        # 侧边栏组件
│   ├── Header.vue         # 顶部导航组件
│   └── Menu.vue           # 菜单组件
```

## 🎨 设计规范

### 颜色规范

```scss
// 布局颜色
$sidebar-bg: #212b36; // 侧边栏背景色
$header-bg: #212b36; // 顶部导航背景色
$content-bg: #f5f5f5; // 主内容区背景色

// 主色调
$primary-color: #409eff; // 主色
$text-white: #ffffff; // 白色文字
```

### 尺寸规范

- **侧边栏宽度**: 240px (展开) / 64px (折叠)
- **顶部导航高度**: 60px
- **Logo区域高度**: 60px
- **菜单项高度**: 48px

## 🔧 功能特性

### 1. 侧边栏功能

- ✅ **Logo显示**: 支持Logo图片和系统名称显示
- ✅ **菜单导航**: 支持多级菜单结构
- ✅ **折叠功能**: 支持侧边栏折叠/展开
- ✅ **菜单高亮**: 自动高亮当前激活菜单
- ✅ **滚动优化**: 自定义滚动条样式

### 2. 顶部导航功能

- ✅ **折叠按钮**: 控制侧边栏折叠状态
- ✅ **面包屑导航**: 显示当前页面路径
- ✅ **用户信息**: 显示当前用户信息
- ✅ **用户菜单**: 支持个人中心、设置、退出登录

### 3. 主内容区功能

- ✅ **路由渲染**: 自动渲染当前路由组件
- ✅ **滚动优化**: 自定义滚动条样式
- ✅ **响应式布局**: 适配不同屏幕尺寸

## 📝 使用方法

### 1. 路由配置

```typescript
// src/router/index.ts
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('@/layout/index.vue'),
    redirect: '/home',
    children: [
      {
        path: '/home',
        name: 'Home',
        component: () => import('@/views/home/<USER>'),
        meta: { title: '首页', icon: 'House' }
      },
      {
        path: '/system',
        name: 'System',
        meta: { title: '系统管理', icon: 'Setting' },
        children: [
          {
            path: '/system/user',
            name: 'UserManagement',
            component: () => import('@/views/system/user/index.vue'),
            meta: { title: '用户管理', icon: 'User' }
          }
        ]
      }
    ]
  }
]
```

### 2. 状态管理

```typescript
// 使用 Pinia 管理布局状态
import { useAppStore } from '@/store/modules/app'

const appStore = useAppStore()

// 切换侧边栏折叠状态
appStore.toggleCollapse()

// 设置折叠状态
appStore.setIsCollapse(true)
```

### 3. 菜单配置

菜单数据在 `src/layout/components/Menu.vue` 中配置：

```typescript
const menuItems: MenuItem[] = [
  {
    path: '/home',
    name: 'Home',
    meta: { title: '首页', icon: 'House' }
  },
  {
    path: '/system',
    name: 'System',
    meta: { title: '系统管理', icon: 'Setting' },
    children: [
      {
        path: '/system/user',
        name: 'UserManagement',
        meta: { title: '用户管理', icon: 'User' }
      }
    ]
  }
]
```

## 🎨 样式定制

### 1. 全局变量

在 `src/styles/_variables.scss` 中定义布局相关变量：

```scss
// 布局系统变量
$sidebar-bg: #212b36;
$header-bg: #212b36;
$content-bg: #f5f5f5;

// 间距变量
$spacing-md: 16px;
$spacing-lg: 24px;

// 圆角变量
$border-radius: 6px;
```

### 2. 组件样式

每个布局组件都使用 BEM 命名规范：

```scss
.sidebar {
  &__logo {
    // Logo 样式
  }

  &__menu {
    // 菜单样式
  }

  &--collapsed {
    // 折叠状态样式
  }
}
```

## 🔧 扩展功能

### 1. 添加新页面

1. 在 `src/views/` 下创建页面组件
2. 在路由配置中添加路由
3. 在菜单配置中添加菜单项

### 2. 自定义菜单图标

使用 Element Plus 图标：

```vue
<el-icon>
  <House />
</el-icon>
```

### 3. 权限控制

可以在路由守卫中添加权限验证：

```typescript
router.beforeEach((to, from, next) => {
  // 检查用户权限
  if (to.meta.requiresAuth && !isAuthenticated()) {
    next('/login')
  } else {
    next()
  }
})
```

## 🚀 性能优化

### 1. 组件懒加载

```typescript
// 路由懒加载
const UserManagement = () => import('@/views/system/user/index.vue')
```

### 2. 状态管理优化

```typescript
// 使用 shallowRef 优化大型对象
const chartConfig = shallowRef({
  // 大量配置数据
})
```

### 3. 样式优化

- 使用 CSS 变量提高性能
- 避免深层嵌套选择器
- 合理使用 `:deep()` 选择器

## 📋 检查清单

### 基础功能

- [x] 布局组件创建完成
- [x] 侧边栏和顶部导航实现
- [x] 路由配置使用布局
- [x] Logo资源添加
- [x] 基础样式实现

### 交互功能

- [x] 菜单高亮显示
- [x] 侧边栏折叠功能
- [x] 面包屑导航
- [x] 用户菜单下拉

### 样式优化

- [x] 响应式设计
- [x] 自定义滚动条
- [x] 过渡动画效果
- [x] 主题色彩统一

## 🔗 相关文档

- [项目开发指南](../project-guide.md)
- [路由配置说明](../router-guide.md)
- [状态管理指南](../store-guide.md)
- [样式规范文档](../style-guide.md)
