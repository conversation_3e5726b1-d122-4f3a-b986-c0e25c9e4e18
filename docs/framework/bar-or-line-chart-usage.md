# BarOrLineChart 组件使用指南

## 组件概述

`BarOrLineChart` 是一个基于 ECharts 的柱状图和折线图组合组件，专门用于展示 VOC（Voice of Customer）数据趋势。该组件支持柱状图堆叠显示（正面、中性、负面提及量）和折线图显示（体验值），具有完整的交互功能和数据格式化能力。

## 功能特性

- ✅ **双图表类型**: 柱状图堆叠 + 折线图组合
- ✅ **双Y轴支持**: 独立的提及量和体验值坐标轴
- ✅ **数据格式化**: 自动单位转换（万、千万、亿等）
- ✅ **交互功能**: 点击事件、悬停提示、详情查看
- ✅ **响应式设计**: 支持自定义尺寸和自适应布局
- ✅ **完整类型**: TypeScript 类型定义支持

## 基础用法

### 1. 导入组件

```vue
<script setup lang="ts">
import BarOrLineChart from '@/components/Charts/BarOrLineChart/index.vue'
</script>
```

### 2. 准备数据

```typescript
const chartData = ref([
  {
    keyWord: '2024-01',
    positiveMentionValue: 1200, // 正面提及量
    neutralMentionValue: 800, // 中性提及量
    negativeMentionValue: -300, // 负面提及量（负值）
    experienceValue: 85.5 // 体验值
  },
  {
    keyWord: '2024-02',
    positiveMentionValue: 1500,
    neutralMentionValue: 900,
    negativeMentionValue: -200,
    experienceValue: 88.2
  }
  // ... 更多数据
])
```

### 3. 基础使用

```vue
<template>
  <BarOrLineChart :data="chartData" width="100%" height="400" @bar-click="handleBarClick" />
</template>

<script setup lang="ts">
const handleBarClick = (payload: { date: string }) => {
  console.log('点击了:', payload.date)
}
</script>
```

## Props 参数

| 参数          | 类型               | 默认值   | 说明             |
| ------------- | ------------------ | -------- | ---------------- |
| `data`        | `ChartDataItem[]`  | -        | 图表数据（必填） |
| `width`       | `string \| number` | `'100%'` | 图表宽度         |
| `height`      | `string \| number` | `400`    | 图表高度         |
| `needDetails` | `boolean`          | `false`  | 是否显示详情提示 |

### 数据类型定义

```typescript
interface ChartDataItem {
  keyWord: string // X轴标签（时间/关键词）
  positiveMentionValue: number // 正面提及量
  neutralMentionValue: number // 中性提及量
  negativeMentionValue: number // 负面提及量
  experienceValue: number // 体验值
}
```

## 事件

| 事件名      | 参数               | 说明             |
| ----------- | ------------------ | ---------------- |
| `bar-click` | `{ date: string }` | 点击柱状图时触发 |

## 高级用法

### 1. 自定义尺寸

```vue
<template>
  <!-- 固定像素尺寸 -->
  <BarOrLineChart :data="chartData" :width="600" :height="300" />

  <!-- 百分比尺寸 -->
  <BarOrLineChart :data="chartData" width="80%" height="500px" />
</template>
```

### 2. 启用详情模式

```vue
<template>
  <BarOrLineChart :data="chartData" :need-details="true" @bar-click="handleBarClick" />
</template>
```

### 3. 响应式布局

```vue
<template>
  <el-row :gutter="20">
    <el-col :span="12">
      <BarOrLineChart :data="chartData" width="100%" height="300" />
    </el-col>
    <el-col :span="12">
      <BarOrLineChart :data="chartData" width="100%" height="300" :need-details="true" />
    </el-col>
  </el-row>
</template>
```

### 4. 事件处理

```vue
<script setup lang="ts">
const handleBarClick = (payload: { date: string }) => {
  // 显示提示信息
  ElMessage.success(`点击了 ${payload.date} 的数据`)

  // 跳转到详情页
  router.push(`/detail/${payload.date}`)

  // 打开弹窗
  dialogVisible.value = true
  selectedDate.value = payload.date
}
</script>
```

## 样式定制

### 1. 容器样式

```vue
<template>
  <div class="chart-wrapper">
    <BarOrLineChart :data="chartData" width="100%" height="400" />
  </div>
</template>

<style scoped lang="scss">
.chart-wrapper {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.03);
  padding: 20px;
}
</style>
```

### 2. 全局样式覆盖

```scss
// 自定义 tooltip 样式
:deep(.echarts-tooltip) {
  background: #fff !important;
  border: 1px solid #0077ff !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

:deep(.public-tooltip-div) {
  padding: 15px;

  .axis-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
  }

  .each-series {
    margin-bottom: 8px;

    .each-series-name {
      color: #666;
    }

    .each-series-value {
      color: #333;
      font-weight: 500;
    }
  }
}
```

## 数据格式化

组件内置了智能的数据格式化功能：

### 数值单位转换

- `1,000` → `1K`
- `10,000` → `1万`
- `1,000,000` → `100万`
- `10,000,000` → `1千万`
- `100,000,000` → `1亿`

### 体验值格式化

- 保留两位小数
- 添加千分位分隔符

## 性能优化

### 1. 大数据量处理

```vue
<script setup lang="ts">
// 使用 shallowRef 优化大数据量
const chartData = shallowRef([])

// 异步加载数据
const loadData = async () => {
  const data = await fetchChartData()
  chartData.value = data
}
</script>
```

### 2. 组件懒加载

```vue
<script setup lang="ts">
// 按需导入组件
const BarOrLineChart = defineAsyncComponent(
  () => import('@/components/Charts/BarOrLineChart/index.vue')
)
</script>
```

## 常见问题

### Q1: 图表不显示数据？

**A**: 检查数据格式是否正确，确保 `data` 数组不为空且包含所有必需字段。

### Q2: 图表尺寸不正确？

**A**: 确保父容器有明确的尺寸，或者给图表设置固定的 `width` 和 `height`。

### Q3: 点击事件不触发？

**A**: 确保正确绑定了 `@bar-click` 事件，并且只对柱状图有效（折线图点击不会触发）。

### Q4: 数据格式化异常？

**A**: 确保数值字段为数字类型，字符串会被自动转换为数字。

## 完整示例

查看完整的使用示例：[barOrLineChart.vue](../../exampleCode/barOrLineChart.vue)

## 相关组件

- [BarAndPointChart](./bar-and-point-chart-usage.md) - 柱状图加散点图混合组件
- [VocTrendChart](../promote/VOC体验值趋势图表.md) - VOC 趋势图表组件

---

**更新时间**: 2024年12月
**版本**: 1.0.0
