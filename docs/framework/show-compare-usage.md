# ShowCompare 组件使用指南

## 组件介绍

`ShowCompare` 是一个用于显示数据环比/同比变化的组件，支持不同类型的指标展示，包括提及数、体验值、负面提及率等。

## 功能特性

- ✅ **多指标支持**: 支持提及数、体验值、负面提及率三种指标类型
- ✅ **智能颜色**: 根据数值正负自动显示不同颜色（绿色表示正向，红色表示负向）
- ✅ **方向指示**: 使用箭头图标直观显示变化方向
- ✅ **格式化显示**: 自动格式化为百分比并添加千分位分隔符
- ✅ **TypeScript 支持**: 完整的类型定义和类型检查
- ✅ **响应式设计**: 支持自定义样式类名

## 基本用法

```vue
<template>
  <div>
    <!-- 提及数环比 -->
    <ShowCompare compare-key="momTotalMentionValueRate" :compare-value="0.15" />

    <!-- 体验值环比 -->
    <ShowCompare compare-key="momExperienceValueRate" :compare-value="-0.08" />

    <!-- 负面提及率环比 -->
    <ShowCompare compare-key="momNegativeMentionRate" :compare-value="0.05" />
  </div>
</template>

<script setup lang="ts">
import { ShowCompare } from '@/components'
</script>
```

## Props 参数

| 参数           | 类型               | 默认值                       | 说明                   |
| -------------- | ------------------ | ---------------------------- | ---------------------- |
| `compareKey`   | `CompareKeyType`   | `'momTotalMentionValueRate'` | 比较键名，支持三种类型 |
| `compareValue` | `number \| string` | `''`                         | 比较数值，不用传百分比 |
| `customClass`  | `string`           | `''`                         | 自定义样式类名         |

### compareKey 支持的类型

```typescript
type CompareKeyType =
  | 'momTotalMentionValueRate' // 提及数环比
  | 'momExperienceValueRate' // 体验值环比
  | 'momNegativeMentionRate' // 负面提及率环比
```

## 显示效果

### 正值显示（绿色）

```
↗ 15.00 %
```

### 负值显示（红色）

```
↘ -8.00 %
```

### 零值显示（默认色）

```
0.00 %
```

## 样式定制

组件支持通过 `customClass` 属性添加自定义样式：

```vue
<template>
  <ShowCompare
    compare-key="momExperienceValueRate"
    :compare-value="0.12"
    custom-class="my-custom-style"
  />
</template>

<style scoped>
.my-custom-style {
  font-size: 16px;
  font-weight: bold;
}
</style>
```

## 数据格式化

组件内部会自动处理数据格式化：

1. **数值转换**: 自动将字符串转换为数字
2. **百分比转换**: 自动乘以100转换为百分比
3. **小数位控制**: 保留2位小数
4. **千分位分隔**: 使用中文千分位分隔符
5. **异常处理**: 无效数据显示为 "-"

## 注意事项

1. **数值格式**: 传入的 `compareValue` 应该是小数形式（如 0.15 表示 15%），组件会自动转换为百分比显示
2. **类型安全**: 建议使用 TypeScript 以获得完整的类型检查
3. **样式兼容**: 组件使用 SCSS 样式，确保项目支持 SCSS 预处理

## 完整示例

```vue
<template>
  <div class="data-comparison">
    <h3>数据环比分析</h3>

    <div class="comparison-item">
      <span class="label">提及数环比：</span>
      <ShowCompare compare-key="momTotalMentionValueRate" :compare-value="mentionRate" />
    </div>

    <div class="comparison-item">
      <span class="label">体验值环比：</span>
      <ShowCompare compare-key="momExperienceValueRate" :compare-value="experienceRate" />
    </div>

    <div class="comparison-item">
      <span class="label">负面提及率环比：</span>
      <ShowCompare compare-key="momNegativeMentionRate" :compare-value="negativeRate" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ShowCompare } from '@/components'

// 模拟数据
const mentionRate = ref(0.15)
const experienceRate = ref(-0.08)
const negativeRate = ref(0.05)
</script>

<style lang="scss" scoped>
.data-comparison {
  padding: 20px;

  .comparison-item {
    margin-bottom: 15px;
    display: flex;
    align-items: center;

    .label {
      margin-right: 10px;
      font-weight: 500;
    }
  }
}
</style>
```

## 版本历史

- **v2.0.0**: 重构为 Vue 3 Composition API + TypeScript
- **v1.0.0**: 初始版本，基于 Vue 2 Options API
