# BarAndPointChart 组件使用指南

## 概述

`BarAndPointChart` 是一个基于 ECharts 的柱状图加散点图组件，支持柱状图和折线图的混合展示。组件已重构为简化版本，所有配置项都集成在组件内部，与参考组件保持完全一致，只需要传入 `data`、`width`、`height`、`needDetails` 等核心 props 即可使用。

## 组件特性

- ✅ **简化 Props**: 只需要传入 4 个核心属性
- ✅ **响应式设计**: 支持固定尺寸和百分比宽度
- ✅ **交互功能**: 可选的点击详情和钻取功能
- ✅ **样式一致**: 保持与原有设计风格一致
- ✅ **TypeScript 支持**: 完整的类型定义

## Props 说明

| 属性名        | 类型                 | 默认值   | 说明                             |
| ------------- | -------------------- | -------- | -------------------------------- |
| `data`        | `object`             | -        | 图表数据，包含数据数组和配置信息 |
| `width`       | `string \| number`   | `'100%'` | 图表宽度，支持像素值或百分比     |
| `height`      | `string \| number`   | `400`    | 图表高度，支持像素值或百分比     |
| `needDetails` | `boolean`            | `false`  | 是否启用点击交互功能             |
| `yAxisName`   | `string \| string[]` | `''`     | Y轴名称，支持单轴或双轴配置      |
| `isTwoYaxis`  | `boolean`            | `false`  | 是否使用双Y轴                    |

## 数据结构

```typescript
interface ChartData {
  data: Array<Record<string, any>> // 数据数组
  xDataKey: string // X轴数据键名
  seriesDataKey: Array<{
    // 系列配置
    name: string // 系列名称
    key: string // 数据键名
    type?: 'bar' | 'scatter' | 'line' // 图表类型
  }>
  chart?: string // 图表类型标识
}
```

## 使用示例

### 基础用法

```vue
<template>
  <BarAndPointChart :data="chartData" :width="600" :height="400" />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import BarAndPointChart from '@/components/Charts/BarAndPointChart/index.vue'

const chartData = ref({
  data: [
    { month: '1月', 正面提及量: 1200, 中性提及量: 800, 负面提及量: 400, 体验值: 85 },
    { month: '2月', 正面提及量: 1400, 中性提及量: 900, 负面提及量: 300, 体验值: 88 },
    { month: '3月', 正面提及量: 1100, 中性提及量: 1000, 负面提及量: 500, 体验值: 82 }
  ],
  xDataKey: 'month',
  seriesDataKey: [
    { name: '正面提及量', key: '正面提及量', type: 'bar' },
    { name: '中性提及量', key: '中性提及量', type: 'bar' },
    { name: '负面提及量', key: '负面提及量', type: 'bar' },
    { name: '体验值', key: '体验值', type: 'line' }
  ]
})
</script>
```

### 带交互功能

```vue
<template>
  <BarAndPointChart
    :data="chartData"
    :width="600"
    :height="400"
    :needDetails="true"
    @seeDetail="handleSeeDetail"
    @drill="handleDrill"
  />
</template>

<script setup lang="ts">
// ... 数据定义同上

const handleSeeDetail = (data: { name: string; seriesName?: string }) => {
  console.log('查看详情:', data)
}

const handleDrill = (data: { name: string }) => {
  console.log('钻取数据:', data)
}
</script>
```

### 响应式布局

```vue
<template>
  <BarAndPointChart :data="chartData" width="100%" :height="300" />
</template>
```

### 双Y轴配置

```vue
<template>
  <BarAndPointChart
    :data="chartData"
    :width="600"
    :height="400"
    :isTwoYaxis="true"
    :yAxisName="['提及量', '体验值']"
  />
</template>
```

## 事件说明

当 `needDetails` 为 `true` 时，组件会触发以下事件：

### seeDetail

点击图表区域时触发，用于查看详情。

**参数:**

```typescript
{
  name: string        // 点击的X轴数据项
  seriesName?: string // 系列名称（可选）
}
```

### drill

点击图表区域时触发，用于数据钻取。

**参数:**

```typescript
{
  name: string // 点击的X轴数据项
}
```

## 样式特性

### 图表样式

- **颜色方案**: 使用预定义的 10 色配色方案
- **柱状图**: 圆角边框，最大宽度 25px
- **折线图**: 虚线样式，圆形标记点
- **网格**: 浅灰色分割线，交替背景色

### 交互样式

- **悬停效果**: 鼠标悬停时显示指针样式
- **激活状态**: 点击的X轴标签会高亮显示
- **Tooltip**: 十字准星指示器，蓝色边框

## 技术实现

### 核心功能

1. **数据处理**: 自动处理数据格式，支持缺失值
2. **图表渲染**: 基于 ECharts 5.x 版本
3. **事件系统**: 原生事件处理，高性能交互
4. **响应式**: 自动监听窗口大小变化

### 性能优化

- 组件卸载时自动清理事件监听
- 图表实例复用和销毁管理
- 数据变化时的智能重绘

## 注意事项

1. **数据格式**: 确保数据格式符合接口定义
2. **容器ID**: 组件会自动生成唯一的容器ID
3. **事件监听**: 只有在 `needDetails` 为 `true` 时才启用交互
4. **样式覆盖**: 使用 `:deep()` 选择器覆盖 Element Plus 样式

## 更新日志

### v2.0.0 (重构版本)

- ✅ 简化 Props 接口，只保留核心属性
- ✅ 移除复杂的配置选项，提供默认样式
- ✅ 优化 TypeScript 类型定义
- ✅ 保持与原有设计风格一致
- ✅ 提升组件易用性和维护性
