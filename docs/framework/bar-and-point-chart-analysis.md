# 柱状图加散点图组件分析文档

## 📊 组件概述

`barAndPointChart.vue` 是一个基于 ECharts 的复合图表组件，支持柱状图和散点图（线图）的混合展示。该组件主要用于数据可视化场景，特别适用于展示多维度数据的对比分析。

## 🎯 核心功能

### 1. 图表类型支持

- **柱状图**: 支持多系列柱状图展示
- **散点图/线图**: 支持散点图和虚线连接线
- **混合展示**: 可在同一图表中同时展示柱状图和线图

### 2. 交互功能

- **点击交互**: 支持点击图表区域进行数据钻取
- **悬停效果**: 鼠标悬停时显示详细数据
- **联动功能**: 支持与其他组件的数据联动
- **下载功能**: 支持图表图片下载

### 3. 样式特性

- **响应式设计**: 自适应容器大小
- **主题定制**: 支持自定义颜色和样式
- **网格系统**: 内置网格线和背景色
- **坐标轴**: 支持双Y轴配置

## 📋 组件接口

### Props 参数

| 参数名             | 类型         | 默认值                                                   | 说明                   |
| ------------------ | ------------ | -------------------------------------------------------- | ---------------------- |
| `data`             | Object       | `{data: [], xDataKey: '', seriesDataKey: [], chart: ''}` | 图表数据配置           |
| `divId`            | String       | `''`                                                     | 图表容器ID             |
| `xNameText`        | String       | `''`                                                     | X轴名称文本            |
| `transverse`       | Boolean      | `false`                                                  | 是否横向显示           |
| `xAxisName`        | String       | `''`                                                     | X轴名称                |
| `yAxisName`        | String/Array | `''`                                                     | Y轴名称                |
| `isTwoYaxis`       | Boolean      | `false`                                                  | 是否使用双Y轴          |
| `tooltipFormatter` | Function     | -                                                        | 自定义提示框格式化函数 |
| `needLinkage`      | Boolean      | `false`                                                  | 是否需要联动功能       |

### Events 事件

| 事件名      | 参数             | 说明         |
| ----------- | ---------------- | ------------ |
| `seeDetail` | `{name: string}` | 查看详情事件 |
| `drill`     | `{name: string}` | 数据钻取事件 |

### 数据格式

```typescript
interface ChartData {
  data: Array<{
    [key: string]: any // 动态数据字段
  }>
  xDataKey: string // X轴数据键名
  seriesDataKey: Array<{
    name: string // 系列名称
    key: string // 数据键名
    type?: 'bar' | 'scatter' | 'line' // 图表类型
  }>
  chart: string // 图表类型标识
}
```

## 🔧 技术实现

### 1. 数据处理流程

```typescript
// 1. 数据预处理
- 提取X轴数据
- 构建图例数据
- 配置系列样式

// 2. 系列数据处理
- 根据类型设置样式（柱状图/散点图）
- 数据格式化和排序
- 特殊处理（如散点图转为线图）

// 3. 图表配置生成
- 基础配置（颜色、图例、网格）
- 坐标轴配置
- 系列配置
- 交互配置
```

### 2. 样式配置

#### 柱状图样式

```javascript
{
  itemStyle: {
    borderRadius: 3,
    borderColor: '#fff',
    borderWidth: 0.5
  },
  barMaxWidth: 25
}
```

#### 散点图/线图样式

```javascript
{
  type: 'line',
  symbol: 'circle',
  smooth: true,
  symbolSize: 9,
  color: "#5D7092",
  lineStyle: {
    type: "dashed",
    color: '#5D7092'
  },
  itemStyle: {
    borderWidth: 2,
    color: 'rgba(93, 112, 146, .3)',
    borderColor: "#5D7092"
  }
}
```

### 3. 交互实现

#### 点击事件处理

```javascript
// 图表区域点击
chart.getZr().on('click', function (params) {
  if (chart.containPixel('grid', [params.offsetX, params.offsetY])) {
    // 获取点击位置对应的数据索引
    const xIndex = chart.convertFromPixel({ seriesIndex: 0 }, [params.offsetX, params.offsetY])[0]
    // 触发事件
    this.$emit('seeDetail', { name: xData })
    this.$emit('drill', { name: xData })
  }
})
```

#### 鼠标悬停效果

```javascript
// 鼠标悬停时改变光标样式
chart.getZr().on('mousemove', param => {
  if (chart.containPixel('grid', [param.offsetX, param.offsetY])) {
    chart.getZr().setCursorStyle('pointer')
  } else {
    chart.getZr().setCursorStyle('default')
  }
})
```

## 🎨 样式特性

### 1. 颜色配置

```javascript
color: [
  '#0077FF',
  '#3ED4A9',
  '#5D7092',
  '#FFC157',
  '#7163FD',
  '#95D8F2',
  '#BA70CA',
  '#FA9C78',
  '#11999C',
  '#FEBAD6'
]
```

### 2. 网格配置

```javascript
grid: {
  top: 55,
  right: 10,
  bottom: 40,
  left: 10,
  containLabel: true
}
```

### 3. 坐标轴样式

- **X轴**: 支持标签旋转、自定义格式化
- **Y轴**: 支持数值格式化、双Y轴配置
- **网格线**: 浅灰色网格线，交替背景色

## 📱 响应式特性

### 1. 自适应布局

- 图表自动适应容器大小
- 窗口大小变化时自动重绘

### 2. 标签自适应

- X轴标签根据数据量自动旋转
- 标签宽度自适应

### 3. 移动端适配

- 支持触摸交互
- 响应式网格配置

## 🔍 使用示例

### 基础用法

```vue
<template>
  <bar-and-point-chart
    :data="chartData"
    div-id="myChart"
    x-axis-name="月份"
    y-axis-name="数值"
    @see-detail="handleSeeDetail"
    @drill="handleDrill"
  />
</template>

<script>
export default {
  data() {
    return {
      chartData: {
        data: [
          { month: '1月', sales: 100, profit: 30 },
          { month: '2月', sales: 120, profit: 35 }
        ],
        xDataKey: 'month',
        seriesDataKey: [
          { name: '销售额', key: 'sales', type: 'bar' },
          { name: '利润率', key: 'profit', type: 'line' }
        ]
      }
    }
  },
  methods: {
    handleSeeDetail(data) {
      console.log('查看详情:', data)
    },
    handleDrill(data) {
      console.log('数据钻取:', data)
    }
  }
}
</script>
```

### 双Y轴配置

```vue
<template>
  <bar-and-point-chart
    :data="chartData"
    :is-two-yaxis="true"
    :y-axis-name="['销售额', '利润率']"
    div-id="dualAxisChart"
  />
</template>
```

## ⚠️ 注意事项

### 1. 性能优化

- 大数据量时建议使用数据分页
- 避免频繁更新图表配置
- 合理使用图表实例销毁和重建

### 2. 兼容性

- 需要 ECharts 5.x 版本
- 支持现代浏览器（IE11+）
- 移动端需要额外配置

### 3. 数据要求

- 数据格式必须符合接口定义
- 空值处理需要统一
- 时间格式需要标准化

## 🚀 改进建议

### 1. 代码优化

- 使用 Vue 3 Composition API 重构
- 添加 TypeScript 类型支持
- 优化变量命名和代码结构

### 2. 功能增强

- 添加更多图表类型支持
- 支持动态主题切换
- 增加数据导出功能

### 3. 性能提升

- 实现虚拟滚动
- 添加图表懒加载
- 优化渲染性能

## 📚 相关文档

- [ECharts 官方文档](https://echarts.apache.org/zh/index.html)
- [Vue 3 组件开发指南](https://vuejs.org/guide/)
- [项目开发规范](../project-guide.md)
