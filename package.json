{"name": "voc-std-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "lint": "eslint src", "lint:fix": "eslint src --fix", "format": "prettier --write \"src/**/*.{vue,ts,js,json,scss}\"", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.9.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.10.2", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21", "pinia": "^3.0.2", "remixicon": "^4.6.0", "vue": "^3.5.13", "vue-pdf": "^4.3.0", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/lodash-es": "^4.17.12", "@types/node": "^22.15.18", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vitest/coverage-v8": "3.2.4", "@vitest/ui": "^3.2.4", "@vue/test-utils": "^2.4.3", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-vue": "^10.0.0", "fast-glob": "^3.3.3", "globals": "^16.0.0", "happy-dom": "^12.10.3", "jsdom": "^23.0.1", "prettier": "^3.5.3", "sass": "^1.89.0", "typescript": "~5.8.3", "typescript-eslint": "^8.26.1", "vite": "^6.3.5", "vite-plugin-svg-icons": "^2.0.1", "vitest": "^3.2.4", "vue-tsc": "^2.2.8"}}