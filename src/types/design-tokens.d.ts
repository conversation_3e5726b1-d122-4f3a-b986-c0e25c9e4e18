/**
 * 设计规范相关的类型定义
 */

// 品牌色类型
export type BrandColor = 'primary' | 'secondary';

// 功能色类型
export type FunctionalColor = 'error' | 'warning' | 'success' | 'info';

// 功能色（浅色版本）类型
export type FunctionalColorLight = 'error-light' | 'warning-light' | 'success-light' | 'info-light';

// 文字色类型
export type TextColor = 
  | 'primary' 
  | 'secondary' 
  | 'tertiary' 
  | 'placeholder' 
  | 'link' 
  | 'danger' 
  | 'warning' 
  | 'success';

// 基础色类型
export type BaseColor = 'border-dark' | 'border-regular' | 'bg-regular' | 'bg-light';

// 中性色类型
export type NeutralColor = 
  | 'black' 
  | '900' 
  | '700' 
  | '500' 
  | '400' 
  | '300' 
  | '200' 
  | '100' 
  | '50' 
  | 'white';

// 图表色类型
export type ChartColor = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11;

// 字体大小类型
export type FontSize = 'h1' | 'h2' | 'h3' | 'h4' | 'body' | 'caption';

// 字重类型
export type FontWeight = 'thin' | 'normal' | 'medium' | 'semibold';

// 间距类型
export type Spacing = 0 | 1 | 2 | 4 | 6 | 8 | 10 | 12 | 16 | 24;

// 圆角类型
export type BorderRadius = 's' | 'm' | 'l' | 'xl' | 'c';

// 阴影类型
export type Shadow = 's' | 'm-left' | 'm-bottom' | 'l' | 'xl';

// 设计规范配置接口
export interface DesignTokens {
  // 品牌色
  brand: {
    primary: string;
    secondary: string;
  };
  
  // 功能色
  functional: {
    error: string;
    warning: string;
    success: string;
    info: string;
    errorLight: string;
    warningLight: string;
    successLight: string;
    infoLight: string;
  };
  
  // 文字色
  text: {
    primary: string;
    secondary: string;
    tertiary: string;
    placeholder: string;
    link: string;
    danger: string;
    warning: string;
    success: string;
  };
  
  // 基础色
  base: {
    borderDark: string;
    borderRegular: string;
    bgRegular: string;
    bgLight: string;
    disabled: string;
    mask: string;
  };
  
  // 中性色
  neutral: {
    black: string;
    900: string;
    700: string;
    500: string;
    400: string;
    300: string;
    200: string;
    100: string;
    50: string;
    white: string;
  };
  
  // 图表色
  chart: string[];
  
  // 字体
  font: {
    family: string;
    size: {
      h1: string;
      h2: string;
      h3: string;
      h4: string;
      body: string;
      caption: string;
    };
    lineHeight: {
      h1: string;
      h2: string;
      h3: string;
      h4: string;
      body: string;
      caption: string;
    };
    weight: {
      thin: number;
      normal: number;
      medium: number;
      semibold: number;
    };
  };
  
  // 间距
  spacing: {
    0: string;
    1: string;
    2: string;
    4: string;
    6: string;
    8: string;
    10: string;
    12: string;
    16: string;
    24: string;
  };
  
  // 圆角
  borderRadius: {
    s: string;
    m: string;
    l: string;
    xl: string;
    c: string;
  };
  
  // 阴影
  shadow: {
    s: string;
    mLeft: string;
    mBottom: string;
    l: string;
    xl: string;
  };
}

// 工具类名称类型
export type UtilityClassName = 
  | `text-${TextColor}`
  | `text-${FontSize}`
  | `font-${FontWeight}`
  | `bg-${BrandColor | FunctionalColor | FunctionalColorLight | BaseColor}`
  | `m-${Spacing}` | `mt-${Spacing}` | `mb-${Spacing}` | `ml-${Spacing}` | `mr-${Spacing}`
  | `p-${Spacing}` | `pt-${Spacing}` | `pb-${Spacing}` | `pl-${Spacing}` | `pr-${Spacing}`
  | `rounded-${BorderRadius}`
  | `shadow-${Shadow}`
  | `border-${BaseColor | BrandColor | FunctionalColor}`;

// 主题配置接口
export interface ThemeConfig {
  name: string;
  tokens: DesignTokens;
}

// 主题上下文接口
export interface ThemeContext {
  currentTheme: string;
  themes: Record<string, ThemeConfig>;
  setTheme: (themeName: string) => void;
  getToken: (path: string) => string;
}
