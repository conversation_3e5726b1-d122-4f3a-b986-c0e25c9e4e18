# 全局类型说明

本目录包含项目的全局类型定义，这些类型可以在整个项目中直接使用，无需导入。

## 全局类型

### BaseResponse\<T\>

通用API响应类型，用于统一所有接口的返回格式。

```typescript
// 直接使用，无需导入
const response: BaseResponse<UserInfo[]> = {
  success: true,
  message: '获取成功',
  code: 200,
  result: userList,
  tid: 'request-id-123'
}

// API函数返回值类型
export const getUserList = (): Promise<BaseResponse<UserInfo[]>> => {
  return http.get('/api/users')
}

// 处理响应数据
const handleResponse = (res: BaseResponse<any>) => {
  if (res.success) {
    console.log('成功:', res.result)
  } else {
    console.error('失败:', res.message)
  }
}
```

### ID

通用ID类型，支持字符串或数字。

```typescript
// 直接使用，无需导入
const userId: ID = '123' // 或者 123
const roleId: ID = 456
```

## 模块声明

文件还包含了以下模块的类型声明：

- Vue 单文件组件 (\*.vue)
- 图片资源 (_.png, _.jpg, \*.svg 等)
- CSS 模块 (_.module.css, _.module.scss)
- JSON 文件 (\*.json)

这些声明让 TypeScript 能够正确识别和处理这些文件类型。

## 注意事项

1. 全局类型定义在 `src/types/index.d.ts` 中
2. 不要在此文件中使用 `export` 语句，以保持全局性
3. 新增全局类型时，使用 `declare` 关键字
4. TypeScript 配置已包含此文件，确保全局类型生效
