// 布局系统变量
$sidebar-bg: #212b36;
$header-bg: #212b36;
$content-bg: #f5f5f5;
$bg-color: #f5f5f5;
$bg-color-secondary: #f9f9f9;

// 主色调
$primary-color: #409eff;
$color-primary: #409eff;
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;
$info-color: #909399;

// 文字颜色
$text-primary: #303133;
$text-color-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;
$text-color-secondary: #909399;
$text-white: #ffffff;

// 边框颜色
$border-color: #dcdfe6;
$border-light: #e4e7ed;

// 间距变量
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 圆角变量
$border-radius: 6px;
$border-radius-lg: 8px;

// 阴影变量
$box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
$box-shadow-light:
  0 2px 4px rgba(0, 0, 0, 0.12),
  0 0 6px rgba(0, 0, 0, 0.04);

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 13px;
$font-size-md: 14px;
$font-size-lg: 16px;
$font-size-xl: 18px;

// 过渡动画
$transition-duration: 0.3s;
$transition-timing: ease;
