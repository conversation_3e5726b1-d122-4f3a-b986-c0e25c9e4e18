// 使用SCSS循环生成间距工具类 (1-500px)
@for $i from 1 through 500 {
  .mt-#{$i} {
    margin-top: #{$i}px;
  }
  .mr-#{$i} {
    margin-right: #{$i}px;
  }
  .mb-#{$i} {
    margin-bottom: #{$i}px;
  }
  .ml-#{$i} {
    margin-left: #{$i}px;
  }
  .m-#{$i} {
    margin: #{$i}px;
  }

  .pt-#{$i} {
    padding-top: #{$i}px;
    box-sizing: border-box;
  }
  .pr-#{$i} {
    padding-right: #{$i}px;
    box-sizing: border-box;
  }
  .pb-#{$i} {
    padding-bottom: #{$i}px;
    box-sizing: border-box;
  }
  .pl-#{$i} {
    padding-left: #{$i}px;
    box-sizing: border-box;
  }
  .p-#{$i} {
    padding: #{$i}px;
    box-sizing: border-box;
  }

  .t-#{$i} {
    top: #{$i}px;
  }
  .l-#{$i} {
    left: #{$i}px;
  }
  .b-#{$i} {
    bottom: #{$i}px;
  }
  .r-#{$i} {
    right: #{$i}px;
  }

  .lh-#{$i} {
    line-height: #{$i}px;
  }
  .h-#{$i} {
    height: #{$i}px;
  }

  .min-h-#{$i} {
    min-height: #{$i}px;
  }
  .max-h-#{$i} {
    max-height: #{$i}px;
  }

  .w-#{$i} {
    width: #{$i}px;
  }
  .fs-#{$i} {
    font-size: #{$i}px;
  }
  .fw-#{$i} {
    font-weight: #{$i};
  }

  // 查询项的行高
  .search-form-label-lh-#{$i} {
    .el-form-item__label {
      line-height: #{$i}px;
    }
  }
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.flex {
  display: flex;
}

.flex-none {
  flex: none;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-space {
  display: flex;
  justify-content: space-between;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.text-center {
  text-align: center;
}

.position-center {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.ps-relative {
  position: relative;
}

.ps-absolute {
  position: absolute;
}

.ps-fixed {
  position: fixed;
}

.inline-block {
  display: inline-block;
}

.point-hand {
  cursor: pointer;
}

.float-right {
  float: right;
}

.float-left {
  float: left;
}
