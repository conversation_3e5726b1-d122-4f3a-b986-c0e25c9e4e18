# UI 设计规范文档

本目录包含了项目的 UI 设计规范和样式文件。

## 文件结构

```
src/styles/
├── design-tokens.scss    # 设计规范变量定义
├── utilities.scss        # 工具类样式
├── index.scss           # 样式入口文件
├── base.scss            # 基础样式
├── spacing.scss         # 间距样式
├── element-plus-reset.scss  # Element Plus 重置样式
├── _variables.scss      # 旧版变量文件（向后兼容）
└── README.md           # 本文档
```

## 设计规范

### 品牌色

- **主色**: `#1677FF` (`$brand-primary`)
- **品牌色**: `#0B457F` (`$brand-secondary`)

### 功能色

- **失败提示**: `#FF5959` (`$color-error`)
- **警告提示**: `#FAB007` (`$color-warning`)
- **成功提示**: `#14CA64` (`$color-success`)
- **信息提示**: `#1677FF` (`$color-info`)

### 文字色

- **主要文字**: `#1F2733` (`$text-primary`)
- **次要文字**: `#5F6A7A` (`$text-secondary`)
- **辅助文字**: `#929AA6` (`$text-tertiary`)
- **预设文字**: `#C9CED6` (`$text-placeholder`)

### 字体规范

- **字体族**: `$font-family`
- **一级标题**: 32px/40px (`$font-size-h1`/`$line-height-h1`)
- **二级标题**: 24px/32px (`$font-size-h2`/`$line-height-h2`)
- **三级标题**: 20px/28px (`$font-size-h3`/`$line-height-h3`)
- **四级标题**: 16px/24px (`$font-size-h4`/`$line-height-h4`)
- **正文内容**: 14px/22px (`$font-size-body`/`$line-height-body`)
- **次要文字**: 12px/20px (`$font-size-caption`/`$line-height-caption`)

### 间距规范

- `$spacing-0`: 0 (无间距)
- `$spacing-1`: 2px (特小间距)
- `$spacing-2`: 4px (超小间距)
- `$spacing-4`: 8px (较小间距)
- `$spacing-6`: 12px (小间距)
- `$spacing-8`: 16px (中等间距)
- `$spacing-10`: 20px (中大间距)
- `$spacing-12`: 24px (大间距)
- `$spacing-16`: 32px (较大间距)
- `$spacing-24`: 48px (超大间距)

### 圆角规范

- `$border-radius-s`: 2px (小圆角)
- `$border-radius-m`: 4px (中等圆角)
- `$border-radius-l`: 6px (大圆角)
- `$border-radius-xl`: 8px (超大圆角)
- `$border-radius-c`: 50% (全圆角)

### 阴影规范

- `$shadow-s`: 低层级阴影
- `$shadow-m-left`: 左侧导航阴影
- `$shadow-m-bottom`: 顶部导航阴影
- `$shadow-l`: 下拉面板阴影
- `$shadow-xl`: 对话框阴影

## 使用方法

### 1. 在 SCSS 文件中使用变量

```scss
@import '@/styles/design-tokens.scss';

.my-component {
  color: $text-primary;
  background-color: $brand-primary;
  padding: $spacing-8;
  border-radius: $border-radius-m;
  box-shadow: $shadow-s;
}
```

### 2. 在 Vue 组件中使用 CSS 变量

```vue
<template>
  <div class="my-component">
    <h1 class="title">标题</h1>
    <p class="content">内容</p>
  </div>
</template>

<style scoped>
.my-component {
  background-color: var(--bg-light);
  padding: var(--spacing-12);
  border-radius: var(--border-radius-l);
}

.title {
  color: var(--text-primary);
  font-size: var(--font-size-h3);
  line-height: var(--line-height-h3);
}

.content {
  color: var(--text-secondary);
  font-size: var(--font-size-body);
  line-height: var(--line-height-body);
}
</style>
```

### 3. 使用工具类

```vue
<template>
  <div class="bg-light p-12 rounded-l shadow-s">
    <h1 class="text-h3 text-primary mb-8">标题</h1>
    <p class="text-body text-secondary">内容</p>
  </div>
</template>
```

## 工具类说明

### 文字颜色类

- `.text-primary`, `.text-secondary`, `.text-tertiary`, `.text-placeholder`
- `.text-link`, `.text-danger`, `.text-warning`, `.text-success`

### 字体大小类

- `.text-h1`, `.text-h2`, `.text-h3`, `.text-h4`, `.text-body`, `.text-caption`

### 字重类

- `.font-thin`, `.font-normal`, `.font-medium`, `.font-semibold`

### 背景色类

- `.bg-primary`, `.bg-secondary`, `.bg-error`, `.bg-warning`, `.bg-success`, `.bg-info`
- `.bg-regular`, `.bg-light`

### 间距类

- Margin: `.m-{size}`, `.mt-{size}`, `.mb-{size}`, `.ml-{size}`, `.mr-{size}`
- Padding: `.p-{size}`, `.pt-{size}`, `.pb-{size}`, `.pl-{size}`, `.pr-{size}`
- Size: `0`, `1`, `2`, `4`, `6`, `8`, `10`, `12`, `16`, `24`

### 圆角类

- `.rounded-s`, `.rounded-m`, `.rounded-l`, `.rounded-xl`, `.rounded-full`

### 阴影类

- `.shadow-s`, `.shadow-m-left`, `.shadow-m-bottom`, `.shadow-l`, `.shadow-xl`

### 边框类

- `.border-dark`, `.border-regular`, `.border-primary`
- `.border-error`, `.border-warning`, `.border-success`, `.border-info`

## 图表主题色

项目提供了 11 种图表主题色，可以通过 `$chart-colors` 数组或单独的变量使用：

```scss
// 使用单独的颜色变量
.chart-item-1 {
  color: $chart-color-1;
}

// 使用颜色数组
@each $color in $chart-colors {
  // 生成图表颜色类
}
```

## JavaScript/TypeScript 工具函数

### 1. 使用工具函数

```typescript
import {
  getBrandColor,
  getFunctionalColor,
  getSpacing,
  getChartColors
} from '@/utils/design-tokens'

// 获取品牌色
const primaryColor = getBrandColor('primary') // '#1677FF'

// 获取功能色
const errorColor = getFunctionalColor('error') // '#FF5959'

// 获取间距
const spacing = getSpacing(8) // '16px'

// 获取图表颜色
const chartColors = getChartColors(5) // ['#1677FF', '#0AADFF', ...]
```

### 2. 使用 Vue 3 组合式函数

```vue
<script setup lang="ts">
import { useDesignTokens, useColors, useSpacing } from '@/composables'

// 基础设计规范
const { tokens, getBrandColor } = useDesignTokens()

// 颜色相关
const { getSemanticColor, getStatusColor } = useColors()

// 间距相关
const { getMarginStyle, getPaddingStyle } = useSpacing()

// 使用示例
const primaryColor = getBrandColor('primary')
const successColor = getStatusColor('success')
const marginStyle = getMarginStyle(8, 'top')
</script>

<template>
  <div :style="{ color: primaryColor, ...marginStyle }">示例内容</div>
</template>
```

### 3. 动态样式生成

```typescript
import { useStyles, useTypography } from '@/composables'

const { getCardStyle, getButtonStyle } = useStyles()
const { getHeadingStyle } = useTypography()

// 生成卡片样式
const cardStyle = getCardStyle({
  padding: 16,
  radius: 'l',
  shadow: 's'
})

// 生成按钮样式
const buttonStyle = getButtonStyle('primary', 'medium')

// 生成标题样式
const h1Style = getHeadingStyle(1)
```

### 4. 主题切换

```typescript
import { useDesignTokens } from '@/composables'

const { createCustomTheme, setTheme, resetTheme } = useDesignTokens()

// 创建深色主题
const darkTheme = createCustomTheme({
  brand: {
    primary: '#4A90E2',
    secondary: '#2C5282'
  },
  text: {
    primary: '#FFFFFF',
    secondary: '#CBD5E0'
  }
})

// 应用主题
setTheme(darkTheme)

// 重置为默认主题
resetTheme()
```

## 注意事项

1. 优先使用设计规范中定义的变量，保持设计一致性
2. 新增颜色或尺寸时，应先在设计规范中定义
3. 工具类使用 `!important` 确保优先级，谨慎使用
4. CSS 变量支持运行时修改，适合主题切换场景
5. SCSS 变量在编译时确定，性能更好但不支持运行时修改
6. 组合式函数提供响应式的设计规范访问，适合 Vue 3 项目
7. 工具函数可以在任何 JavaScript/TypeScript 环境中使用
