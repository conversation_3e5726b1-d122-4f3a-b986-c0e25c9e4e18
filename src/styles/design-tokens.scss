/**
 * UI 设计规范 - Design Tokens
 * 包含品牌色、功能色、文字色、基础色、字体、间距、圆角、阴影等设计变量
 */

// ==================== 品牌色 ====================
$brand-primary: #1677FF;           // 主色
$brand-secondary: #0B457F;         // 品牌色

// ==================== 功能色 ====================
$color-error: #FF5959;             // 失败提示
$color-warning: #FAB007;           // 警告提示
$color-success: #14CA64;           // 成功提示
$color-info: #1677FF;              // 信息提示

// 功能色/浅色版本
$color-error-light: #FFD1C9;       // 失败提示/浅
$color-warning-light: #FEF2B4;     // 警告提示/浅
$color-success-light: #B3F2C6;     // 成功提示/浅
$color-info-light: #BDE2FF;        // 信息提示/浅

// ==================== 文字色 ====================
$text-primary: #1F2733;            // 主要文字
$text-secondary: #5F6A7A;          // 次要文字
$text-tertiary: #929AA6;           // 辅助文字
$text-placeholder: #C9CED6;        // 预设文字
$text-link: #1677FF;               // 链接文字
$text-danger: #FF5959;             // 危险文字
$text-warning: #FAB007;            // 警示文字
$text-success: #14CA64;            // 成功文字

// ==================== 基础色 ====================
$border-dark: #DFE2E8;             // 分割线&描边/深
$border-regular: #EBEDF0;          // 分割线/常规
$bg-regular: #F2F4F7;              // 背景色/常规
$bg-light: #F5F7FA;                // 背景色/浅

// 状态色
$color-disabled: rgba(31, 39, 51, 0.2);  // 禁用状态
$color-mask: rgba(31, 39, 51, 0.3);      // 蒙层

// ==================== 中性色板 ====================
$neutral-black: #000000;
$neutral-900: #1F2733;
$neutral-700: #5F6A7A;
$neutral-500: #929AA6;
$neutral-400: #C9CED6;
$neutral-300: #DFE2E8;
$neutral-200: #EBEDF0;
$neutral-100: #F2F4F7;
$neutral-50: #F5F7FA;
$neutral-white: #FFFFFF;

// ==================== 图表主题色 ====================
$chart-color-1: #1677FF;
$chart-color-2: #0AADFF;
$chart-color-3: #28C7C7;
$chart-color-4: #14CA64;
$chart-color-5: #FACE0C;
$chart-color-6: #FAB007;
$chart-color-7: #FE7840;
$chart-color-8: #FF5959;
$chart-color-9: #9772FB;
$chart-color-10: #6675FF;
$chart-color-11: #7298D0;

// 图表色板数组
$chart-colors: (
  $chart-color-1,
  $chart-color-2,
  $chart-color-3,
  $chart-color-4,
  $chart-color-5,
  $chart-color-6,
  $chart-color-7,
  $chart-color-8,
  $chart-color-9,
  $chart-color-10,
  $chart-color-11
);

// ==================== 字体 ====================
$font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";

// 字号/行高
$font-size-h1: 32px;               // 一级标题
$line-height-h1: 40px;
$font-size-h2: 24px;               // 二级标题
$line-height-h2: 32px;
$font-size-h3: 20px;               // 三级标题
$line-height-h3: 28px;
$font-size-h4: 16px;               // 四级标题
$line-height-h4: 24px;
$font-size-body: 14px;             // 正文内容
$line-height-body: 22px;
$font-size-caption: 12px;          // 次要文字
$line-height-caption: 20px;

// 字重
$font-weight-thin: 200;            // 纤细体
$font-weight-normal: 400;          // 常规体
$font-weight-medium: 500;          // 中黑体
$font-weight-semibold: 600;        // 中粗体

// ==================== 阴影 ====================
$shadow-s: 0 1px 2px 0 rgba(216, 39, 20, 0.1);                    // 低层级阴影
$shadow-m-left: 4px 0 8px 0 rgba(216, 39, 20, 0.1);              // 左侧导航阴影
$shadow-m-bottom: 0 4px 8px 0 rgba(216, 39, 20, 0.1);            // 顶部导航阴影
$shadow-l: 0 8px 16px 6px rgba(216, 39, 20, 0.1);                // 下拉面板阴影
$shadow-xl: 0 12px 24px 8px rgba(216, 39, 20, 0.08);             // 对话框阴影

// ==================== 间距 ====================
$spacing-0: 0;                     // 无间距
$spacing-1: 2px;                   // 特小间距
$spacing-2: 4px;                   // 超小间距
$spacing-4: 8px;                   // 较小间距
$spacing-6: 12px;                  // 小间距
$spacing-8: 16px;                  // 中等间距
$spacing-10: 20px;                 // 中大间距
$spacing-12: 24px;                 // 大间距
$spacing-16: 32px;                 // 较大间距
$spacing-24: 48px;                 // 超大间距

// ==================== 圆角 ====================
$border-radius-s: 2px;             // 小圆角 - 小标签tag/小button
$border-radius-m: 4px;             // 中等圆角 - 常规button/条单/提示
$border-radius-l: 6px;             // 大圆角 - 卡片/页面模块/填充组件/气泡
$border-radius-xl: 8px;            // 超大圆角 - 卡片/页面模块/填充组件/气泡
$border-radius-c: 50%;             // 全圆角 - 全圆角Button/头像/开关

// ==================== CSS 自定义属性 (CSS Variables) ====================
:root {
  // 品牌色
  --brand-primary: #{$brand-primary};
  --brand-secondary: #{$brand-secondary};
  
  // 功能色
  --color-error: #{$color-error};
  --color-warning: #{$color-warning};
  --color-success: #{$color-success};
  --color-info: #{$color-info};
  --color-error-light: #{$color-error-light};
  --color-warning-light: #{$color-warning-light};
  --color-success-light: #{$color-success-light};
  --color-info-light: #{$color-info-light};
  
  // 文字色
  --text-primary: #{$text-primary};
  --text-secondary: #{$text-secondary};
  --text-tertiary: #{$text-tertiary};
  --text-placeholder: #{$text-placeholder};
  --text-link: #{$text-link};
  --text-danger: #{$text-danger};
  --text-warning: #{$text-warning};
  --text-success: #{$text-success};
  
  // 基础色
  --border-dark: #{$border-dark};
  --border-regular: #{$border-regular};
  --bg-regular: #{$bg-regular};
  --bg-light: #{$bg-light};
  --color-disabled: #{$color-disabled};
  --color-mask: #{$color-mask};
  
  // 字体
  --font-family: #{$font-family};
  
  // 间距
  --spacing-0: #{$spacing-0};
  --spacing-1: #{$spacing-1};
  --spacing-2: #{$spacing-2};
  --spacing-4: #{$spacing-4};
  --spacing-6: #{$spacing-6};
  --spacing-8: #{$spacing-8};
  --spacing-10: #{$spacing-10};
  --spacing-12: #{$spacing-12};
  --spacing-16: #{$spacing-16};
  --spacing-24: #{$spacing-24};
  
  // 圆角
  --border-radius-s: #{$border-radius-s};
  --border-radius-m: #{$border-radius-m};
  --border-radius-l: #{$border-radius-l};
  --border-radius-xl: #{$border-radius-xl};
  --border-radius-c: #{$border-radius-c};
  
  // 阴影
  --shadow-s: #{$shadow-s};
  --shadow-m-left: #{$shadow-m-left};
  --shadow-m-bottom: #{$shadow-m-bottom};
  --shadow-l: #{$shadow-l};
  --shadow-xl: #{$shadow-xl};
}
