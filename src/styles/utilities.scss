/**
 * 工具类 - Utilities
 * 基于设计规范的常用 CSS 工具类
 */

@import './design-tokens.scss';

// ==================== SCSS 函数和 Mixins ====================
// 获取间距值的函数
@function spacing($size) {
  @return var(--spacing-#{$size});
}

// 文字样式 mixin
@mixin text-style($size, $weight: normal, $color: $text-primary) {
  @if $size == 'h1' {
    font-size: $font-size-h1;
    line-height: $line-height-h1;
  } @else if $size == 'h2' {
    font-size: $font-size-h2;
    line-height: $line-height-h2;
  } @else if $size == 'h3' {
    font-size: $font-size-h3;
    line-height: $line-height-h3;
  } @else if $size == 'h4' {
    font-size: $font-size-h4;
    line-height: $line-height-h4;
  } @else if $size == 'body' {
    font-size: $font-size-body;
    line-height: $line-height-body;
  } @else if $size == 'caption' {
    font-size: $font-size-caption;
    line-height: $line-height-caption;
  }

  font-weight: $weight;
  color: $color;
  font-family: $font-family;
}

// 卡片样式 mixin
@mixin card-style($padding: 12, $radius: 'l', $shadow: 's', $background: $neutral-white) {
  padding: spacing($padding);
  border-radius: var(--border-radius-#{$radius});
  box-shadow: var(--shadow-#{$shadow});
  background-color: $background;
}

// 按钮样式 mixin
@mixin button-style($variant: 'primary', $size: 'medium') {
  @include text-style('body', 'medium');

  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: $border-radius-m;

  @if $size == 'small' {
    padding: spacing(4) spacing(8);
    @include text-style('caption', 'medium');
  } @else if $size == 'medium' {
    padding: spacing(8) spacing(16);
  } @else if $size == 'large' {
    padding: spacing(12) spacing(24);
    @include text-style('h4', 'medium');
  }

  @if $variant == 'primary' {
    background-color: $brand-primary;
    color: $neutral-white;

    &:hover {
      background-color: darken($brand-primary, 10%);
    }
  } @else if $variant == 'secondary' {
    background-color: $bg-regular;
    color: $text-primary;

    &:hover {
      background-color: darken($bg-regular, 5%);
    }
  } @else if $variant == 'outline' {
    background-color: transparent;
    color: $brand-primary;
    border: 1px solid $brand-primary;

    &:hover {
      background-color: $brand-primary;
      color: $neutral-white;
    }
  }
}

// 响应式断点 mixin
@mixin responsive($breakpoint) {
  @if $breakpoint == 'mobile' {
    @media (max-width: 767px) {
      @content;
    }
  } @else if $breakpoint == 'tablet' {
    @media (min-width: 768px) and (max-width: 1023px) {
      @content;
    }
  } @else if $breakpoint == 'desktop' {
    @media (min-width: 1024px) {
      @content;
    }
  }
}

// ==================== 文字工具类 ====================
// 文字颜色映射
$text-color-map: (
  'primary': $text-primary,
  'secondary': $text-secondary,
  'tertiary': $text-tertiary,
  'placeholder': $text-placeholder,
  'link': $text-link,
  'danger': $text-danger,
  'warning': $text-warning,
  'success': $text-success
);

// 生成文字颜色类
@each $name, $color in $text-color-map {
  .text-#{$name} {
    color: $color !important;
  }
}

// ==================== 字体大小工具类 ====================
// 字体大小映射
$font-size-map: (
  'h1': (
    $font-size-h1,
    $line-height-h1
  ),
  'h2': (
    $font-size-h2,
    $line-height-h2
  ),
  'h3': (
    $font-size-h3,
    $line-height-h3
  ),
  'h4': (
    $font-size-h4,
    $line-height-h4
  ),
  'body': (
    $font-size-body,
    $line-height-body
  ),
  'caption': (
    $font-size-caption,
    $line-height-caption
  )
);

// 生成字体大小类
@each $name, $values in $font-size-map {
  .text-#{$name} {
    font-size: nth($values, 1) !important;
    line-height: nth($values, 2) !important;
  }
}

// ==================== 字重工具类 ====================
// 字重映射
$font-weight-map: (
  'thin': $font-weight-thin,
  'normal': $font-weight-normal,
  'medium': $font-weight-medium,
  'semibold': $font-weight-semibold
);

// 生成字重类
@each $name, $weight in $font-weight-map {
  .font-#{$name} {
    font-weight: $weight !important;
  }
}

// ==================== 背景色工具类 ====================
// 背景色映射
$background-color-map: (
  'primary': $brand-primary,
  'secondary': $brand-secondary,
  'error': $color-error,
  'warning': $color-warning,
  'success': $color-success,
  'info': $color-info,
  'error-light': $color-error-light,
  'warning-light': $color-warning-light,
  'success-light': $color-success-light,
  'info-light': $color-info-light,
  'regular': $bg-regular,
  'light': $bg-light
);

// 生成背景色类
@each $name, $color in $background-color-map {
  .bg-#{$name} {
    background-color: $color !important;
  }
}

// ==================== 间距工具类 ====================
// 间距值列表
$spacing-sizes: (0, 1, 2, 4, 6, 8, 10, 12, 16, 24);

// 间距属性映射
$spacing-properties: (
  'm': 'margin',
  'mt': 'margin-top',
  'mb': 'margin-bottom',
  'ml': 'margin-left',
  'mr': 'margin-right',
  'p': 'padding',
  'pt': 'padding-top',
  'pb': 'padding-bottom',
  'pl': 'padding-left',
  'pr': 'padding-right'
);

// 生成间距工具类
@each $prefix, $property in $spacing-properties {
  @each $size in $spacing-sizes {
    .#{$prefix}-#{$size} {
      #{$property}: var(--spacing-#{$size}) !important;
    }
  }
}

// ==================== 圆角工具类 ====================
// 圆角映射
$border-radius-map: (
  's': $border-radius-s,
  'm': $border-radius-m,
  'l': $border-radius-l,
  'xl': $border-radius-xl,
  'full': $border-radius-c
);

// 生成圆角类
@each $name, $radius in $border-radius-map {
  .rounded-#{$name} {
    border-radius: $radius !important;
  }
}

// ==================== 阴影工具类 ====================
// 阴影映射
$shadow-map: (
  's': $shadow-s,
  'm-left': $shadow-m-left,
  'm-bottom': $shadow-m-bottom,
  'l': $shadow-l,
  'xl': $shadow-xl
);

// 生成阴影类
@each $name, $shadow in $shadow-map {
  .shadow-#{$name} {
    box-shadow: $shadow !important;
  }
}

// ==================== 边框工具类 ====================
// 边框颜色映射
$border-color-map: (
  'dark': $border-dark,
  'regular': $border-regular,
  'primary': $brand-primary,
  'error': $color-error,
  'warning': $color-warning,
  'success': $color-success,
  'info': $color-info
);

// 生成边框类
@each $name, $color in $border-color-map {
  .border-#{$name} {
    border: 1px solid $color !important;
  }
}

// ==================== 显示工具类 ====================
// 显示类型
$display-values: (none, block, inline, inline-block, flex, inline-flex, grid, inline-grid);

@each $value in $display-values {
  .d-#{$value} {
    display: $value !important;
  }
}

// Flex 相关工具类
.flex-row {
  flex-direction: row !important;
}
.flex-col {
  flex-direction: column !important;
}
.flex-wrap {
  flex-wrap: wrap !important;
}
.flex-nowrap {
  flex-wrap: nowrap !important;
}

// Justify content
.justify-start {
  justify-content: flex-start !important;
}
.justify-center {
  justify-content: center !important;
}
.justify-end {
  justify-content: flex-end !important;
}
.justify-between {
  justify-content: space-between !important;
}
.justify-around {
  justify-content: space-around !important;
}
.justify-evenly {
  justify-content: space-evenly !important;
}

// Align items
.items-start {
  align-items: flex-start !important;
}
.items-center {
  align-items: center !important;
}
.items-end {
  align-items: flex-end !important;
}
.items-stretch {
  align-items: stretch !important;
}
.items-baseline {
  align-items: baseline !important;
}

// Align self
.self-start {
  align-self: flex-start !important;
}
.self-center {
  align-self: center !important;
}
.self-end {
  align-self: flex-end !important;
}
.self-stretch {
  align-self: stretch !important;
}

// Flex grow/shrink
.flex-1 {
  flex: 1 1 0% !important;
}
.flex-auto {
  flex: 1 1 auto !important;
}
.flex-initial {
  flex: 0 1 auto !important;
}
.flex-none {
  flex: none !important;
}

// ==================== 位置工具类 ====================
.relative {
  position: relative !important;
}
.absolute {
  position: absolute !important;
}
.fixed {
  position: fixed !important;
}
.sticky {
  position: sticky !important;
}
.static {
  position: static !important;
}

// ==================== 尺寸工具类 ====================
.w-full {
  width: 100% !important;
}
.w-auto {
  width: auto !important;
}
.h-full {
  height: 100% !important;
}
.h-auto {
  height: auto !important;
}

// 最大/最小尺寸
.max-w-full {
  max-width: 100% !important;
}
.max-h-full {
  max-height: 100% !important;
}
.min-w-0 {
  min-width: 0 !important;
}
.min-h-0 {
  min-height: 0 !important;
}

// ==================== 溢出工具类 ====================
.overflow-hidden {
  overflow: hidden !important;
}
.overflow-auto {
  overflow: auto !important;
}
.overflow-scroll {
  overflow: scroll !important;
}
.overflow-visible {
  overflow: visible !important;
}

.overflow-x-hidden {
  overflow-x: hidden !important;
}
.overflow-x-auto {
  overflow-x: auto !important;
}
.overflow-y-hidden {
  overflow-y: hidden !important;
}
.overflow-y-auto {
  overflow-y: auto !important;
}

// ==================== 文本对齐工具类 ====================
.text-left {
  text-align: left !important;
}
.text-center {
  text-align: center !important;
}
.text-right {
  text-align: right !important;
}
.text-justify {
  text-align: justify !important;
}

// 文本装饰
.underline {
  text-decoration: underline !important;
}
.line-through {
  text-decoration: line-through !important;
}
.no-underline {
  text-decoration: none !important;
}

// 文本转换
.uppercase {
  text-transform: uppercase !important;
}
.lowercase {
  text-transform: lowercase !important;
}
.capitalize {
  text-transform: capitalize !important;
}
.normal-case {
  text-transform: none !important;
}

// 文本溢出
.truncate {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

.text-ellipsis {
  text-overflow: ellipsis !important;
}
.text-clip {
  text-overflow: clip !important;
}

// ==================== 可见性工具类 ====================
.visible {
  visibility: visible !important;
}
.invisible {
  visibility: hidden !important;
}

.opacity-0 {
  opacity: 0 !important;
}
.opacity-25 {
  opacity: 0.25 !important;
}
.opacity-50 {
  opacity: 0.5 !important;
}
.opacity-75 {
  opacity: 0.75 !important;
}
.opacity-100 {
  opacity: 1 !important;
}

// ==================== 指针事件工具类 ====================
.pointer-events-none {
  pointer-events: none !important;
}
.pointer-events-auto {
  pointer-events: auto !important;
}

.cursor-pointer {
  cursor: pointer !important;
}
.cursor-default {
  cursor: default !important;
}
.cursor-not-allowed {
  cursor: not-allowed !important;
}

// ==================== 用户选择工具类 ====================
.select-none {
  user-select: none !important;
}
.select-text {
  user-select: text !important;
}
.select-all {
  user-select: all !important;
}
.select-auto {
  user-select: auto !important;
}

// ==================== 响应式工具类 ====================
// 移动端隐藏/显示
@include responsive('mobile') {
  .hidden-mobile {
    display: none !important;
  }
  .block-mobile {
    display: block !important;
  }
  .flex-mobile {
    display: flex !important;
  }
}

// 平板端隐藏/显示
@include responsive('tablet') {
  .hidden-tablet {
    display: none !important;
  }
  .block-tablet {
    display: block !important;
  }
  .flex-tablet {
    display: flex !important;
  }
}

// 桌面端隐藏/显示
@include responsive('desktop') {
  .hidden-desktop {
    display: none !important;
  }
  .block-desktop {
    display: block !important;
  }
  .flex-desktop {
    display: flex !important;
  }
}

// ==================== 状态工具类 ====================
// 禁用状态
.disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
  pointer-events: none !important;
}

// 加载状态
.loading {
  position: relative !important;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid $border-regular;
    border-top-color: $brand-primary;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// ==================== 交互状态工具类 ====================
// 悬停效果
.hover-shadow {
  transition: box-shadow 0.2s ease !important;

  &:hover {
    box-shadow: $shadow-l !important;
  }
}

.hover-scale {
  transition: transform 0.2s ease !important;

  &:hover {
    transform: scale(1.05) !important;
  }
}

.hover-opacity {
  transition: opacity 0.2s ease !important;

  &:hover {
    opacity: 0.8 !important;
  }
}
