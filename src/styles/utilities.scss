/**
 * 工具类 - Utilities
 * 基于设计规范的常用 CSS 工具类
 */

@import './design-tokens.scss';

// ==================== 文字工具类 ====================
// 文字颜色映射
$text-color-map: (
  'primary': $text-primary,
  'secondary': $text-secondary,
  'tertiary': $text-tertiary,
  'placeholder': $text-placeholder,
  'link': $text-link,
  'danger': $text-danger,
  'warning': $text-warning,
  'success': $text-success
);

// 生成文字颜色类
@each $name, $color in $text-color-map {
  .text-#{$name} {
    color: $color !important;
  }
}

// ==================== 字体大小工具类 ====================
// 字体大小映射
$font-size-map: (
  'h1': (
    $font-size-h1,
    $line-height-h1
  ),
  'h2': (
    $font-size-h2,
    $line-height-h2
  ),
  'h3': (
    $font-size-h3,
    $line-height-h3
  ),
  'h4': (
    $font-size-h4,
    $line-height-h4
  ),
  'body': (
    $font-size-body,
    $line-height-body
  ),
  'caption': (
    $font-size-caption,
    $line-height-caption
  )
);

// 生成字体大小类
@each $name, $values in $font-size-map {
  .text-#{$name} {
    font-size: nth($values, 1) !important;
    line-height: nth($values, 2) !important;
  }
}

// ==================== 字重工具类 ====================
// 字重映射
$font-weight-map: (
  'thin': $font-weight-thin,
  'normal': $font-weight-normal,
  'medium': $font-weight-medium,
  'semibold': $font-weight-semibold
);

// 生成字重类
@each $name, $weight in $font-weight-map {
  .font-#{$name} {
    font-weight: $weight !important;
  }
}

// ==================== 背景色工具类 ====================
// 背景色映射
$background-color-map: (
  'primary': $brand-primary,
  'secondary': $brand-secondary,
  'error': $color-error,
  'warning': $color-warning,
  'success': $color-success,
  'info': $color-info,
  'error-light': $color-error-light,
  'warning-light': $color-warning-light,
  'success-light': $color-success-light,
  'info-light': $color-info-light,
  'regular': $bg-regular,
  'light': $bg-light
);

// 生成背景色类
@each $name, $color in $background-color-map {
  .bg-#{$name} {
    background-color: $color !important;
  }
}

// ==================== 间距工具类 ====================
// 间距值列表
$spacing-sizes: (0, 1, 2, 4, 6, 8, 10, 12, 16, 24);

// 间距属性映射
$spacing-properties: (
  'm': 'margin',
  'mt': 'margin-top',
  'mb': 'margin-bottom',
  'ml': 'margin-left',
  'mr': 'margin-right',
  'p': 'padding',
  'pt': 'padding-top',
  'pb': 'padding-bottom',
  'pl': 'padding-left',
  'pr': 'padding-right'
);

// 生成间距工具类
@each $prefix, $property in $spacing-properties {
  @each $size in $spacing-sizes {
    .#{$prefix}-#{$size} {
      #{$property}: var(--spacing-#{$size}) !important;
    }
  }
}

// ==================== 圆角工具类 ====================
// 圆角映射
$border-radius-map: (
  's': $border-radius-s,
  'm': $border-radius-m,
  'l': $border-radius-l,
  'xl': $border-radius-xl,
  'full': $border-radius-c
);

// 生成圆角类
@each $name, $radius in $border-radius-map {
  .rounded-#{$name} {
    border-radius: $radius !important;
  }
}

// ==================== 阴影工具类 ====================
// 阴影映射
$shadow-map: (
  's': $shadow-s,
  'm-left': $shadow-m-left,
  'm-bottom': $shadow-m-bottom,
  'l': $shadow-l,
  'xl': $shadow-xl
);

// 生成阴影类
@each $name, $shadow in $shadow-map {
  .shadow-#{$name} {
    box-shadow: $shadow !important;
  }
}

// ==================== 边框工具类 ====================
// 边框颜色映射
$border-color-map: (
  'dark': $border-dark,
  'regular': $border-regular,
  'primary': $brand-primary,
  'error': $color-error,
  'warning': $color-warning,
  'success': $color-success,
  'info': $color-info
);

// 生成边框类
@each $name, $color in $border-color-map {
  .border-#{$name} {
    border: 1px solid $color !important;
  }
}
