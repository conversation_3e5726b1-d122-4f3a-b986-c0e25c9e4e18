/**
 * Vue 3 设计规范组合式函数
 * 提供在 Vue 组件中使用设计规范的便捷方法
 */

import { computed, ref, type Ref } from 'vue'
import type { 
  BrandColor, 
  FunctionalColor, 
  TextColor, 
  FontSize, 
  Spacing, 
  BorderRadius, 
  Shadow,
  DesignTokens 
} from '@/types/design-tokens'
import {
  DESIGN_TOKENS,
  getBrandColor,
  getFunctionalColor,
  getTextColor,
  getSpacing,
  getFontSize,
  getBorderRadius,
  getShadow,
  getChartColor,
  getChartColors,
  getCSSVariable,
  getContrastTextColor,
  getResponsiveSpacing,
  isValidDesignColor,
  createTheme
} from '@/utils/design-tokens'

/**
 * 设计规范组合式函数
 * @returns 设计规范相关的响应式数据和方法
 */
export function useDesignTokens() {
  // 当前主题
  const currentTheme = ref<DesignTokens>(DESIGN_TOKENS)
  
  // 响应式的设计规范
  const tokens = computed(() => currentTheme.value)
  
  /**
   * 设置主题
   * @param theme 主题配置
   */
  const setTheme = (theme: DesignTokens) => {
    currentTheme.value = theme
  }
  
  /**
   * 创建自定义主题
   * @param overrides 覆盖配置
   */
  const createCustomTheme = (overrides: Partial<DesignTokens>) => {
    const customTheme = createTheme(overrides)
    setTheme(customTheme)
    return customTheme
  }
  
  /**
   * 重置为默认主题
   */
  const resetTheme = () => {
    setTheme(DESIGN_TOKENS)
  }
  
  return {
    // 响应式数据
    tokens,
    currentTheme: computed(() => currentTheme.value),
    
    // 主题管理
    setTheme,
    createCustomTheme,
    resetTheme,
    
    // 颜色获取函数
    getBrandColor,
    getFunctionalColor,
    getTextColor,
    getChartColor,
    getChartColors,
    getContrastTextColor,
    
    // 尺寸获取函数
    getSpacing,
    getFontSize,
    getBorderRadius,
    getShadow,
    getResponsiveSpacing,
    
    // CSS 变量函数
    getCSSVariable,
    
    // 验证函数
    isValidDesignColor
  }
}

/**
 * 颜色相关的组合式函数
 * @returns 颜色相关的方法和计算属性
 */
export function useColors() {
  const { tokens } = useDesignTokens()
  
  // 品牌色
  const brandColors = computed(() => tokens.value.brand)
  
  // 功能色
  const functionalColors = computed(() => tokens.value.functional)
  
  // 文字色
  const textColors = computed(() => tokens.value.text)
  
  // 基础色
  const baseColors = computed(() => tokens.value.base)
  
  // 中性色
  const neutralColors = computed(() => tokens.value.neutral)
  
  // 图表色
  const chartColors = computed(() => tokens.value.chart)
  
  /**
   * 获取状态颜色
   * @param status 状态类型
   * @returns 颜色值
   */
  const getStatusColor = (status: 'success' | 'warning' | 'error' | 'info') => {
    return functionalColors.value[status]
  }
  
  /**
   * 获取语义化颜色
   * @param semantic 语义类型
   * @returns 颜色值
   */
  const getSemanticColor = (semantic: 'primary' | 'secondary' | 'danger' | 'warning' | 'success' | 'info') => {
    switch (semantic) {
      case 'primary':
        return brandColors.value.primary
      case 'secondary':
        return brandColors.value.secondary
      case 'danger':
        return functionalColors.value.error
      case 'warning':
        return functionalColors.value.warning
      case 'success':
        return functionalColors.value.success
      case 'info':
        return functionalColors.value.info
      default:
        return brandColors.value.primary
    }
  }
  
  return {
    brandColors,
    functionalColors,
    textColors,
    baseColors,
    neutralColors,
    chartColors,
    getStatusColor,
    getSemanticColor,
    getContrastTextColor
  }
}

/**
 * 间距相关的组合式函数
 * @returns 间距相关的方法和计算属性
 */
export function useSpacing() {
  const { tokens } = useDesignTokens()
  
  // 间距值
  const spacingValues = computed(() => tokens.value.spacing)
  
  /**
   * 生成 margin 样式
   * @param spacing 间距值
   * @param direction 方向
   * @returns CSS 样式对象
   */
  const getMarginStyle = (spacing: Spacing, direction?: 'top' | 'right' | 'bottom' | 'left') => {
    const value = getSpacing(spacing)
    if (direction) {
      return { [`margin-${direction}`]: value }
    }
    return { margin: value }
  }
  
  /**
   * 生成 padding 样式
   * @param spacing 间距值
   * @param direction 方向
   * @returns CSS 样式对象
   */
  const getPaddingStyle = (spacing: Spacing, direction?: 'top' | 'right' | 'bottom' | 'left') => {
    const value = getSpacing(spacing)
    if (direction) {
      return { [`padding-${direction}`]: value }
    }
    return { padding: value }
  }
  
  return {
    spacingValues,
    getSpacing,
    getMarginStyle,
    getPaddingStyle,
    getResponsiveSpacing
  }
}

/**
 * 字体相关的组合式函数
 * @returns 字体相关的方法和计算属性
 */
export function useTypography() {
  const { tokens } = useDesignTokens()
  
  // 字体配置
  const fontConfig = computed(() => tokens.value.font)
  
  /**
   * 生成字体样式
   * @param size 字体大小类型
   * @param weight 字重
   * @returns CSS 样式对象
   */
  const getFontStyle = (size: FontSize, weight?: keyof typeof tokens.value.font.weight) => {
    const { fontSize, lineHeight } = getFontSize(size)
    const style: Record<string, string> = {
      fontSize,
      lineHeight,
      fontFamily: fontConfig.value.family
    }
    
    if (weight) {
      style.fontWeight = fontConfig.value.weight[weight].toString()
    }
    
    return style
  }
  
  /**
   * 生成标题样式
   * @param level 标题级别
   * @returns CSS 样式对象
   */
  const getHeadingStyle = (level: 1 | 2 | 3 | 4) => {
    const sizeMap: Record<number, FontSize> = {
      1: 'h1',
      2: 'h2',
      3: 'h3',
      4: 'h4'
    }
    return getFontStyle(sizeMap[level], 'semibold')
  }
  
  return {
    fontConfig,
    getFontSize,
    getFontStyle,
    getHeadingStyle
  }
}

/**
 * 样式相关的组合式函数
 * @returns 样式相关的方法和计算属性
 */
export function useStyles() {
  const { tokens } = useDesignTokens()
  
  // 圆角值
  const borderRadiusValues = computed(() => tokens.value.borderRadius)
  
  // 阴影值
  const shadowValues = computed(() => tokens.value.shadow)
  
  /**
   * 生成卡片样式
   * @param options 配置选项
   * @returns CSS 样式对象
   */
  const getCardStyle = (options: {
    padding?: Spacing
    radius?: BorderRadius
    shadow?: Shadow
    background?: string
  } = {}) => {
    const {
      padding = 12,
      radius = 'l',
      shadow = 's',
      background = tokens.value.neutral.white
    } = options
    
    return {
      padding: getSpacing(padding),
      borderRadius: getBorderRadius(radius),
      boxShadow: getShadow(shadow),
      backgroundColor: background
    }
  }
  
  /**
   * 生成按钮样式
   * @param variant 按钮变体
   * @param size 按钮大小
   * @returns CSS 样式对象
   */
  const getButtonStyle = (variant: 'primary' | 'secondary' | 'outline' = 'primary', size: 'small' | 'medium' | 'large' = 'medium') => {
    const sizeMap = {
      small: { padding: 4, fontSize: 'caption' as FontSize },
      medium: { padding: 8, fontSize: 'body' as FontSize },
      large: { padding: 12, fontSize: 'h4' as FontSize }
    }
    
    const { padding, fontSize } = sizeMap[size]
    const baseStyle = {
      ...getFontStyle(fontSize, 'medium'),
      padding: `${getSpacing(padding)} ${getSpacing(padding * 2)}`,
      borderRadius: getBorderRadius('m'),
      border: 'none',
      cursor: 'pointer',
      transition: 'all 0.2s ease'
    }
    
    switch (variant) {
      case 'primary':
        return {
          ...baseStyle,
          backgroundColor: tokens.value.brand.primary,
          color: tokens.value.neutral.white
        }
      case 'secondary':
        return {
          ...baseStyle,
          backgroundColor: tokens.value.base.bgRegular,
          color: tokens.value.text.primary
        }
      case 'outline':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
          color: tokens.value.brand.primary,
          border: `1px solid ${tokens.value.brand.primary}`
        }
      default:
        return baseStyle
    }
  }
  
  return {
    borderRadiusValues,
    shadowValues,
    getBorderRadius,
    getShadow,
    getCardStyle,
    getButtonStyle
  }
}
