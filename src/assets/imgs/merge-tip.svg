<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1.通用/2.Icon图标/stroke描边/失败</title>
    <defs>
        <rect id="path-1" x="0" y="0" width="800" height="555" rx="2"></rect>
        <filter x="-12.5%" y="-16.6%" width="125.0%" height="136.0%" filterUnits="objectBoundingBox" id="filter-2">
            <feMorphology radius="8" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="8" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="24" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.03 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feOffset dx="0" dy="16" in="SourceAlpha" result="shadowOffsetOuter2"></feOffset>
            <feGaussianBlur stdDeviation="14" in="shadowOffsetOuter2" result="shadowBlurOuter2"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.08 0" type="matrix" in="shadowBlurOuter2" result="shadowMatrixOuter2"></feColorMatrix>
            <feMorphology radius="4" operator="erode" in="SourceAlpha" result="shadowSpreadOuter3"></feMorphology>
            <feOffset dx="0" dy="6" in="shadowSpreadOuter3" result="shadowOffsetOuter3"></feOffset>
            <feGaussianBlur stdDeviation="8" in="shadowOffsetOuter3" result="shadowBlurOuter3"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.12 0" type="matrix" in="shadowBlurOuter3" result="shadowMatrixOuter3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="shadowMatrixOuter2"></feMergeNode>
                <feMergeNode in="shadowMatrixOuter3"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="长安VOC" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="4-10合并分类" transform="translate(-1026.000000, -355.000000)">
            <rect fill="#F5F5F5" x="0" y="0" width="1440" height="923"></rect>
            <rect id="矩形" stroke="#979797" fill-opacity="0.423568619" fill="#000000" x="0.5" y="0.5" width="1445" height="980"></rect>
            <g id="7.容器/6.阴影/白色/第三层/下阴影-3" transform="translate(344.000000, 169.000000)">
                <g id="编组" fill-rule="nonzero">
                    <g id="边框">
                        <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                        <use fill="#FFFFFF" xlink:href="#path-1"></use>
                    </g>
                </g>
                <rect id="背景" fill="#FFFFFF" x="0" y="0" width="800" height="555" rx="2"></rect>
            </g>
            <g id="4.数据展示/14.表格/table/default备份-177" transform="translate(950.000000, 339.000000)">
                <rect id="矩形" fill="#FFFFFF" x="0" y="0" width="133" height="48"></rect>
                <g id="边框">
                    <rect id="bg" fill="#E4F2FF" x="0" y="0" width="133" height="48"></rect>
                    <rect id="右描边" fill-opacity="0" fill="#000000" x="132" y="0" width="1" height="48"></rect>
                    <rect id="左描边" fill-opacity="0" fill="#000000" x="0" y="0" width="1" height="48"></rect>
                    <rect id="下描边" fill-opacity="0.06" fill="#000000" x="0" y="47" width="133" height="1"></rect>
                    <rect id="上描边" fill-opacity="0" fill="#000000" x="0" y="0" width="133" height="1"></rect>
                </g>
                <g id="表格内容" transform="translate(16.000000, 13.000000)" fill="#000000" fill-opacity="0.75" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" line-spacing="22">
                    <text>
                        <tspan x="0" y="15">备注</tspan>
                    </text>
                </g>
            </g>
            <rect id="矩形" stroke="#F0F0F0" x="405.5" y="291.5" width="676" height="287"></rect>
            <g id="success" transform="translate(1026.000000, 355.000000)">
                <rect id="矩形备份-54" opacity="0.15" x="0" y="0" width="16" height="16"></rect>
                <path d="M8,0.666666667 C12.0500882,0.666666667 15.3333333,3.94991183 15.3333333,8 C15.3333333,12.0500882 12.0500882,15.3333333 8,15.3333333 C3.94991183,15.3333333 0.666666667,12.0500882 0.666666667,8 C0.666666667,3.94991183 3.94991183,0.666666667 8,0.666666667 Z M11.1854497,5.80473785 C10.9251002,5.54438833 10.5029902,5.54438833 10.2426407,5.80473785 L10.2426407,5.80473785 L6.94233764,9.10362669 L6,8.16176046 C5.73965047,7.90141093 5.31754049,7.90141093 5.05719096,8.16176046 C4.79684143,8.42210999 4.79684143,8.84421997 5.05719096,9.1045695 L5.05719096,9.1045695 L6.47140452,10.5187831 L6.54617646,10.5833325 C6.80716446,10.7769809 7.17753217,10.7554645 7.41421356,10.5187831 L7.41421356,10.5187831 L11.1854497,6.7475469 C11.4457993,6.48719737 11.4457993,6.06508738 11.1854497,5.80473785 Z" id="形状结合" fill="#00CA00"></path>
            </g>
        </g>
    </g>
</svg>