import { describe, it, expect } from 'vitest'

/**
 * 测试环境验证
 */
describe('测试环境验证', () => {
  it('should have vitest globals available', () => {
    expect(describe).toBeDefined()
    expect(it).toBeDefined()
    expect(expect).toBeDefined()
  })

  it('should be able to run basic assertions', () => {
    expect(1 + 1).toBe(2)
    expect('hello').toBe('hello')
    expect(true).toBeTruthy()
    expect(false).toBeFalsy()
  })

  it('should handle async operations', async () => {
    const result = await Promise.resolve('async test')
    expect(result).toBe('async test')
  })
})
