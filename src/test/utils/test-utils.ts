import { vi } from 'vitest'

/**
 * 测试实用工具函数
 */

/**
 * 等待指定时间
 * @param ms 等待毫秒数
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 创建模拟函数
 * @param returnValue 返回值
 */
export function createMockFn<T = any>(returnValue?: T) {
  return vi.fn(() => returnValue)
}

/**
 * 创建模拟的 Promise 函数
 * @param returnValue 返回值
 * @param delay 延迟时间（毫秒）
 */
export function createMockPromiseFn<T = any>(returnValue?: T, delay = 0) {
  return vi.fn(() =>
    delay > 0
      ? new Promise(resolve => setTimeout(() => resolve(returnValue), delay))
      : Promise.resolve(returnValue)
  )
}

/**
 * 创建模拟的错误 Promise 函数
 * @param error 错误信息
 * @param delay 延迟时间（毫秒）
 */
export function createMockErrorFn(error: any = new Error('Mock error'), delay = 0) {
  return vi.fn(() =>
    delay > 0
      ? new Promise((_, reject) => setTimeout(() => reject(error), delay))
      : Promise.reject(error)
  )
}

/**
 * 生成随机字符串
 * @param length 长度
 */
export function randomString(length = 8): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 生成随机数字
 * @param min 最小值
 * @param max 最大值
 */
export function randomNumber(min = 0, max = 100): number {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

/**
 * 深度克隆对象
 * @param obj 对象
 */
export function deepClone<T>(obj: T): T {
  return JSON.parse(JSON.stringify(obj))
}

/**
 * 获取元素的文本内容（去除多余空格）
 * @param element DOM 元素
 */
export function getCleanText(element: Element): string {
  return element.textContent?.replace(/\s+/g, ' ').trim() || ''
}

/**
 * 检查元素是否包含指定的 CSS 类
 * @param element DOM 元素
 * @param className 类名
 */
export function hasClass(element: Element, className: string): boolean {
  return element.classList.contains(className)
}

/**
 * 模拟用户输入
 * @param input 输入元素
 * @param value 输入值
 */
export async function userInput(input: HTMLInputElement, value: string): Promise<void> {
  input.value = value
  input.dispatchEvent(new Event('input', { bubbles: true }))
  await nextTick()
}

/**
 * 模拟点击事件
 * @param element 元素
 */
export async function userClick(element: Element): Promise<void> {
  element.dispatchEvent(new MouseEvent('click', { bubbles: true }))
  await nextTick()
}

/**
 * Vue nextTick 封装
 */
export function nextTick(): Promise<void> {
  return new Promise(resolve => {
    setTimeout(resolve, 0)
  })
}
