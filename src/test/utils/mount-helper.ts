import { mount, VueWrapper } from '@vue/test-utils'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import type { Component } from 'vue'

/**
 * 组件挂载辅助函数
 * 提供统一的组件挂载配置
 */
export function mountComponent(component: Component, options: any = {}): VueWrapper<any> {
  const defaultOptions = {
    global: {
      plugins: [ElementPlus, createPinia()],
      stubs: {
        // 可以在这里添加需要stub的组件
      }
    }
  }

  const mergedOptions = {
    ...defaultOptions,
    ...options,
    global: {
      ...defaultOptions.global,
      ...options.global
    }
  }

  return mount(component, mergedOptions)
}

/**
 * 创建测试用的 Pinia 实例
 */
export function createTestPinia() {
  return createPinia()
}

/**
 * 等待 Vue 的 nextTick
 */
export async function nextTick(): Promise<void> {
  return new Promise(resolve => {
    setTimeout(resolve, 0)
  })
}
