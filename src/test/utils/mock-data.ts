import { UserStatus, RoleStatus, DataScope } from '@/types/system'
import type { User, Role, Permission, Menu } from '@/types/system'
import type { Option } from '@/components/ButtonGroup/types.d'
import type { ChartData } from '@/types/chart'

/**
 * Mock 用户数据
 */
export const mockUsers: User[] = [
  {
    id: 1,
    username: 'admin',
    nickname: '超级管理员',
    email: '<EMAIL>',
    phone: '13800138000',
    status: UserStatus.ACTIVE,
    roles: [],
    createTime: new Date('2024-01-01'),
    updateTime: new Date('2024-01-01'),
    remark: '系统管理员'
  },
  {
    id: 2,
    username: 'user',
    nickname: '普通用户',
    email: '<EMAIL>',
    phone: '13800138001',
    status: UserStatus.ACTIVE,
    roles: [],
    createTime: new Date('2024-01-02'),
    updateTime: new Date('2024-01-02'),
    remark: '普通用户'
  }
]

/**
 * Mock 角色数据
 */
export const mockRoles: Role[] = [
  {
    id: 1,
    name: '超级管理员',
    code: 'SUPER_ADMIN',
    description: '系统超级管理员',
    level: 1,
    permissions: [],
    menus: [],
    dataScope: DataScope.ALL,
    status: RoleStatus.ENABLE,
    createTime: new Date('2024-01-01'),
    updateTime: new Date('2024-01-01')
  },
  {
    id: 2,
    name: '普通用户',
    code: 'USER',
    description: '系统普通用户',
    level: 3,
    permissions: [],
    menus: [],
    dataScope: DataScope.SELF,
    status: RoleStatus.ENABLE,
    createTime: new Date('2024-01-01'),
    updateTime: new Date('2024-01-01')
  }
]

/**
 * Mock 按钮组选项数据
 */
export const mockButtonOptions: Option[] = [
  { label: '选项1', value: 'option1' },
  { label: '选项2', value: 'option2' },
  { label: '选项3', value: 'option3' }
]

/**
 * Mock 图表数据
 */
export const mockChartData: ChartData = {
  data: [
    {
      date: '2024-01',
      positive: 100,
      neutral: 50,
      negative: 30,
      experience: 0.8
    },
    {
      date: '2024-02',
      positive: 120,
      neutral: 60,
      negative: 25,
      experience: 0.85
    },
    {
      date: '2024-03',
      positive: 90,
      neutral: 45,
      negative: 35,
      experience: 0.75
    }
  ],
  xDataKey: 'date',
  seriesDataKey: [
    {
      name: '正面提及量',
      key: 'positive',
      type: 'bar',
      color: '#3ED4A9'
    },
    {
      name: '中性提及量',
      key: 'neutral',
      type: 'bar',
      color: '#0077FF'
    },
    {
      name: '负面提及量',
      key: 'negative',
      type: 'bar',
      color: '#5D7092'
    },
    {
      name: '体验值',
      key: 'experience',
      type: 'line',
      color: '#FFC157'
    }
  ]
}

/**
 * Mock API 响应数据
 */
export const mockApiResponse = {
  success: {
    code: '200',
    message: '操作成功',
    data: null
  },
  error: {
    code: '500',
    message: '操作失败',
    data: null
  }
}
