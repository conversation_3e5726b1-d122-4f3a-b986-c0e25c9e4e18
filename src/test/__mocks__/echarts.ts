import { vi } from 'vitest'

/**
 * ECharts Mock
 * 模拟 ECharts 图表库的基本功能
 */

const mockChart = {
  setOption: vi.fn(),
  resize: vi.fn(),
  dispose: vi.fn(),
  on: vi.fn(),
  off: vi.fn(),
  showLoading: vi.fn(),
  hideLoading: vi.fn(),
  getOption: vi.fn(() => ({})),
  clear: vi.fn(),
  getDataURL: vi.fn(() => 'data:image/png;base64,mock'),
  getConnectedDataURL: vi.fn(() => 'data:image/png;base64,mock')
}

export const init = vi.fn(() => mockChart)
export const dispose = vi.fn()
export const getInstanceByDom = vi.fn(() => mockChart)
export const registerMap = vi.fn()
export const registerTheme = vi.fn()
export const graphic = {
  clipPointsByRect: vi.fn(),
  clipRectByRect: vi.fn()
}

export default {
  init,
  dispose,
  getInstanceByDom,
  registerMap,
  registerTheme,
  graphic
}
