import { vi } from 'vitest'

/**
 * Element Plus Mock
 * 模拟 Element Plus UI 组件库的基本功能
 */

// 模拟 ElMessage
export const ElMessage = {
  success: vi.fn(),
  error: vi.fn(),
  warning: vi.fn(),
  info: vi.fn(),
  close: vi.fn(),
  closeAll: vi.fn()
}

// 模拟 ElMessageBox
export const ElMessageBox = {
  alert: vi.fn(() => Promise.resolve()),
  confirm: vi.fn(() => Promise.resolve()),
  prompt: vi.fn(() => Promise.resolve({ value: 'mock input' }))
}

// 模拟 ElNotification
export const ElNotification = {
  success: vi.fn(),
  error: vi.fn(),
  warning: vi.fn(),
  info: vi.fn(),
  close: vi.fn(),
  closeAll: vi.fn()
}

// 模拟 ElLoading
export const ElLoading = {
  service: vi.fn(() => ({
    close: vi.fn()
  }))
}

// 模拟常用图标
export const Edit = vi.fn()
export const Delete = vi.fn()
export const Plus = vi.fn()
export const Search = vi.fn()
export const Close = vi.fn()

export default {
  ElMessage,
  ElMessageBox,
  ElNotification,
  ElLoading,
  Edit,
  Delete,
  Plus,
  Search,
  Close
}
