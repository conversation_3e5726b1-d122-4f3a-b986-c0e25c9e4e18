<template>
  <div class="home-page">
    <ButtonGroup v-model="activeTab" :options="tabs" @change="handleTabChange" />

    <template v-if="activeTab === 'overview'">
      <h2 class="mt-20 mb-10">VOC体验值趋势</h2>
      <VocTrendChart :data="chartData" :needDetails="true" />

      <!-- VOC体验值趋势图表 -->
      <div class="chart-section">
        <h2 class="section-title">VOC体验值趋势图表</h2>
        <div class="chart-container">
          <div class="tips-sidebar">
            <ExperienceTips :data="customerJourneyExperience" />
          </div>
          <BarAndPointChart
            ref="basicChartRef"
            :data="basicChartData"
            :need-details="true"
            @bar-click="handleBarClick"
            class="chart-main"
          />
        </div>
      </div>
    </template>
    <template v-else-if="activeTab === 'top'">
      <h2 class="mt-20 mb-10">TOP问题排行榜</h2>
      <TopQuestion :data="topQuestionData" :config="topQuestionConfig" />
    </template>
    <template v-else-if="activeTab === 'crowd'">
      <h2 class="mt-20 mb-10">人群特征</h2>
      <PopulationCharacteristics :data="populationData" />
    </template>
    <template v-else-if="activeTab === 'source'">
      <h2 class="mt-20 mb-10">数据来源分析</h2>
      <div v-if="dataSourceLoading" class="loading-container">
        <el-loading :loading="true" text="加载数据中..." />
      </div>
      <DataSourceAnalysis
        v-else
        :data="dataSourceList"
        :remarkData="dataSourceRemark"
        :loading="false"
        @download="onDownloadSourceData"
        @changeDatasource="onChangeDatasource"
        @datasourceSeeDetail="onDatasourceSeeDetail"
        @wordCloudChartClick="onWordCloudClick"
      />
    </template>
    <template v-else-if="activeTab === 'region'">
      <h2 class="mt-20 mb-10">地域分析</h2>
      <RegionAnalysis
        :data="regionData"
        :loading="false"
        @download="onDownloadRegionData"
        @see-area-detail="onSeeRegionDetail"
      />
    </template>
    <template v-if="activeTab === 'indicator'">
      <IndexAnalysis :data="analysisData" :remarkData="topoData" />
    </template>
    <template v-else-if="activeTab === 'detail'">
      <h2 class="mt-20 mb-10">原文明细</h2>
      <OriginalDetails
        :data="originalDetailData"
        :total="originalDetailTotal"
        :loading="originalDetailLoading"
        :page-size="originalDetailPageSize"
        :current-page="originalDetailCurrentPage"
        :index-type-name="originalDetailIndexType"
        title="原文明细"
        @page-change="handleOriginalDetailPageChange"
        @user-detail="handleOriginalDetailUserDetail"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { ElMessage } from 'element-plus'
import {
  ButtonGroup,
  VocTrendChart,
  ExperienceTips,
  TopQuestion,
  PopulationCharacteristics,
  RegionAnalysis,
  IndexAnalysis,
  OriginalDetails
} from '@/components'
import { BarAndPointChart } from '@/components/Charts'
import type { BarAndPointChartInstance } from '@/components/Charts'
import type { TopQuestionConfig } from '@/components/TopQuestion/types.d'
import DataSourceAnalysis from '@/components/DataSourceAnalysis/index.vue'
import type { IndexAnalysisData, TopologicalData } from '@/components/IndexAnalysis/types.d'
import type { OriginalTextItem } from '@/components/OriginalDetails/types.d'
import {
  getCustomerJourneyExperience,
  getTopIssues,
  getVocTrendChartData,
  getDemographics,
  getDataSourceAnalysis,
  getMentionTrend,
  getWordCloud,
  getRegionalAnalysis
} from '@/api/common'
import type {
  CustomerTagExperienceResponse,
  CustomerTagExperienceVo,
  KeywordData,
  VocDataItem,
  PopulationCharacteristicsResponse,
  DataSourceAnalysisItem,
  MentionTrendItem,
  WordCloudItem,
  ProvinceAnalysis
} from '@/api/common/index.d'
import {
  basicChartDataMock,
  chartDataMock,
  topQuestionDataMock,
  populationDataMock,
  dataSourceListMock,
  dataSourceRemarkMock,
  regionDataMock
} from '@/mock'

const tabs = [
  { label: '概览', value: 'overview' },
  { label: 'TOP问题', value: 'top' },
  { label: '人群特征', value: 'crowd' },
  { label: '数据源分析', value: 'source' },
  { label: '地域分析', value: 'region' },
  { label: '指标分析', value: 'indicator' },
  { label: '原文明细', value: 'detail' }
]
const activeTab = ref('overview')

// 图表引用
const basicChartRef = ref<BarAndPointChartInstance>()

// VOC体验值趋势图表数据
const basicChartData = ref<CustomerTagExperienceVo[]>(basicChartDataMock)

const chartData = ref<VocDataItem[]>(chartDataMock)

const topQuestionData = ref<KeywordData[]>(topQuestionDataMock)

const topQuestionConfig = ref<TopQuestionConfig>({
  showTitle: true,
  title: 'TOP问题排行榜',
  pageSize: 10,
  sortType: 'mention',
  emotionFilter: 'all',
  exportEnabled: true,
  keywordClickEnabled: true
})

const populationData = ref<PopulationCharacteristicsResponse>(populationDataMock)

// 数据源分析数据 - 初始使用mock数据
const dataSourceList = ref(dataSourceListMock)
const dataSourceRemark = ref(dataSourceRemarkMock)
const dataSourceLoading = ref(false)

// 地域分析数据 - 初始使用mock数据
const regionData = ref(transformRegionData(regionDataMock))

/**
 * 数据转换函数：将接口返回的DataSourceAnalysisItem转换为组件需要的格式
 */
function transformDataSourceList(apiData: DataSourceAnalysisItem[]) {
  const result = apiData.map(item => ({
    dataSource: item.dataSourceName || '',
    totalMentionValue: item.mentions || 0,
    positiveMentionValue: item.positiveMentions || 0,
    neutralMentionValue: item.neutralMentions || 0,
    negativeMentionValue: item.negativeMentions || 0
  }))
  return result
}

/**
 * 数据转换函数：将接口返回的ProvinceAnalysis转换为组件需要的格式
 */
function transformRegionData(apiData: ProvinceAnalysis[]) {
  return apiData.map(item => ({
    province: item.provinceName || '',
    experienceValue: item.experienceValue || 0,
    momExperienceValueRate: item.experienceValueMoM || 0,
    totalMentionValue: item.mentions || 0,
    momTotalMentionValueRate: item.mentionsMoM || 0
  }))
}

/**
 * 数据转换函数：将接口返回的MentionTrendItem转换为组件需要的格式
 */
function transformMentionTrend(apiData: MentionTrendItem[]) {
  return apiData.map(item => ({
    keyWord: item.date || '',
    positiveMentionValue: item.positiveMentions || 0,
    neutralMentionValue: item.neutralMentions || 0,
    negativeMentionValue: item.negativeMentions || 0
  }))
}

/**
 * 数据转换函数：将接口返回的WordCloudItem转换为组件需要的格式
 */
function transformWordCloud(apiData: WordCloudItem[]) {
  return apiData.map(item => ({
    name: item.hotWordName || '',
    value: item.mentions || 0
  }))
}

/**
 * 根据数据源生成不同的mock趋势数据
 */
function generateMockTrendData(dataSource?: string) {
  const baseData = [
    {
      keyWord: '2025-06-27',
      positiveMentionValue: 0,
      neutralMentionValue: 0,
      negativeMentionValue: 0
    },
    {
      keyWord: '2025-06-28',
      positiveMentionValue: 0,
      neutralMentionValue: 0,
      negativeMentionValue: 0
    },
    {
      keyWord: '2025-06-29',
      positiveMentionValue: 0,
      neutralMentionValue: 0,
      negativeMentionValue: 0
    },
    {
      keyWord: '2025-06-30',
      positiveMentionValue: 0,
      neutralMentionValue: 0,
      negativeMentionValue: 0
    },
    {
      keyWord: '2025-07-01',
      positiveMentionValue: 0,
      neutralMentionValue: 0,
      negativeMentionValue: 0
    },
    {
      keyWord: '2025-07-02',
      positiveMentionValue: 0,
      neutralMentionValue: 0,
      negativeMentionValue: 0
    },
    {
      keyWord: '2025-07-03',
      positiveMentionValue: 0,
      neutralMentionValue: 0,
      negativeMentionValue: 0
    }
  ]

  // 根据数据源生成不同的数据模式
  const multiplier = getDataSourceMultiplier(dataSource)

  return baseData.map((item, index) => ({
    ...item,
    positiveMentionValue: Math.floor((120 + index * 10) * multiplier),
    neutralMentionValue: Math.floor((80 + index * 5) * multiplier),
    negativeMentionValue: Math.floor((60 + index * 8) * multiplier)
  }))
}

/**
 * 根据数据源生成不同的mock词云数据
 */
function generateMockWordCloudData(dataSource?: string) {
  const baseWords = [
    { name: '空间大', value: 0 },
    { name: '油耗高', value: 0 },
    { name: '噪音大', value: 0 },
    { name: '外观漂亮', value: 0 },
    { name: '动力不足', value: 0 },
    { name: '性价比高', value: 0 },
    { name: '内饰精致', value: 0 },
    { name: '操控好', value: 0 },
    { name: '配置丰富', value: 0 },
    { name: '售后服务好', value: 0 }
  ]

  // 根据数据源生成不同的词云权重
  const multiplier = getDataSourceMultiplier(dataSource)
  const baseValues = [500, 400, 350, 300, 250, 200, 180, 160, 140, 120]

  return baseWords.map((item, index) => ({
    ...item,
    value: Math.floor(baseValues[index] * multiplier)
  }))
}

/**
 * 根据数据源返回不同的数据倍数，使不同数据源显示不同的数据模式
 */
function getDataSourceMultiplier(dataSource?: string) {
  switch (dataSource) {
    case '汽车之家':
      return 1.2
    case '懂车帝':
      return 0.8
    case '易车网':
      return 0.6
    case '微博':
      return 0.5
    case '小红书':
      return 0.4
    default:
      return 1.0
  }
}

/**
 * 获取数据源分析数据
 */
async function fetchDataSourceAnalysis() {
  try {
    dataSourceLoading.value = true
    const response = await getDataSourceAnalysis()

    if (response?.success && response?.result) {
      dataSourceList.value = transformDataSourceList(response.result)
      console.log('数据源分析数据获取成功，共', response.result.length, '条')
    } else {
      // 接口调用成功但无数据，使用mock数据
      console.warn('数据源分析接口返回无数据，使用mock数据')
      dataSourceList.value = dataSourceListMock
    }
  } catch (error) {
    // 接口调用异常，使用mock数据
    console.error('获取数据源分析数据失败，使用mock数据:', error)
    ElMessage.warning('获取数据源分析数据失败，显示模拟数据')
    dataSourceList.value = dataSourceListMock
  } finally {
    dataSourceLoading.value = false
  }
}

/**
 * 获取提及量趋势数据
 */
async function fetchMentionTrend(dataSource?: string) {
  console.log(`正在获取【${dataSource}】的提及量趋势数据`)
  try {
    const response = await getMentionTrend({ dataSource })

    if (response?.success && response?.result) {
      dataSourceRemark.value.trend = transformMentionTrend(response.result)
      console.log(`【${dataSource}】趋势数据获取成功:`, response.result)
    } else {
      console.warn(`【${dataSource}】提及量趋势接口返回无数据，使用mock数据`)
      dataSourceRemark.value.trend = generateMockTrendData(dataSource)
    }
  } catch (error) {
    console.error(`获取【${dataSource}】提及量趋势数据失败，使用mock数据:`, error)
    dataSourceRemark.value.trend = generateMockTrendData(dataSource)
  }
}

/**
 * 获取词云图数据
 */
async function fetchWordCloud(dataSource?: string) {
  console.log(`正在获取【${dataSource}】的词云图数据`)
  try {
    const response = await getWordCloud({ dataSource })

    if (response?.success && response?.result) {
      dataSourceRemark.value.wordCloud = transformWordCloud(response.result)
      console.log(`【${dataSource}】词云数据获取成功:`, response.result)
    } else {
      console.warn(`【${dataSource}】词云图接口返回无数据，使用mock数据`)
      dataSourceRemark.value.wordCloud = generateMockWordCloudData(dataSource)
    }
  } catch (error) {
    console.error(`获取【${dataSource}】词云图数据失败，使用mock数据:`, error)
    dataSourceRemark.value.wordCloud = generateMockWordCloudData(dataSource)
  }
}

function handleTabChange(val: string) {
  // 当切换到数据源分析标签时，加载数据
  if (val === 'source') {
    fetchDataSourceAnalysis()
    // 获取默认数据源的趋势和词云数据
    const defaultDataSource = dataSourceList.value[0]?.dataSource
    if (defaultDataSource) {
      fetchMentionTrend(defaultDataSource)
      fetchWordCloud(defaultDataSource)
    }
  }
}

// 原文明细事件处理函数
function handleOriginalDetailPageChange(params: { pageNum: number; pageSize?: number }) {
  originalDetailCurrentPage.value = params.pageNum
  if (params.pageSize) {
    originalDetailPageSize.value = params.pageSize
  }

  // 模拟加载数据
  originalDetailLoading.value = true
  setTimeout(() => {
    ElMessage.success(`已切换到第${params.pageNum}页`)
    originalDetailLoading.value = false
  }, 1000)
}

function handleOriginalDetailUserDetail(params: { oneId: string; userName: string }) {
  ElMessage.info(`查看用户详情: ${params.userName} (${params.oneId})`)
  // 这里可以打开用户详情弹窗或跳转到用户详情页面
}

// 事件处理函数
const handleBarClick = (payload: { date: string }) => {
  ElMessage.success(`点击了: ${payload.date}`)
  console.log('点击了:', payload.date)
}

function onDownloadSourceData() {
  ElMessage.success('下载数据功能待实现')
}

function onChangeDatasource(ds: string) {
  console.log(`主页面接收到数据源切换事件: ${ds}`)
  ElMessage.info(`切换数据源: ${ds}`)
  // 切换数据源时，重新获取该数据源的趋势和词云数据
  fetchMentionTrend(ds).then(() => {
    console.log(`【${ds}】趋势数据更新完成:`, dataSourceRemark.value.trend)
  })
  fetchWordCloud(ds).then(() => {
    console.log(`【${ds}】词云数据更新完成:`, dataSourceRemark.value.wordCloud)
  })
}

function onDatasourceSeeDetail(item: any) {
  ElMessage.info('查看趋势详情')
  console.log('趋势详情数据:', item)
}

function onWordCloudClick(word: any) {
  ElMessage.info(`点击词条: ${word.name}`)
  console.log('词云点击数据:', word)
}

// 地域分析相关函数
function onDownloadRegionData(command: string, type: string) {
  ElMessage.success(`正在导出地域分析${type.toUpperCase()}文件...`)
}

function onSeeRegionDetail(province: string) {
  ElMessage.info(`查看${province}地域详情`)
}

const analysisData = ref<IndexAnalysisData>({
  detail: [
    {
      indexId: '1',
      keyWord: '空间',
      totalMentionValue: 300,
      experienceValue: 80,
      momTotalMentionValueRate: 10,
      momExperienceValueRate: undefined,
      nowIndex: 1,
      momIndex: '—'
    },
    {
      indexId: '2',
      keyWord: '动力',
      totalMentionValue: 250,
      experienceValue: 75,
      momTotalMentionValueRate: -5,
      momExperienceValueRate: undefined,
      nowIndex: 2,
      momIndex: '—'
    }
  ],
  detailMom: []
})

const topoData = ref<TopologicalData[]>([
  {
    name: '空间',
    value: 300,
    indexId: '1',
    children: [
      { name: '后排空间', value: 150, indexId: '1-1' },
      { name: '储物空间', value: 150, indexId: '1-2' }
    ]
  }
])

// 原文明细相关数据
const originalDetailData = ref<OriginalTextItem[]>([
  {
    id: '1',
    dataSourceName: '汽车之家-用户发帖',
    brandName: '长安汽车',
    seriesName: 'CS75 PLUS',
    createTime: '2024-12-28 10:30:00',
    vin: 'LS1234567890',
    isCarOwner: '是',
    oneId: 'user001',
    user: '车友小张',
    name: '张三',
    postsTitle: '长安CS75 PLUS驾驶体验分享',
    postsContent:
      '这车的空间确实不错，后排坐三个成年人也不会太挤。动力方面1.5T的发动机在城市里开完全够用，起步比较轻快。不过油耗稍微有点高，市区开下来要8个多油。',
    analysisResult: {
      extractedinfo: [
        {
          standardkeyword: '空间大',
          extractedsense: '正面',
          extracteddomain: '全领域业务'
        },
        {
          standardkeyword: '动力够用',
          extractedsense: '正面',
          extracteddomain: '全领域业务'
        },
        {
          standardkeyword: '油耗高',
          extractedsense: '负面',
          extracteddomain: '全领域业务'
        }
      ]
    }
  },
  {
    id: '2',
    dataSourceName: '联络中心热线服务',
    brandName: '长安汽车',
    seriesName: 'UNI-T',
    createTime: '2024-12-28 09:15:00',
    vin: 'LS0987654321',
    isCarOwner: '是',
    oneId: 'user002',
    user: '李女士',
    name: '李四',
    title: '车机系统卡顿问题反馈',
    content:
      '您好，我的UNI-T车机系统经常出现卡顿现象，特别是使用导航的时候。有时候触摸屏幕没有反应，需要重启车机才能恢复正常。希望能够得到解决。',
    analysisResult: {
      extractedinfo: [
        {
          standardkeyword: '车机卡顿',
          extractedsense: '负面',
          extracteddomain: '全领域业务'
        },
        {
          standardkeyword: '触摸屏无反应',
          extractedsense: '负面',
          extracteddomain: '全领域业务'
        }
      ]
    }
  },
  {
    id: '3',
    dataSourceName: '懂车帝-口碑描述',
    brandName: '长安汽车',
    seriesName: 'CS55 PLUS',
    createTime: '2024-12-28 08:45:00',
    isCarOwner: '否',
    title: '外观设计点评',
    content:
      '长安CS55 PLUS的外观设计确实很不错，前脸很有科技感，LED大灯的造型也很犀利。侧面线条流畅，整体看起来比较年轻时尚。内饰做工也还可以，软性材质用得比较多。',
    analysisResult: {
      extractedinfo: [
        {
          standardkeyword: '外观漂亮',
          extractedsense: '正面',
          extracteddomain: '全领域业务'
        },
        {
          standardkeyword: '内饰做工好',
          extractedsense: '正面',
          extracteddomain: '全领域业务'
        }
      ]
    }
  },
  {
    id: '4',
    dataSourceName: '长安汽车直评',
    brandName: '长安汽车',
    seriesName: 'CS75 PLUS',
    createTime: '2024-12-28 07:20:00',
    vin: 'LS5678901234',
    isCarOwner: '是',
    oneId: 'user004',
    user: '王先生',
    name: '王五',
    title: '购车满意度调研',
    answer_content:
      '总体来说比较满意，性价比不错。空间够用，配置也比较丰富。就是胎噪有点大，跑高速的时候比较明显。',
    analysisResult: {
      extractedinfo: [
        {
          standardkeyword: '性价比高',
          extractedsense: '正面',
          extracteddomain: '全领域业务'
        },
        {
          standardkeyword: '胎噪大',
          extractedsense: '负面',
          extracteddomain: '全领域业务'
        }
      ]
    }
  },
  {
    id: '5',
    dataSourceName: '车机端-意见反馈',
    brandName: '长安汽车',
    seriesName: 'UNI-V',
    createTime: '2024-12-28 06:30:00',
    vin: 'LS3456789012',
    isCarOwner: '是',
    title: '语音识别准确率问题',
    content:
      '车机的语音识别功能准确率不够高，经常识别错误，特别是在有噪音的环境下。建议优化语音算法，提高识别准确率。',
    analysisResult: {
      extractedinfo: [
        {
          standardkeyword: '语音识别差',
          extractedsense: '负面',
          extracteddomain: '全领域业务'
        }
      ]
    }
  }
])

const originalDetailTotal = ref(158)
const originalDetailLoading = ref(false)
const originalDetailPageSize = ref(10)
const originalDetailCurrentPage = ref(1)
const originalDetailIndexType = ref('全领域业务')

const customerJourneyExperience = ref<CustomerTagExperienceResponse>({
  totalExperienceValue: 0,
  totalExperienceValueMoM: 0,
  totalMentions: 0,
  totalMentionsMoM: 0
})

const getCustomerJourneyExperienceData = async () => {
  const res2 = await getCustomerJourneyExperience({})
  customerJourneyExperience.value.totalExperienceValue = res2.result.totalExperienceValue
  customerJourneyExperience.value.totalExperienceValueMoM = res2.result.totalExperienceValueMoM
  customerJourneyExperience.value.totalMentions = res2.result.totalMentions
  customerJourneyExperience.value.totalMentionsMoM = res2.result.totalMentionsMoM
  if (res2.result.customerTagExperienceList && res2.result.customerTagExperienceList.length > 0) {
    basicChartData.value = res2.result.customerTagExperienceList
  } else {
    basicChartData.value = basicChartDataMock
  }
}

const getVocTrendChartDataData = async () => {
  const res = await getVocTrendChartData({})
  if (res.result.length > 0) {
    chartData.value = res.result
  } else {
    chartData.value = chartDataMock
  }
}

const getTopIssuesData = async () => {
  const res = await getTopIssues({})
  if (res.result.length > 0) {
    topQuestionData.value = res.result
  } else {
    topQuestionData.value = topQuestionDataMock
  }
}

const getDemographicsData = async () => {
  try {
    const res = await getDemographics({})
    if (res.result) {
      populationData.value = res.result
    } else {
      populationData.value = populationDataMock
    }
  } catch (error) {
    console.error('获取人群特征数据失败:', error)
    ElMessage.error('获取人群特征数据失败，使用默认数据')
    populationData.value = populationDataMock
  }
}

/**
 * 获取地域分析数据
 */
const getRegionalAnalysisData = async () => {
  try {
    const res = await getRegionalAnalysis({})
    if (res.result && res.result.length > 0) {
      regionData.value = transformRegionData(res.result)
    } else {
      regionData.value = transformRegionData(regionDataMock)
    }
  } catch (error) {
    console.error('获取地域分析数据失败:', error)
    ElMessage.error('获取地域分析数据失败，使用默认数据')
    regionData.value = transformRegionData(regionDataMock)
  }
}

onMounted(async () => {
  getVocTrendChartDataData()
  getCustomerJourneyExperienceData()
  getTopIssuesData()
  getDemographicsData()
  getRegionalAnalysisData()

  // 预加载数据源分析数据
  await fetchDataSourceAnalysis()
  // 获取默认第一个数据源的趋势和词云数据
  const defaultDataSource = dataSourceList.value[0]?.dataSource
  if (defaultDataSource) {
    fetchMentionTrend(defaultDataSource)
    fetchWordCloud(defaultDataSource)
  }
})
</script>

<style lang="scss" scoped>
.home-page {
  padding: 32px;

  .section-title {
    margin: 20px 0 15px 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
  }

  .chart-section {
    margin-bottom: 30px;

    .chart-container {
      display: flex;
      gap: 20px;
      background: #fff;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .tips-sidebar {
        width: 200px;
        flex-shrink: 0;
      }

      .chart-main {
        flex: 1;
        min-height: 400px;
      }
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}
</style>
