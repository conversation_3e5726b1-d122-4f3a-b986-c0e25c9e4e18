<template>
  <div class="bar-or-line-chart-test">
    <h2>BarOrLineChart 组件测试页面</h2>

    <!-- 基础测试 -->
    <div class="test-section">
      <h3>基础功能测试</h3>
      <div class="chart-container">
        <BarOrLineChart :data="testData" width="100%" height="400" @bar-click="handleBarClick" />
      </div>
    </div>

    <!-- 详情模式测试 -->
    <div class="test-section">
      <h3>详情模式测试</h3>
      <div class="chart-container">
        <BarOrLineChart
          :data="testData"
          :need-details="true"
          width="100%"
          height="400"
          @bar-click="handleBarClick"
        />
      </div>
    </div>

    <!-- 响应式测试 -->
    <div class="test-section">
      <h3>响应式布局测试</h3>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="chart-container">
            <BarOrLineChart
              :data="testData"
              width="100%"
              height="300"
              @bar-click="handleBarClick"
            />
          </div>
        </el-col>
        <el-col :span="12">
          <div class="chart-container">
            <BarOrLineChart
              :data="testData"
              width="100%"
              height="300"
              :need-details="true"
              @bar-click="handleBarClick"
            />
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 点击事件测试 -->
    <div class="test-section">
      <h3>点击事件测试</h3>
      <div class="event-log">
        <h4>点击事件日志：</h4>
        <div v-if="clickLogs.length === 0" class="no-logs">暂无点击事件</div>
        <div v-else class="log-list">
          <div v-for="(log, index) in clickLogs" :key="index" class="log-item">
            {{ log }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import BarOrLineChart from '@/components/Charts/BarOrLineChart/index.vue'

// 测试数据
const testData = ref([
  {
    keyWord: '2024-01',
    positiveMentionValue: 1200,
    neutralMentionValue: 800,
    negativeMentionValue: -300,
    experienceValue: 85.5
  },
  {
    keyWord: '2024-02',
    positiveMentionValue: 1500,
    neutralMentionValue: 900,
    negativeMentionValue: -200,
    experienceValue: 88.2
  },
  {
    keyWord: '2024-03',
    positiveMentionValue: 1800,
    neutralMentionValue: 1100,
    negativeMentionValue: -400,
    experienceValue: 82.1
  },
  {
    keyWord: '2024-04',
    positiveMentionValue: 1600,
    neutralMentionValue: 950,
    negativeMentionValue: -250,
    experienceValue: 86.8
  },
  {
    keyWord: '2024-05',
    positiveMentionValue: 2000,
    neutralMentionValue: 1200,
    negativeMentionValue: -350,
    experienceValue: 89.3
  }
])

// 点击事件日志
const clickLogs = ref<string[]>([])

// 处理点击事件
const handleBarClick = (payload: { date: string }) => {
  const log = `点击了 ${payload.date} 的数据 - ${new Date().toLocaleTimeString()}`
  clickLogs.value.unshift(log)

  // 保持最多10条日志
  if (clickLogs.value.length > 10) {
    clickLogs.value = clickLogs.value.slice(0, 10)
  }

  ElMessage.success(`点击了 ${payload.date} 的数据`)
  console.log('BarOrLineChart 点击事件:', payload)
}
</script>

<style scoped lang="scss">
.bar-or-line-chart-test {
  padding: 20px;

  h2 {
    color: #333;
    margin-bottom: 30px;
    font-size: 24px;
    text-align: center;
  }

  .test-section {
    margin-bottom: 40px;

    h3 {
      color: #666;
      margin-bottom: 20px;
      font-size: 18px;
      border-left: 4px solid #0077ff;
      padding-left: 10px;
    }

    .chart-container {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.03);
      padding: 20px;
      min-height: 400px;
    }

    .event-log {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.03);
      padding: 20px;

      h4 {
        color: #333;
        margin-bottom: 15px;
        font-size: 16px;
      }

      .no-logs {
        color: #999;
        text-align: center;
        padding: 20px;
        font-style: italic;
      }

      .log-list {
        max-height: 200px;
        overflow-y: auto;

        .log-item {
          padding: 8px 12px;
          margin-bottom: 8px;
          background: #f5f5f5;
          border-radius: 4px;
          font-family: monospace;
          font-size: 14px;
          color: #333;
          border-left: 3px solid #0077ff;
        }
      }
    }
  }
}
</style>
