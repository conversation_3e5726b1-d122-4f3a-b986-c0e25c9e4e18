<template>
  <div class="chart-demo">
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <span>柱状图加散点图组件演示</span>
          <el-button type="primary" @click="downloadChart">下载图表</el-button>
        </div>
      </template>

      <h3>VOC体验值趋势图表</h3>
      <div class="flex">
        <div class="w-200 flex-none">
          <ExperienceTips :data="experienceTipsData" />
        </div>
        <BarAndPointChart
          ref="basicChartRef"
          :data="basicChartData"
          :need-details="true"
          @bar-click="handleBarClick"
          class="flex-1"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { BarAndPointChart } from '@/components/Charts'
import type { BarAndPointChartInstance } from '@/components/Charts'
import { ExperienceTips } from '@/components'

// 组件名称定义
defineOptions({
  name: 'ChartDemo'
})

// 图表引用
const basicChartRef = ref<BarAndPointChartInstance>()

// ExperienceTips 数据
const experienceTipsData = ref({
  totalExperienceValue: 75.6,
  totalExperienceValueMoM: 2.3,
  totalMentions: 8543,
  totalMentionsMoM: 1.5
})

// VOC体验值趋势图表数据
const basicChartData = ref([
  {
    keyWord: '全旅程',
    indexId: 'L0_1',
    experienceValue: 75.6,
    totalMentionValue: 8543,
    positiveMentionValue: 4512,
    neutralMentionValue: 2845,
    negativeMentionValue: 1186,
    momExperienceValueRate: 2.3,
    momTotalMentionValueRate: 1.5,
    negativeMentionRate: 15.2,
    momNegativeMentionRate: -0.8,
    positiveMentionRate: 52.8,
    standardKeywordMentionValue: 6789,
    dwdEvtWorkOrderDtlEntities: [
      {
        keyWord: '产品',
        indexId: 'L1_1',
        experienceValue: 78.2,
        totalMentionValue: 5432,
        positiveMentionValue: 2876,
        neutralMentionValue: 1543,
        negativeMentionValue: 1013,
        momExperienceValueRate: 2.8,
        momTotalMentionValueRate: 2.1,
        negativeMentionRate: 18.7,
        momNegativeMentionRate: 0.5,
        positiveMentionRate: 53.0,
        standardKeywordMentionValue: 4346
      },
      {
        keyWord: '服务',
        indexId: 'L1_2',
        experienceValue: 72.1,
        totalMentionValue: 3111,
        positiveMentionValue: 1636,
        neutralMentionValue: 1302,
        negativeMentionValue: 173,
        momExperienceValueRate: 1.8,
        momTotalMentionValueRate: 0.9,
        negativeMentionRate: 5.6,
        momNegativeMentionRate: -2.1,
        positiveMentionRate: 52.6,
        standardKeywordMentionValue: 2443
      }
    ]
  },
  {
    keyWord: '外观',
    indexId: 'L2_1',
    experienceValue: 82.5,
    totalMentionValue: 2156,
    positiveMentionValue: 1456,
    neutralMentionValue: 543,
    negativeMentionValue: 157,
    momExperienceValueRate: 3.2,
    momTotalMentionValueRate: 2.8,
    negativeMentionRate: 7.3,
    momNegativeMentionRate: -1.2,
    positiveMentionRate: 67.5,
    standardKeywordMentionValue: 1890
  },
  {
    keyWord: '内饰',
    indexId: 'L2_2',
    experienceValue: 76.8,
    totalMentionValue: 1876,
    positiveMentionValue: 987,
    neutralMentionValue: 654,
    negativeMentionValue: 235,
    momExperienceValueRate: 1.5,
    momTotalMentionValueRate: 1.2,
    negativeMentionRate: 12.5,
    momNegativeMentionRate: 0.8,
    positiveMentionRate: 52.6,
    standardKeywordMentionValue: 1543
  },
  {
    keyWord: '空间',
    indexId: 'L2_3',
    experienceValue: 85.2,
    totalMentionValue: 2345,
    positiveMentionValue: 1654,
    neutralMentionValue: 543,
    negativeMentionValue: 148,
    momExperienceValueRate: 4.1,
    momTotalMentionValueRate: 3.5,
    negativeMentionRate: 6.3,
    momNegativeMentionRate: -2.1,
    positiveMentionRate: 70.5,
    standardKeywordMentionValue: 1987
  },
  {
    keyWord: '配置',
    indexId: 'L2_4',
    experienceValue: 79.3,
    totalMentionValue: 1654,
    positiveMentionValue: 876,
    neutralMentionValue: 543,
    negativeMentionValue: 235,
    momExperienceValueRate: 2.1,
    momTotalMentionValueRate: 1.8,
    negativeMentionRate: 14.2,
    momNegativeMentionRate: 1.5,
    positiveMentionRate: 52.9,
    standardKeywordMentionValue: 1321
  },
  {
    keyWord: '动力',
    indexId: 'L2_5',
    experienceValue: 73.6,
    totalMentionValue: 1987,
    positiveMentionValue: 987,
    neutralMentionValue: 654,
    negativeMentionValue: 346,
    momExperienceValueRate: 0.8,
    momTotalMentionValueRate: 0.5,
    negativeMentionRate: 17.4,
    momNegativeMentionRate: 2.3,
    positiveMentionRate: 49.7,
    standardKeywordMentionValue: 1543
  },
  {
    keyWord: '操控',
    indexId: 'L2_6',
    experienceValue: 77.9,
    totalMentionValue: 1432,
    positiveMentionValue: 765,
    neutralMentionValue: 432,
    negativeMentionValue: 235,
    momExperienceValueRate: 2.5,
    momTotalMentionValueRate: 2.1,
    negativeMentionRate: 16.4,
    momNegativeMentionRate: 1.2,
    positiveMentionRate: 53.4,
    standardKeywordMentionValue: 1198
  },
  {
    keyWord: '舒适性',
    indexId: 'L2_7',
    experienceValue: 74.2,
    totalMentionValue: 1234,
    positiveMentionValue: 654,
    neutralMentionValue: 432,
    negativeMentionValue: 148,
    momExperienceValueRate: 1.2,
    momTotalMentionValueRate: 0.9,
    negativeMentionRate: 12.0,
    momNegativeMentionRate: -0.8,
    positiveMentionRate: 53.0,
    standardKeywordMentionValue: 1086
  },
  {
    keyWord: '油耗',
    indexId: 'L2_8',
    experienceValue: 68.5,
    totalMentionValue: 1876,
    positiveMentionValue: 765,
    neutralMentionValue: 654,
    negativeMentionValue: 457,
    momExperienceValueRate: -1.8,
    momTotalMentionValueRate: -2.1,
    negativeMentionRate: 24.4,
    momNegativeMentionRate: 3.2,
    positiveMentionRate: 40.8,
    standardKeywordMentionValue: 1419
  },
  {
    keyWord: '质量',
    indexId: 'L2_9',
    experienceValue: 71.8,
    totalMentionValue: 2345,
    positiveMentionValue: 1234,
    neutralMentionValue: 765,
    negativeMentionValue: 346,
    momExperienceValueRate: 0.5,
    momTotalMentionValueRate: 0.3,
    negativeMentionRate: 14.8,
    momNegativeMentionRate: 1.8,
    positiveMentionRate: 52.6,
    standardKeywordMentionValue: 1999
  },
  {
    keyWord: '销售服务',
    indexId: 'L2_10',
    experienceValue: 81.4,
    totalMentionValue: 1654,
    positiveMentionValue: 987,
    neutralMentionValue: 543,
    negativeMentionValue: 124,
    momExperienceValueRate: 3.5,
    momTotalMentionValueRate: 3.2,
    negativeMentionRate: 7.5,
    momNegativeMentionRate: -1.5,
    positiveMentionRate: 59.7,
    standardKeywordMentionValue: 1530
  },
  {
    keyWord: '售后服务',
    indexId: 'L2_11',
    experienceValue: 77.8,
    totalMentionValue: 1432,
    positiveMentionValue: 765,
    neutralMentionValue: 432,
    negativeMentionValue: 235,
    momExperienceValueRate: 2.1,
    momTotalMentionValueRate: 1.8,
    negativeMentionRate: 16.4,
    momNegativeMentionRate: 0.9,
    positiveMentionRate: 53.4,
    standardKeywordMentionValue: 1197
  }
])

// 事件处理函数
const handleBarClick = (payload: { date: string }) => {
  ElMessage.success(`点击了: ${payload.date}`)
  console.log('点击了:', payload.date)
}

// 下载图表
const downloadChart = () => {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
  basicChartRef.value?.downloadChart(`chart-${timestamp}.png`)
  ElMessage.success('图表下载成功')
}
</script>

<style lang="scss" scoped>
.chart-demo {
  padding: 20px;

  .demo-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    h3 {
      margin: 0 0 15px 0;
      color: #333;
      font-size: 16px;
      font-weight: 600;
    }
  }
}
</style>
