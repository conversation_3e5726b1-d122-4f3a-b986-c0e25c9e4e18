<template>
  <div class="data-analysis">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card>
          <template #header>
            <span>总用户数</span>
          </template>
          <div class="stat-number">1,234</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <template #header>
            <span>今日活跃</span>
          </template>
          <div class="stat-number">567</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <template #header>
            <span>本月新增</span>
          </template>
          <div class="stat-number">89</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <template #header>
            <span>系统负载</span>
          </template>
          <div class="stat-number">23%</div>
        </el-card>
      </el-col>
    </el-row>

    <el-card style="margin-top: 20px">
      <template #header>
        <span>数据趋势</span>
      </template>
      <div class="chart-container">
        <div class="chart-placeholder">图表区域 - 这里可以集成 ECharts 等图表库</div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'DataAnalysis'
})
</script>

<style lang="scss" scoped>
.data-analysis {
  height: 100%;

  .stat-number {
    font-size: 32px;
    font-weight: bold;
    color: #409eff;
    text-align: center;
  }

  .chart-container {
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .chart-placeholder {
    color: #909399;
    font-size: 16px;
    text-align: center;
  }
}
</style>
