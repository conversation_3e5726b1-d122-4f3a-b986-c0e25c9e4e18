<template>
  <div class="data-report">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>数据报表</span>
          <div class="header-actions">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="margin-right: 10px"
            />
            <el-button type="primary" @click="generateReport">
              <el-icon><Download /></el-icon>
              生成报表
            </el-button>
          </div>
        </div>
      </template>

      <el-table :data="reportData" style="width: 100%">
        <el-table-column prop="date" label="日期" />
        <el-table-column prop="users" label="用户数" />
        <el-table-column prop="orders" label="订单数" />
        <el-table-column prop="revenue" label="收入" />
        <el-table-column prop="growth" label="增长率">
          <template #default="{ row }">
            <span :class="{ 'text-success': row.growth > 0, 'text-danger': row.growth < 0 }">
              {{ row.growth > 0 ? '+' : '' }}{{ row.growth }}%
            </span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

defineOptions({
  name: 'DataReport'
})

const dateRange = ref([])

const reportData = ref([
  { date: '2024-01-01', users: 1200, orders: 150, revenue: '15,000', growth: 5.2 },
  { date: '2024-01-02', users: 1250, orders: 160, revenue: '16,000', growth: 6.7 },
  { date: '2024-01-03', users: 1180, orders: 145, revenue: '14,500', growth: -3.2 },
  { date: '2024-01-04', users: 1300, orders: 170, revenue: '17,000', growth: 8.1 }
])

const generateReport = () => {
  // TODO: 实现报表生成功能
}
</script>

<style lang="scss" scoped>
.data-report {
  height: 100%;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-actions {
      display: flex;
      align-items: center;
    }
  }

  .text-success {
    color: #67c23a;
  }

  .text-danger {
    color: #f56c6c;
  }
}
</style>
