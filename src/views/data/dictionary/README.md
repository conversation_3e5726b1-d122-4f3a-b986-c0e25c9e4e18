# 数据字典维护模块

## 📋 功能概述

数据字典维护模块是系统中用于管理和维护各种数据字典的核心功能模块。提供了完整的字典类型管理和字典项管理功能，支持系统中其他模块的数据标准化需求。

## 🎯 主要功能

### 字典类型管理

- ✅ **增删改查**：完整的字典类型 CRUD 操作
- ✅ **搜索过滤**：支持按字典名称、编码进行搜索
- ✅ **类型分类**：支持字符串、数字、布尔值三种数据类型
- ✅ **分页展示**：支持分页浏览和每页条数设置

### 字典项管理

- ✅ **关联管理**：基于选中字典类型管理其下的字典项
- ✅ **状态控制**：支持字典项的启用/停用状态管理
- ✅ **排序支持**：支持字典项的排序值设置
- ✅ **实时联动**：选择字典类型后实时加载对应字典项

### 界面交互

- ✅ **左右布局**：左侧字典类型列表，右侧字典项详情面板
- ✅ **点击联动**：点击字典类型名称展开右侧字典项面板
- ✅ **动画效果**：右侧面板滑入滑出动画效果
- ✅ **响应式设计**：适配不同屏幕尺寸

## 🏗️ 技术架构

### 目录结构

```
src/views/data/dictionary/
├── index.vue                   # 主页面
├── components/
│   ├── DictTypeTable.vue       # 字典类型表格组件
│   ├── DictItemPanel.vue       # 字典项面板组件
│   ├── DictFormDialog.vue      # 字典类型表单弹窗
│   └── DictItemFormDialog.vue  # 字典项表单弹窗
└── README.md                   # 模块说明文档
```

### API 接口

```
src/api/dictionary/
├── index.ts          # API 接口实现
└── index.d.ts        # TypeScript 类型定义
```

### 数据模型

#### 字典类型 (DictType)

```typescript
interface DictListVo {
  id: string // 字典ID
  dictName: string // 字典名称
  dictCode: string // 字典编码（唯一）
  type: number // 数据类型：0-字符串，1-数字，2-布尔值
  description?: string // 字典描述
  operator?: string // 创建人
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  itemCount?: number // 字典项数量
}
```

#### 字典项 (DictItem)

```typescript
interface DictItemListVo {
  id: string // 字典项ID
  dictId: string // 字典ID（关联字段）
  itemText: string // 显示文本
  itemKey: string // 字典项键
  itemValue: string // 字典项值
  description?: string // 描述信息
  sortOrder: number // 排序值
  status: number // 启用状态：1-启用，0-停用
  operator?: string // 操作人
  createTime?: string // 创建时间
}
```

## 🔧 使用指南

### 基本操作流程

1. **访问页面**：在数据管理菜单下点击"数据字典"
2. **浏览字典**：左侧表格显示所有字典类型，支持搜索和分页
3. **选择字典**：点击字典名称或"查看"按钮，右侧展开字典项面板
4. **管理字典项**：在右侧面板中可以查看、新增、编辑、删除字典项
5. **维护字典**：可以新增、编辑、删除字典类型

### 操作说明

#### 字典类型管理

- **新增**：点击"添加"按钮，填写字典信息并选择数据类型
- **编辑**：点击表格操作列的"编辑"按钮
- **删除**：点击"删除"按钮，需确认操作（会同时删除所有字典项）
- **搜索**：输入字典名称或编码进行过滤查询

#### 字典项管理

- **查看字典项**：点击字典类型名称展开右侧面板
- **新增字典项**：在右侧面板点击"添加"按钮
- **编辑字典项**：点击字典项操作列的"编辑"按钮
- **删除字典项**：点击"删除"按钮进行删除
- **状态管理**：可以设置字典项为启用或停用状态

## 🎨 界面特性

### 视觉设计

- **现代化界面**：采用扁平化设计风格
- **颜色标识**：不同数据类型使用不同颜色标签
- **状态展示**：字典项状态用绿色（启用）和红色（停用）区分
- **响应式布局**：自适应桌面和移动端显示

### 交互体验

- **流畅动画**：右侧面板滑入滑出动画效果
- **即时反馈**：操作成功后即时显示提示信息
- **智能校验**：表单提交前进行完整性和格式校验
- **便捷操作**：支持键盘回车搜索、批量操作等

## ⚡ 性能优化

### 数据加载

- **按需加载**：字典项只在选中字典时才加载
- **分页处理**：大量数据采用分页方式避免性能问题
- **缓存机制**：避免重复请求相同数据

### 用户体验

- **加载状态**：数据加载时显示 loading 效果
- **错误处理**：网络异常时显示友好提示信息
- **防抖搜索**：搜索输入防抖处理，避免频繁请求

## 🔗 相关页面

- **路由地址**：`/data/dictionary`
- **菜单位置**：数据管理 > 数据字典
- **权限要求**：需要数据管理相关权限

## 📊 接口依赖

### 字典类型接口

- `POST /insDict/dict-list` - 分页查询字典列表
- `POST /insDict/insert` - 新增字典
- `POST /insDict/update` - 更新字典
- `DELETE /insDict/delete/{id}` - 删除字典

### 字典项接口

- `POST /insDictItem/dict-item-list` - 分页查询字典项列表
- `GET /insDictItem/dict-items-by-dict/{dictId}` - 根据字典ID查询字典项
- `POST /insDictItem/insert` - 新增字典项
- `POST /insDictItem/update` - 更新字典项
- `DELETE /insDictItem/delete/{id}` - 删除字典项

## 🚀 未来扩展

### 计划功能

- [ ] **批量导入**：支持 Excel 文件批量导入字典数据
- [ ] **数据导出**：支持导出字典数据为 Excel 或 JSON 格式
- [ ] **版本控制**：字典数据变更的版本追踪
- [ ] **使用统计**：统计字典项在系统中的使用情况

### 技术改进

- [ ] **虚拟滚动**：大数据量时使用虚拟滚动提升性能
- [ ] **离线缓存**：支持离线模式下的数据缓存
- [ ] **实时同步**：多用户操作时的实时数据同步

---

**开发团队**：VOC 管理系统开发组
**文档更新**：2024年12月
**版本**：v1.0.0
