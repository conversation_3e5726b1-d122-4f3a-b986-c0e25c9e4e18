<template>
  <div class="role-management">
    <!-- 搜索表单 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" label-width="80px" :inline="true">
        <el-form-item label="角色名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入角色名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="角色编码">
          <el-input
            v-model="searchForm.code"
            placeholder="请输入角色编码"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="角色级别">
          <el-select
            v-model="searchForm.level"
            placeholder="请选择级别"
            clearable
            style="width: 150px"
          >
            <el-option label="超级管理员" :value="1" />
            <el-option label="管理员" :value="2" />
            <el-option label="普通用户" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="启用" :value="RoleStatus.ENABLE" />
            <el-option label="禁用" :value="RoleStatus.DISABLE" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作工具栏 -->
    <el-card class="toolbar-card" shadow="never">
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增角色
          </el-button>
          <el-button
            type="danger"
            :disabled="selectedRoleIds.length === 0"
            @click="handleBatchDelete"
          >
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-tooltip content="刷新">
            <el-button circle @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </el-card>

    <!-- 角色统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><UserFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ systemStore.roleTotal || 0 }}</div>
                <div class="stat-label">总角色数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon active">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ systemStore.enabledRoles.length || 0 }}</div>
                <div class="stat-label">启用角色</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon disabled">
                <el-icon><Close /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ systemStore.disabledRoles.length || 0 }}</div>
                <div class="stat-label">禁用角色</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon permissions">
                <el-icon><Key /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ systemStore.permissions.length || 0 }}</div>
                <div class="stat-label">权限总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 角色表格 -->
    <el-card class="table-card" shadow="never">
      <el-table
        v-loading="systemStore.roleLoading"
        :data="systemStore.roles"
        @selection-change="handleSelectionChange"
        stripe
        max-height="500"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="id" label="ID" width="80" />

        <el-table-column label="角色信息" min-width="200">
          <template #default="{ row }">
            <div class="role-info">
              <div class="role-icon">
                <el-icon :size="24">
                  <component :is="getRoleIcon(row.level)" />
                </el-icon>
              </div>
              <div class="role-details">
                <div class="role-name">{{ row.name }}</div>
                <div class="role-code">{{ row.code }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />

        <el-table-column label="级别" width="120">
          <template #default="{ row }">
            <el-tag :type="getRoleLevelTagType(row.level)">
              {{ getRoleLevelText(row.level) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="权限数量" width="100">
          <template #default="{ row }">
            <el-text type="primary">{{ row.permissions.length }}</el-text>
          </template>
        </el-table-column>

        <el-table-column label="数据权限" width="120">
          <template #default="{ row }">
            <el-tag size="small">{{ getDataScopeText(row.dataScope) }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleEdit(row)"> 编辑 </el-button>
            <el-button size="small" type="primary" @click="handlePermission(row)">
              权限配置
            </el-button>
            <el-dropdown @command="(command: string) => handleAction(command, row)">
              <el-button class="ml-12" link>
                更多<el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="copy">复制角色</el-dropdown-item>
                  <el-dropdown-item command="toggleStatus">
                    {{ row.status === RoleStatus.ENABLE ? '禁用' : '启用' }}
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" :disabled="row.code === 'SUPER_ADMIN'" divided>
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="systemStore.roleQueryParams.page"
          v-model:page-size="systemStore.roleQueryParams.pageSize"
          :total="systemStore.roleTotal"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 角色表单对话框 -->
    <RoleFormDialog
      v-model:visible="formDialogVisible"
      :role-data="currentEditRole"
      :is-edit="isEditMode"
      @success="handleFormSuccess"
    />

    <!-- 权限配置对话框 -->
    <PermissionDialog
      v-model:visible="permissionDialogVisible"
      :role-data="currentPermissionRole"
      @success="handlePermissionSuccess"
    />

    <!-- 复制角色对话框 -->
    <CopyRoleDialog
      v-model:visible="copyDialogVisible"
      :role-data="currentCopyRole"
      @success="handleCopySuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Delete,
  UserFilled,
  Check,
  Close,
  Key,
  ArrowDown,
  Avatar,
  Star,
  User
} from '@element-plus/icons-vue'
import { useSystemStore } from '@/store/modules/system'
import type { Role } from '@/types/system'
import { RoleStatus, DataScope } from '@/types/system'
import RoleFormDialog from './components/RoleFormDialog.vue'
import PermissionDialog from './components/PermissionDialog.vue'
import CopyRoleDialog from './components/CopyRoleDialog.vue'
import { formatDate } from '@/utils'
import { getRoleListApi } from '@/api/system/role'

defineOptions({
  name: 'RoleManagement'
})

// Store
const systemStore = useSystemStore()

// 响应式数据
const searchForm = reactive({
  name: '',
  code: '',
  level: undefined as number | undefined,
  status: undefined as RoleStatus | undefined
})

const selectedRoleIds = ref<number[]>([])
const formDialogVisible = ref(false)
const permissionDialogVisible = ref(false)
const copyDialogVisible = ref(false)
const currentEditRole = ref<Role | null>(null)
const currentPermissionRole = ref<Role | null>(null)
const currentCopyRole = ref<Role | null>(null)
const isEditMode = ref(false)

// 方法
const handleSearch = async () => {
  await systemStore.fetchRoles({
    page: 1,
    ...searchForm
  })
}

const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    code: '',
    level: undefined,
    status: undefined
  })
  systemStore.resetRoleQuery()
  handleRefresh()
}

const handleRefresh = async () => {
  await Promise.all([systemStore.fetchRoles(), systemStore.fetchPermissions()])
}

const handleAdd = () => {
  currentEditRole.value = null
  isEditMode.value = false
  formDialogVisible.value = true
}

const handleEdit = (role: Role) => {
  currentEditRole.value = role
  isEditMode.value = true
  formDialogVisible.value = true
}

const handlePermission = (role: Role) => {
  currentPermissionRole.value = role
  permissionDialogVisible.value = true
}

const handleDelete = async (role: Role) => {
  if (role.code === 'SUPER_ADMIN') {
    ElMessage.warning('不能删除超级管理员角色')
    return
  }

  try {
    await ElMessageBox.confirm(`确定要删除角色 "${role.name}" 吗？`, '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await systemStore.deleteRole(role.id)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除角色失败:', error)
    }
  }
}

const handleBatchDelete = async () => {
  if (selectedRoleIds.value.length === 0) {
    ElMessage.warning('请选择要删除的角色')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRoleIds.value.length} 个角色吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await systemStore.batchDeleteRoles(selectedRoleIds.value)
    selectedRoleIds.value = []
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除角色失败:', error)
    }
  }
}

const handleAction = async (command: string, role: Role) => {
  switch (command) {
    case 'copy':
      await handleCopy(role)
      break
    case 'toggleStatus':
      await handleToggleStatus(role)
      break
    case 'delete':
      await handleDelete(role)
      break
  }
}

const handleCopy = (role: Role) => {
  currentCopyRole.value = role
  copyDialogVisible.value = true
}

const handleToggleStatus = async (role: Role) => {
  const newStatus = role.status === RoleStatus.ENABLE ? RoleStatus.DISABLE : RoleStatus.ENABLE

  const action = newStatus === RoleStatus.ENABLE ? '启用' : '禁用'

  try {
    await ElMessageBox.confirm(`确定要${action}角色 "${role.name}" 吗？`, `${action}确认`, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await systemStore.updateRoleStatus(role.id, newStatus)
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action}角色失败:`, error)
    }
  }
}

const handleSelectionChange = (selection: Role[]) => {
  selectedRoleIds.value = selection.map(role => role.id)
}

const handleSizeChange = async (size: number) => {
  await systemStore.fetchRoles({ pageSize: size, page: 1 })
}

const handleCurrentChange = async (page: number) => {
  await systemStore.fetchRoles({ page })
}

const handleFormSuccess = async () => {
  formDialogVisible.value = false
  await handleRefresh()
}

const handlePermissionSuccess = async () => {
  permissionDialogVisible.value = false
  await handleRefresh()
}

const handleCopySuccess = async () => {
  copyDialogVisible.value = false
  await handleRefresh()
}

// 辅助方法
const getStatusTagType = (status: RoleStatus) => {
  switch (status) {
    case RoleStatus.ENABLE:
      return 'success'
    case RoleStatus.DISABLE:
      return 'danger'
    default:
      return 'info'
  }
}

const getStatusText = (status: RoleStatus) => {
  switch (status) {
    case RoleStatus.ENABLE:
      return '启用'
    case RoleStatus.DISABLE:
      return '禁用'
    default:
      return '未知'
  }
}

const getRoleLevelTagType = (level: number) => {
  switch (level) {
    case 1:
      return 'danger' // 超级管理员
    case 2:
      return 'warning' // 管理员
    case 3:
      return 'info' // 普通用户
    default:
      return 'info'
  }
}

const getRoleLevelText = (level: number) => {
  switch (level) {
    case 1:
      return '超级管理员'
    case 2:
      return '管理员'
    case 3:
      return '普通用户'
    default:
      return '未知'
  }
}

const getRoleIcon = (level: number) => {
  switch (level) {
    case 1:
      return Star // 超级管理员
    case 2:
      return Avatar // 管理员
    case 3:
      return User // 普通用户
    default:
      return User
  }
}

const getDataScopeText = (dataScope: DataScope) => {
  switch (dataScope) {
    case DataScope.ALL:
      return '全部数据'
    case DataScope.DEPT:
      return '本部门'
    case DataScope.DEPT_AND_SUB:
      return '本部门及下级'
    case DataScope.SELF:
      return '仅本人'
    default:
      return '未知'
  }
}

// 生命周期
onMounted(async () => {
  await Promise.all([
    systemStore.fetchRoles(),
    systemStore.fetchPermissions(),
    systemStore.fetchMenus()
  ])

  getRoleListApi({
    clientId: '764547797eb2e192763f5334028d49c9',
    pageNum: 1,
    pageSize: 10
  })
})
</script>

<style lang="scss" scoped>
.role-management {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: $spacing-md;

  .search-card {
    .el-form {
      margin-bottom: 0;
    }
  }

  .toolbar-card {
    .toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .toolbar-left {
        display: flex;
        gap: $spacing-sm;
      }
    }
  }

  .stats-cards {
    .stat-card {
      .stat-content {
        display: flex;
        align-items: center;
        gap: $spacing-md;

        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          color: white;

          &.total {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }

          &.active {
            background: linear-gradient(135deg, #5cb85c 0%, #449d44 100%);
          }

          &.disabled {
            background: linear-gradient(135deg, #d9534f 0%, #c9302c 100%);
          }

          &.permissions {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
          }
        }

        .stat-info {
          flex: 1;

          .stat-number {
            font-size: 24px;
            font-weight: 600;
            color: $text-primary;
            line-height: 1.2;
          }

          .stat-label {
            font-size: 14px;
            color: $text-secondary;
            margin-top: 4px;
          }
        }
      }
    }
  }

  .table-card {
    flex: 1;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .role-info {
      display: flex;
      align-items: center;
      gap: $spacing-sm;

      .role-icon {
        color: $primary-color;
        flex-shrink: 0;
      }

      .role-details {
        .role-name {
          font-weight: 600;
          color: $text-primary;
        }

        .role-code {
          font-size: 12px;
          color: $text-secondary;
          margin-top: 2px;
          font-family: monospace;
        }
      }
    }

    .pagination-wrapper {
      margin-top: $spacing-md;
      display: flex;
      justify-content: center;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .role-management {
    .toolbar {
      flex-direction: column;
      gap: $spacing-sm;
      align-items: stretch !important;

      .toolbar-left,
      .toolbar-right {
        justify-content: center;
      }
    }

    .stats-cards {
      .el-col {
        margin-bottom: $spacing-sm;
      }
    }
  }
}
</style>
