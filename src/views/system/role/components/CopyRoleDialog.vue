<template>
  <el-dialog v-model="dialogVisible" title="复制角色" width="500px" @close="handleClose">
    <div class="copy-role-content">
      <!-- 源角色信息 -->
      <el-alert
        :title="'正在复制角色 ' + (roleData?.name || '')"
        type="info"
        :closable="false"
        show-icon
        class="source-alert"
      />

      <!-- 新角色信息表单 -->
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
        <el-form-item label="新角色名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入新角色名称" />
        </el-form-item>

        <el-form-item label="新角色编码" prop="code">
          <el-input v-model="formData.code" placeholder="请输入新角色编码" />
        </el-form-item>

        <!-- 复制选项 -->
        <el-form-item label="复制内容">
          <el-checkbox-group v-model="copyOptions">
            <el-checkbox label="permissions" checked disabled>
              权限信息 ({{ roleData?.permissions?.length || 0 }} 个权限)
            </el-checkbox>
            <el-checkbox label="dataScope"> 数据权限范围 </el-checkbox>
            <el-checkbox label="description"> 角色描述 </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <!-- 预览信息 -->
        <el-form-item label="复制预览">
          <div class="preview-info">
            <div class="preview-item">
              <span class="label">源角色级别:</span>
              <el-tag :type="getRoleLevelTagType(roleData?.level || 3)">
                {{ getRoleLevelText(roleData?.level || 3) }}
              </el-tag>
            </div>
            <div class="preview-item">
              <span class="label">数据权限:</span>
              <el-tag size="small">{{ getDataScopeText(roleData?.dataScope) }}</el-tag>
            </div>
            <div class="preview-item">
              <span class="label">权限数量:</span>
              <el-text type="primary">{{ roleData?.permissions?.length || 0 }} 个</el-text>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          确认复制
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { useSystemStore } from '@/store/modules/system'
import type { Role } from '@/types/system'
import { DataScope } from '@/types/system'

interface Props {
  visible: boolean
  roleData?: Role | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  roleData: null
})

const emit = defineEmits<Emits>()

// Store
const systemStore = useSystemStore()

// 响应式数据
const formRef = ref<FormInstance>()
const submitLoading = ref(false)

const formData = reactive({
  name: '',
  code: ''
})

const copyOptions = ref(['permissions', 'dataScope', 'description'])

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value)
})

// 表单验证规则
const formRules = computed<FormRules>(() => ({
  name: [
    { required: true, message: '请输入新角色名称', trigger: 'blur' },
    { min: 2, max: 50, message: '角色名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入新角色编码', trigger: 'blur' },
    { min: 2, max: 50, message: '角色编码长度在 2 到 50 个字符', trigger: 'blur' },
    { pattern: /^[A-Z0-9_]+$/, message: '角色编码只能包含大写字母、数字和下划线', trigger: 'blur' }
  ]
}))

// 方法
const initFormData = () => {
  if (props.roleData) {
    formData.name = `${props.roleData.name}_副本`
    formData.code = `${props.roleData.code}_COPY`
  }
}

const handleSubmit = async () => {
  if (!formRef.value || !props.roleData) return

  try {
    await formRef.value.validate()

    submitLoading.value = true

    await systemStore.copyRole(props.roleData.id, formData.name, formData.code)

    ElMessage.success('角色复制成功')
    emit('success')
  } catch (error) {
    console.error('复制角色失败:', error)
  } finally {
    submitLoading.value = false
  }
}

const handleClose = () => {
  formRef.value?.resetFields()
  copyOptions.value = ['permissions', 'dataScope', 'description']
  emit('update:visible', false)
}

// 辅助方法
const getRoleLevelTagType = (level: number) => {
  switch (level) {
    case 1:
      return 'danger' // 超级管理员
    case 2:
      return 'warning' // 管理员
    case 3:
      return 'info' // 普通用户
    default:
      return 'info'
  }
}

const getRoleLevelText = (level: number) => {
  switch (level) {
    case 1:
      return '超级管理员'
    case 2:
      return '管理员'
    case 3:
      return '普通用户'
    default:
      return '未知'
  }
}

const getDataScopeText = (dataScope?: DataScope) => {
  switch (dataScope) {
    case DataScope.ALL:
      return '全部数据'
    case DataScope.DEPT:
      return '本部门'
    case DataScope.DEPT_AND_SUB:
      return '本部门及下级'
    case DataScope.SELF:
      return '仅本人'
    default:
      return '未知'
  }
}

// 监听对话框显示状态
watch(
  () => props.visible,
  newVal => {
    if (newVal) {
      nextTick(() => {
        initFormData()
      })
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.copy-role-content {
  .source-alert {
    margin-bottom: $spacing-md;
  }

  .preview-info {
    .preview-item {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
      margin-bottom: $spacing-sm;

      .label {
        font-weight: 500;
        color: $text-secondary;
        min-width: 80px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: $spacing-sm;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-checkbox-group) {
  .el-checkbox {
    display: block;
    margin-bottom: $spacing-sm;
    margin-right: 0;
  }
}
</style>
