<template>
  <el-dialog v-model="dialogVisible" title="权限配置" width="800px" @close="handleClose">
    <div class="permission-config">
      <!-- 角色信息 -->
      <el-alert
        :title="`正在为角色 「${roleData?.name}」 配置权限`"
        type="info"
        :closable="false"
        show-icon
        class="role-alert"
      />

      <!-- 权限选择 -->
      <div class="permission-tabs">
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 菜单权限 -->
          <el-tab-pane label="菜单权限" name="menu">
            <div class="tab-content">
              <div class="tab-header">
                <el-button size="small" @click="handleExpandAll(true)"> 展开全部 </el-button>
                <el-button size="small" @click="handleExpandAll(false)"> 收起全部 </el-button>
                <el-button size="small" @click="handleSelectAll(true)"> 全选 </el-button>
                <el-button size="small" @click="handleSelectAll(false)"> 全不选 </el-button>
              </div>

              <el-tree
                ref="menuTreeRef"
                :data="systemStore.menuTree"
                :props="menuTreeProps"
                show-checkbox
                default-expand-all
                node-key="id"
                :default-checked-keys="checkedMenus"
                @check="handleMenuCheck"
                class="permission-tree"
              >
                <template #default="{ node, data }">
                  <div class="tree-node">
                    <el-icon v-if="data.icon" class="node-icon">
                      <component :is="data.icon" />
                    </el-icon>
                    <span class="node-label">{{ data.name }}</span>
                    <el-tag
                      v-if="data.type"
                      size="small"
                      :type="getPermissionTypeTag(data.type)"
                      class="node-tag"
                    >
                      {{ getPermissionTypeText(data.type) }}
                    </el-tag>
                  </div>
                </template>
              </el-tree>
            </div>
          </el-tab-pane>

          <!-- 按钮权限 -->
          <el-tab-pane label="按钮权限" name="button">
            <div class="tab-content">
              <div class="tab-header">
                <el-button size="small" @click="handleSelectAll(true)"> 全选 </el-button>
                <el-button size="small" @click="handleSelectAll(false)"> 全不选 </el-button>
                <el-button size="small" @click="handleSmartSelect" type="primary">
                  智能选择
                </el-button>
              </div>
              <div class="permission-groups">
                <div
                  v-for="group in buttonPermissionGroups"
                  :key="group.name"
                  class="permission-group"
                >
                  <div class="group-header">
                    <el-checkbox
                      :model-value="isGroupAllChecked(group.permissions)"
                      :indeterminate="isGroupIndeterminate(group.permissions)"
                      @change="handleGroupCheck(group.permissions, $event)"
                    >
                      {{ group.name }}
                    </el-checkbox>
                  </div>
                  <div class="group-content">
                    <el-checkbox-group v-model="checkedButtons">
                      <el-checkbox
                        v-for="permission in group.permissions"
                        :key="permission.id"
                        :label="permission.id"
                        class="permission-checkbox"
                      >
                        {{ permission.name }}
                      </el-checkbox>
                    </el-checkbox-group>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- API权限 -->
          <el-tab-pane label="API权限" name="api">
            <div class="tab-content">
              <div class="tab-header">
                <el-button size="small" @click="handleSelectAll(true)"> 全选 </el-button>
                <el-button size="small" @click="handleSelectAll(false)"> 全不选 </el-button>
                <el-button size="small" @click="handleApiQuickSelect" type="primary">
                  快速选择
                </el-button>
              </div>
              <div class="api-permissions">
                <el-table
                  :data="apiPermissions"
                  @select="handleApiSelect"
                  @select-all="handleApiSelectAll"
                  ref="apiTableRef"
                >
                  <el-table-column type="selection" width="55" />
                  <el-table-column prop="name" label="权限名称" />
                  <el-table-column prop="code" label="权限编码" />
                  <el-table-column prop="path" label="API路径" />
                  <el-table-column prop="method" label="请求方法" width="100">
                    <template #default="{ row }">
                      <el-tag :type="getMethodTagType(row.method)" size="small">
                        {{ row.method }}
                      </el-tag>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          保存权限配置
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { useSystemStore } from '@/store/modules/system'
import type { Role, Permission, Menu } from '@/types/system'
import { PermissionType } from '@/types/system'

interface Props {
  visible: boolean
  roleData?: Role | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  roleData: null
})

const emit = defineEmits<Emits>()

// Store
const systemStore = useSystemStore()

// 响应式数据
const menuTreeRef = ref()
const apiTableRef = ref()
const submitLoading = ref(false)
const activeTab = ref('menu')

const checkedMenus = ref<number[]>([])
const checkedButtons = ref<number[]>([])
const checkedApis = ref<number[]>([])

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value)
})

// 菜单树配置
const menuTreeProps = {
  children: 'children',
  label: 'name',
  value: 'id'
}

// 按钮权限分组
const buttonPermissionGroups = computed(() => {
  const buttonPerms = systemStore.permissions.filter(p => p.type === PermissionType.BUTTON)
  const groups: Record<string, Permission[]> = {}

  buttonPerms.forEach(perm => {
    const parentPerm = systemStore.permissions.find(p => p.id === perm.parentId)
    const groupName = parentPerm?.name || '其他'

    if (!groups[groupName]) {
      groups[groupName] = []
    }
    groups[groupName].push(perm)
  })

  return Object.entries(groups).map(([name, permissions]) => ({
    name,
    permissions
  }))
})

// API权限
const apiPermissions = computed(() => {
  return systemStore.permissions.filter(p => p.type === PermissionType.API)
})

// 方法
const initPermissions = () => {
  if (props.roleData) {
    const rolePermissions = props.roleData.permissions || []

    checkedMenus.value = rolePermissions.filter(p => p.type === PermissionType.MENU).map(p => p.id)

    checkedButtons.value = rolePermissions
      .filter(p => p.type === PermissionType.BUTTON)
      .map(p => p.id)

    checkedApis.value = rolePermissions.filter(p => p.type === PermissionType.API).map(p => p.id)

    // 设置API表格选中状态
    nextTick(() => {
      if (apiTableRef.value) {
        apiTableRef.value.clearSelection()
        apiPermissions.value.forEach(perm => {
          if (checkedApis.value.includes(perm.id)) {
            apiTableRef.value.toggleRowSelection(perm, true)
          }
        })
      }
    })
  }
}

const handleExpandAll = (expand: boolean) => {
  const tree = menuTreeRef.value
  if (tree) {
    const nodes = tree.store._getAllNodes()
    nodes.forEach((node: any) => {
      node.expanded = expand
    })
  }
}

const handleSelectAll = (select: boolean) => {
  if (activeTab.value === 'menu') {
    if (select) {
      // 获取所有菜单节点ID（包括子节点）
      const getAllMenuIds = (menus: Menu[]): number[] => {
        let ids: number[] = []
        menus.forEach(menu => {
          ids.push(menu.id)
          if (menu.children && menu.children.length > 0) {
            ids = ids.concat(getAllMenuIds(menu.children))
          }
        })
        return ids
      }

      const allMenuIds = getAllMenuIds(systemStore.menuTree)
      checkedMenus.value = allMenuIds
      menuTreeRef.value?.setCheckedKeys(allMenuIds)

      // 联动选中相关的按钮权限
      handleMenuButtonLinkage(allMenuIds, true)
    } else {
      checkedMenus.value = []
      menuTreeRef.value?.setCheckedKeys([])

      // 联动取消相关的按钮权限
      handleMenuButtonLinkage([], false)
    }
  } else if (activeTab.value === 'button') {
    // 按钮权限全选/全不选
    if (select) {
      checkedButtons.value = systemStore.permissions
        .filter(p => p.type === PermissionType.BUTTON)
        .map(p => p.id)
    } else {
      checkedButtons.value = []
    }
  } else if (activeTab.value === 'api') {
    // API权限全选/全不选
    if (select) {
      checkedApis.value = apiPermissions.value.map(p => p.id)
      // 同步表格选中状态
      nextTick(() => {
        apiTableRef.value?.toggleAllSelection()
      })
    } else {
      checkedApis.value = []
      nextTick(() => {
        apiTableRef.value?.clearSelection()
      })
    }
  }
}

const handleMenuCheck = (data: Menu, checked: any) => {
  checkedMenus.value = checked.checkedKeys

  // 菜单权限与按钮权限联动
  const currentCheckedMenus = checked.checkedKeys || []
  handleMenuButtonLinkage(currentCheckedMenus, true)
}

// 菜单权限与按钮权限联动
const handleMenuButtonLinkage = (checkedMenuIds: number[], isChecking: boolean) => {
  const buttonPerms = systemStore.permissions.filter(p => p.type === PermissionType.BUTTON)

  checkedMenuIds.forEach(menuId => {
    // 找到该菜单相关的按钮权限
    const relatedButtons = buttonPerms.filter(btn => btn.parentId === menuId)

    relatedButtons.forEach(btn => {
      if (isChecking) {
        // 选中菜单时，自动选中相关按钮
        if (!checkedButtons.value.includes(btn.id)) {
          checkedButtons.value.push(btn.id)
        }
      } else {
        // 取消菜单时，检查是否还有其他相关菜单被选中
        const hasOtherRelatedMenu = checkedMenuIds.some(
          id =>
            id !== menuId &&
            systemStore.permissions.some(
              p => p.id === btn.parentId && checkedMenuIds.includes(p.id)
            )
        )

        if (!hasOtherRelatedMenu) {
          const index = checkedButtons.value.indexOf(btn.id)
          if (index > -1) {
            checkedButtons.value.splice(index, 1)
          }
        }
      }
    })
  })
}

const isGroupAllChecked = (permissions: Permission[]) => {
  return permissions.every(p => checkedButtons.value.includes(p.id))
}

const isGroupIndeterminate = (permissions: Permission[]) => {
  const checkedCount = permissions.filter(p => checkedButtons.value.includes(p.id)).length
  return checkedCount > 0 && checkedCount < permissions.length
}

const handleGroupCheck = (permissions: Permission[], checked: boolean) => {
  if (checked) {
    permissions.forEach(p => {
      if (!checkedButtons.value.includes(p.id)) {
        checkedButtons.value.push(p.id)
      }
    })
  } else {
    permissions.forEach(p => {
      const index = checkedButtons.value.indexOf(p.id)
      if (index > -1) {
        checkedButtons.value.splice(index, 1)
      }
    })
  }

  // 按钮权限变化时检查菜单权限联动
  handleButtonMenuLinkage()
}

// 智能选择按钮权限（根据已选中的菜单权限）
const handleSmartSelect = () => {
  const buttonPerms = systemStore.permissions.filter(p => p.type === PermissionType.BUTTON)
  const smartSelectedButtons: number[] = []

  checkedMenus.value.forEach(menuId => {
    const relatedButtons = buttonPerms.filter(btn => btn.parentId === menuId)
    relatedButtons.forEach(btn => {
      if (!smartSelectedButtons.includes(btn.id)) {
        smartSelectedButtons.push(btn.id)
      }
    })
  })

  checkedButtons.value = smartSelectedButtons
  ElMessage.success(`已智能选择 ${smartSelectedButtons.length} 个相关按钮权限`)
}

// 按钮权限变化时影响菜单权限（反向联动）
const handleButtonMenuLinkage = () => {
  // 如果某个菜单的所有按钮都被取消选中，可以考虑提示用户
  // 这里暂时不自动取消菜单权限，因为菜单权限比按钮权限更基础
}

const handleApiSelect = (selection: Permission[], row: Permission) => {
  const isSelected = selection.includes(row)
  if (isSelected) {
    if (!checkedApis.value.includes(row.id)) {
      checkedApis.value.push(row.id)
    }
  } else {
    const index = checkedApis.value.indexOf(row.id)
    if (index > -1) {
      checkedApis.value.splice(index, 1)
    }
  }
}

const handleApiSelectAll = (selection: Permission[]) => {
  if (selection.length === 0) {
    checkedApis.value = []
  } else {
    checkedApis.value = apiPermissions.value.map(p => p.id)
  }
}

// API权限快速选择
const handleApiQuickSelect = () => {
  // 只选择GET和POST方法的API权限（常用的查询和新增操作）
  const quickSelectApis = apiPermissions.value.filter(api => {
    const method = api.method?.toUpperCase()
    return method === 'GET' || method === 'POST'
  })

  checkedApis.value = quickSelectApis.map(api => api.id)

  // 同步表格选中状态
  nextTick(() => {
    if (apiTableRef.value) {
      apiTableRef.value.clearSelection()
      quickSelectApis.forEach(api => {
        apiTableRef.value.toggleRowSelection(api, true)
      })
    }
  })

  ElMessage.success(`已快速选择 ${quickSelectApis.length} 个常用API权限（GET/POST）`)
}

const handleSubmit = async () => {
  if (!props.roleData) return

  try {
    submitLoading.value = true

    const allPermissionIds = [...checkedMenus.value, ...checkedButtons.value, ...checkedApis.value]

    await systemStore.assignPermissions(props.roleData.id, allPermissionIds)

    ElMessage.success('权限配置保存成功')
    emit('success')
  } catch (error) {
    console.error('保存权限配置失败:', error)
  } finally {
    submitLoading.value = false
  }
}

const handleClose = () => {
  emit('update:visible', false)
}

// 辅助方法
const getPermissionTypeTag = (type: PermissionType) => {
  switch (type) {
    case PermissionType.MENU:
      return 'primary'
    case PermissionType.BUTTON:
      return 'success'
    case PermissionType.API:
      return 'warning'
    default:
      return 'info'
  }
}

const getPermissionTypeText = (type: PermissionType) => {
  switch (type) {
    case PermissionType.MENU:
      return '菜单'
    case PermissionType.BUTTON:
      return '按钮'
    case PermissionType.API:
      return 'API'
    default:
      return '未知'
  }
}

const getMethodTagType = (method: string) => {
  switch (method?.toUpperCase()) {
    case 'GET':
      return 'success'
    case 'POST':
      return 'primary'
    case 'PUT':
      return 'warning'
    case 'DELETE':
      return 'danger'
    default:
      return 'info'
  }
}

// 监听对话框显示状态
watch(
  () => props.visible,
  newVal => {
    if (newVal) {
      // 确保权限数据已加载
      Promise.all([systemStore.fetchPermissions(), systemStore.fetchMenuTree()]).then(() => {
        initPermissions()
      })
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.permission-config {
  .role-alert {
    margin-bottom: $spacing-md;
  }

  .permission-tabs {
    .tab-content {
      min-height: 400px;
      max-height: 500px;
      overflow-y: auto;

      .tab-header {
        margin-bottom: $spacing-md;
        padding-bottom: $spacing-sm;
        border-bottom: 1px solid $border-color;

        .el-button {
          margin-right: $spacing-sm;
        }
      }
    }

    .permission-tree {
      .tree-node {
        display: flex;
        align-items: center;
        gap: $spacing-sm;
        width: 100%;

        .node-icon {
          color: $primary-color;
        }

        .node-label {
          flex: 1;
        }

        .node-tag {
          margin-left: auto;
        }
      }
    }

    .permission-groups {
      .permission-group {
        margin-bottom: $spacing-md;
        border: 1px solid $border-color;
        border-radius: $border-radius;

        .group-header {
          padding: $spacing-sm $spacing-md;
          background-color: $content-bg;
          border-bottom: 1px solid $border-color;
          font-weight: 500;
        }

        .group-content {
          padding: $spacing-md;

          .permission-checkbox {
            display: block;
            margin-bottom: $spacing-sm;
            margin-right: 0;
          }
        }
      }
    }

    .api-permissions {
      .el-table {
        .el-tag {
          font-weight: 500;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: $spacing-sm;
}

// 覆盖Element Plus样式
:deep(.el-tabs__content) {
  padding: $spacing-md;
}

:deep(.el-tree) {
  .el-tree-node__content {
    height: 36px;
  }
}
</style>
