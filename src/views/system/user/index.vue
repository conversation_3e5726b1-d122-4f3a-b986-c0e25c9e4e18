<template>
  <div class="user-management">
    <!-- 搜索表单 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" label-width="80px" :inline="true">
        <el-form-item label="用户名">
          <el-input
            v-model="searchForm.username"
            placeholder="请输入用户名"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="显示名称">
          <el-input
            v-model="searchForm.nickname"
            placeholder="请输入显示名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input
            v-model="searchForm.email"
            placeholder="请输入邮箱"
            clearable
            style="width: 220px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="正常" :value="UserStatus.ACTIVE" />
            <el-option label="禁用" :value="UserStatus.DISABLED" />
            <el-option label="锁定" :value="UserStatus.LOCKED" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作工具栏 -->
    <el-card class="toolbar-card" shadow="never">
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增用户
          </el-button>
          <el-button
            type="danger"
            :disabled="selectedUserIds.length === 0"
            @click="handleBatchDelete"
          >
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-tooltip content="刷新">
            <el-button circle @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </el-card>

    <!-- 用户统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><UserFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ systemStore.userStats.total || 0 }}</div>
                <div class="stat-label">总用户数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon active">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ systemStore.userStats.active || 0 }}</div>
                <div class="stat-label">正常用户</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon disabled">
                <el-icon><Close /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ systemStore.userStats.disabled || 0 }}</div>
                <div class="stat-label">禁用用户</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon locked">
                <el-icon><Lock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ systemStore.userStats.locked || 0 }}</div>
                <div class="stat-label">锁定用户</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 用户表格 -->
    <el-card class="table-card" shadow="never">
      <el-table
        v-loading="systemStore.userLoading"
        :data="systemStore.users"
        max-height="500"
        @selection-change="handleSelectionChange"
        stripe
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="id" label="ID" width="80" />

        <el-table-column label="用户信息" min-width="200">
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar :size="40" :src="row.avatar" class="user-avatar">
                {{ row.nickname.charAt(0) }}
              </el-avatar>
              <div class="user-details">
                <div class="username">{{ row.username }}</div>
                <div class="nickname">{{ row.nickname }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="email" label="邮箱" min-width="180" />
        <el-table-column prop="phone" label="手机号" width="120" />

        <el-table-column label="角色" min-width="150">
          <template #default="{ row }">
            <el-tag
              v-for="role in row.roles"
              :key="role.id"
              :type="getRoleTagType(role.level)"
              size="small"
              class="role-tag"
            >
              {{ role.name }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="最后登录" width="160">
          <template #default="{ row }">
            <span v-if="row.lastLoginTime">
              {{ formatDate(row.lastLoginTime) }}
            </span>
            <span v-else class="text-placeholder">从未登录</span>
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="220" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleEdit(row)"> 编辑 </el-button>
            <el-dropdown @command="(command: string) => handleAction(command, row)">
              <el-button class="ml-12" link>
                更多<el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="resetPassword">重置密码</el-dropdown-item>
                  <el-dropdown-item command="toggleStatus">
                    {{ row.status === UserStatus.ACTIVE ? '禁用' : '启用' }}
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" :disabled="row.username === 'admin'" divided>
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="systemStore.userQueryParams.page"
          v-model:page-size="systemStore.userQueryParams.pageSize"
          :total="systemStore.userTotal"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 用户表单对话框 -->
    <UserFormDialog
      v-model:visible="formDialogVisible"
      :user-data="currentEditUser"
      :is-edit="isEditMode"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Delete,
  UserFilled,
  Check,
  Close,
  Lock,
  ArrowDown
} from '@element-plus/icons-vue'
import { useSystemStore } from '@/store/modules/system'
import type { User } from '@/types/system'
import { UserStatus } from '@/types/system'
import UserFormDialog from './components/UserFormDialog.vue'
import { formatDate } from '@/utils'

defineOptions({
  name: 'UserManagement'
})

// Store
const systemStore = useSystemStore()

// 响应式数据
const searchForm = reactive({
  username: '',
  nickname: '',
  email: '',
  status: undefined as UserStatus | undefined
})

const selectedUserIds = ref<number[]>([])
const formDialogVisible = ref(false)
const currentEditUser = ref<User | null>(null)
const isEditMode = ref(false)

// 计算属性（这里可以添加需要的计算属性）

// 方法
const handleSearch = async () => {
  await systemStore.fetchUsers({
    page: 1,
    ...searchForm
  })
}

const handleReset = () => {
  Object.assign(searchForm, {
    username: '',
    nickname: '',
    email: '',
    status: undefined
  })
  systemStore.resetUserQuery()
  handleRefresh()
}

const handleRefresh = async () => {
  await systemStore.fetchUsers()
  await systemStore.fetchUserStats()
}

const handleAdd = () => {
  currentEditUser.value = null
  isEditMode.value = false
  formDialogVisible.value = true
}

const handleEdit = (user: User) => {
  currentEditUser.value = user
  isEditMode.value = true
  formDialogVisible.value = true
}

const handleDelete = async (user: User) => {
  if (user.username === 'admin') {
    ElMessage.warning('不能删除超级管理员账号')
    return
  }

  try {
    await ElMessageBox.confirm(`确定要删除用户 "${user.nickname}" 吗？`, '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await systemStore.deleteUser(user.id)
    await systemStore.fetchUserStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
    }
  }
}

const handleBatchDelete = async () => {
  if (selectedUserIds.value.length === 0) {
    ElMessage.warning('请选择要删除的用户')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedUserIds.value.length} 个用户吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await systemStore.batchDeleteUsers(selectedUserIds.value)
    selectedUserIds.value = []
    await systemStore.fetchUserStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除用户失败:', error)
    }
  }
}

const handleAction = async (command: string, user: User) => {
  switch (command) {
    case 'resetPassword':
      await handleResetPassword(user)
      break
    case 'toggleStatus':
      await handleToggleStatus(user)
      break
    case 'delete':
      await handleDelete(user)
      break
  }
}

const handleResetPassword = async (user: User) => {
  try {
    await ElMessageBox.confirm(`确定要重置用户 "${user.nickname}" 的密码吗？`, '重置密码确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await systemStore.resetUserPassword(user.id)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重置密码失败:', error)
    }
  }
}

const handleToggleStatus = async (user: User) => {
  const newStatus = user.status === UserStatus.ACTIVE ? UserStatus.DISABLED : UserStatus.ACTIVE

  const action = newStatus === UserStatus.ACTIVE ? '启用' : '禁用'

  try {
    await ElMessageBox.confirm(`确定要${action}用户 "${user.nickname}" 吗？`, `${action}确认`, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await systemStore.updateUserStatus(user.id, newStatus)
    await systemStore.fetchUserStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action}用户失败:`, error)
    }
  }
}

const handleSelectionChange = (selection: User[]) => {
  selectedUserIds.value = selection.map(user => user.id)
}

const handleSizeChange = async (size: number) => {
  await systemStore.fetchUsers({ pageSize: size, page: 1 })
}

const handleCurrentChange = async (page: number) => {
  await systemStore.fetchUsers({ page })
}

const handleFormSuccess = async () => {
  formDialogVisible.value = false
  await handleRefresh()
}

// 辅助方法
const getStatusTagType = (status: UserStatus) => {
  switch (status) {
    case UserStatus.ACTIVE:
      return 'success'
    case UserStatus.DISABLED:
      return 'danger'
    case UserStatus.LOCKED:
      return 'warning'
    default:
      return 'info'
  }
}

const getStatusText = (status: UserStatus) => {
  switch (status) {
    case UserStatus.ACTIVE:
      return '正常'
    case UserStatus.DISABLED:
      return '禁用'
    case UserStatus.LOCKED:
      return '锁定'
    default:
      return '未知'
  }
}

const getRoleTagType = (level: number) => {
  switch (level) {
    case 1:
      return 'danger' // 超级管理员
    case 2:
      return 'warning' // 管理员
    case 3:
      return 'info' // 普通用户
    default:
      return 'info'
  }
}

// 生命周期
onMounted(async () => {
  await Promise.all([
    systemStore.fetchUsers(),
    systemStore.fetchUserStats(),
    systemStore.fetchAllRoles() // 为表单对话框准备角色数据
  ])
})
</script>

<style lang="scss" scoped>
.user-management {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: $spacing-md;

  .search-card {
    .el-form {
      margin-bottom: 0;
    }
  }

  .toolbar-card {
    .toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .toolbar-left {
        display: flex;
        gap: $spacing-sm;
      }
    }
  }

  .stats-cards {
    .stat-card {
      .stat-content {
        display: flex;
        align-items: center;
        gap: $spacing-md;

        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          color: white;

          &.total {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }

          &.active {
            background: linear-gradient(135deg, #5cb85c 0%, #449d44 100%);
          }

          &.disabled {
            background: linear-gradient(135deg, #d9534f 0%, #c9302c 100%);
          }

          &.locked {
            background: linear-gradient(135deg, #f0ad4e 0%, #ec971f 100%);
          }
        }

        .stat-info {
          flex: 1;

          .stat-number {
            font-size: 24px;
            font-weight: 600;
            color: $text-primary;
            line-height: 1.2;
          }

          .stat-label {
            font-size: 14px;
            color: $text-secondary;
            margin-top: 4px;
          }
        }
      }
    }
  }

  .table-card {
    flex: 1;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: $spacing-sm;

      .user-avatar {
        flex-shrink: 0;
      }

      .user-details {
        .username {
          font-weight: 600;
          color: $text-primary;
        }

        .nickname {
          font-size: 12px;
          color: $text-secondary;
          margin-top: 2px;
        }
      }
    }

    .role-tag {
      margin-right: 4px;
      margin-bottom: 2px;
    }

    .text-placeholder {
      color: $text-secondary;
      font-style: italic;
    }

    .pagination-wrapper {
      margin-top: $spacing-md;
      display: flex;
      justify-content: center;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .user-management {
    .toolbar {
      flex-direction: column;
      gap: $spacing-sm;
      align-items: stretch !important;

      .toolbar-left,
      .toolbar-right {
        justify-content: center;
      }
    }

    .stats-cards {
      .el-col {
        margin-bottom: $spacing-sm;
      }
    }
  }
}
</style>
