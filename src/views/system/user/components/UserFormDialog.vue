<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑用户' : '新增用户'"
    width="600px"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户名" prop="username">
            <el-input v-model="formData.username" placeholder="请输入用户名" :disabled="isEdit" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="显示名称" prop="nickname">
            <el-input v-model="formData.nickname" placeholder="请输入显示名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="formData.email" placeholder="请输入邮箱" type="email" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号" prop="phone">
            <el-input v-model="formData.phone" placeholder="请输入手机号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="!isEdit">
        <el-col :span="12">
          <el-form-item label="密码" prop="password">
            <el-input
              v-model="formData.password"
              type="password"
              placeholder="请输入密码"
              show-password
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="formData.confirmPassword"
              type="password"
              placeholder="请确认密码"
              show-password
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="分配角色" prop="roleIds">
        <el-select v-model="formData.roleIds" multiple placeholder="请选择角色" style="width: 100%">
          <el-option
            v-for="role in systemStore.roles"
            :key="role.id"
            :label="role.name"
            :value="role.id"
            :disabled="role.status === RoleStatus.DISABLE"
          >
            <div class="role-option">
              <span>{{ role.name }}</span>
              <el-tag :type="getRoleTagType(role.level)" size="small" style="margin-left: 8px">
                {{ getRoleLevelText(role.level) }}
              </el-tag>
            </div>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="用户状态" prop="status" v-if="isEdit">
        <el-radio-group v-model="formData.status">
          <el-radio :label="UserStatus.ACTIVE">正常</el-radio>
          <el-radio :label="UserStatus.DISABLED">禁用</el-radio>
          <el-radio :label="UserStatus.LOCKED">锁定</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, nextTick } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { useSystemStore } from '@/store/modules/system'
import type { User, CreateUserRequest, UpdateUserRequest } from '@/types/system'
import { UserStatus, RoleStatus } from '@/types/system'

interface Props {
  visible: boolean
  userData?: User | null
  isEdit?: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  userData: null,
  isEdit: false
})

const emit = defineEmits<Emits>()

// Store
const systemStore = useSystemStore()

// 响应式数据
const formRef = ref<FormInstance>()
const submitLoading = ref(false)

const formData = reactive({
  username: '',
  nickname: '',
  email: '',
  phone: '',
  password: '',
  confirmPassword: '',
  roleIds: [] as number[],
  status: UserStatus.ACTIVE,
  remark: ''
})

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value)
})

// 表单验证规则
const formRules = computed<FormRules>(() => ({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  nickname: [
    { required: true, message: '请输入显示名称', trigger: 'blur' },
    { min: 2, max: 20, message: '显示名称长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }],
  password: !props.isEdit
    ? [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
      ]
    : [],
  confirmPassword: !props.isEdit
    ? [
        { required: true, message: '请确认密码', trigger: 'blur' },
        {
          validator: (rule: any, value: string, callback: any) => {
            if (value !== formData.password) {
              callback(new Error('两次输入的密码不一致'))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        }
      ]
    : [],
  roleIds: [{ required: true, message: '请选择至少一个角色', trigger: 'change' }]
}))

// 方法
const initFormData = () => {
  if (props.isEdit && props.userData) {
    Object.assign(formData, {
      username: props.userData.username,
      nickname: props.userData.nickname,
      email: props.userData.email,
      phone: props.userData.phone || '',
      password: '',
      confirmPassword: '',
      roleIds: props.userData.roles.map(role => role.id),
      status: props.userData.status,
      remark: props.userData.remark || ''
    })
  } else {
    Object.assign(formData, {
      username: '',
      nickname: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
      roleIds: [],
      status: UserStatus.ACTIVE,
      remark: ''
    })
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    submitLoading.value = true

    if (props.isEdit && props.userData) {
      // 编辑用户
      const updateData: UpdateUserRequest = {
        nickname: formData.nickname,
        email: formData.email,
        phone: formData.phone || undefined,
        status: formData.status,
        roleIds: formData.roleIds,
        remark: formData.remark || undefined
      }

      await systemStore.updateUser(props.userData.id, updateData)
    } else {
      // 新增用户
      const createData: CreateUserRequest = {
        username: formData.username,
        nickname: formData.nickname,
        email: formData.email,
        phone: formData.phone || undefined,
        password: formData.password,
        roleIds: formData.roleIds,
        remark: formData.remark || undefined
      }

      await systemStore.createUser(createData)
    }

    emit('success')
  } catch (error) {
    console.error('提交表单失败:', error)
  } finally {
    submitLoading.value = false
  }
}

const handleClose = () => {
  formRef.value?.resetFields()
  emit('update:visible', false)
}

const getRoleTagType = (level: number) => {
  switch (level) {
    case 1:
      return 'danger' // 超级管理员
    case 2:
      return 'warning' // 管理员
    case 3:
      return 'info' // 普通用户
    default:
      return 'info'
  }
}

const getRoleLevelText = (level: number) => {
  switch (level) {
    case 1:
      return '超级管理员'
    case 2:
      return '管理员'
    case 3:
      return '普通用户'
    default:
      return '未知'
  }
}

// 监听对话框显示状态
watch(
  () => props.visible,
  newVal => {
    if (newVal) {
      nextTick(() => {
        initFormData()
      })
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.role-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: $spacing-sm;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper) {
  &:focus-within {
    box-shadow: 0 0 0 1px var(--el-color-primary) inset;
  }
}

:deep(.el-textarea__inner) {
  &:focus {
    box-shadow: 0 0 0 1px var(--el-color-primary) inset;
  }
}
</style>
