<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h1 class="title">管理系统</h1>
      </div>

      <el-form :model="loginForm" @submit="handleSubmit" class="login-form" label-position="top">
        <el-form-item
          prop="username"
          label="用户名"
          :rules="[{ required: true, message: '请输入用户名', trigger: 'blur' }]"
        >
          <el-input v-model="loginForm.username" placeholder="请输入用户名" clearable>
            <template #prefix>
              <i class="ri-user-line"></i>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item
          prop="password"
          label="密码"
          :rules="[{ required: true, message: '请输入密码', trigger: 'blur' }]"
        >
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            clearable
            show-password
          >
            <template #prefix>
              <i class="ri-lock-line"></i>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item>
          <div class="remember-forgot">
            <el-checkbox v-model="loginForm.rememberMe">记住我</el-checkbox>
            <el-link>忘记密码？</el-link>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            native-type="submit"
            :loading="loading"
            size="large"
            style="width: 100%"
          >
            登录
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAppStore } from '@/store'

const router = useRouter()
const route = useRoute()
const appStore = useAppStore()

// 登录表单
const loginForm = reactive({
  username: 'admin',
  password: '123456',
  rememberMe: true
})

// 加载状态
const loading = ref(false)

// 处理登录提交
const handleSubmit = async () => {
  loading.value = true
  try {
    // 简单的登录验证
    if (loginForm.username === 'admin' && loginForm.password === '123456') {
      // 设置用户信息
      appStore.setUser({
        userName: loginForm.username,
        userAccount: loginForm.username
      })

      ElMessage.success('登录成功')

      // 如果有重定向地址，则跳转到重定向地址
      const redirectPath = route.query.redirect as string
      router.replace(redirectPath || '/home')
    } else {
      throw new Error('用户名或密码错误')
    }
  } catch (error) {
    console.error('登录失败:', error)
    ElMessage.error('登录失败，请检查用户名和密码')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.login-container {
  height: 100vh;
  width: 100vw;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: linear-gradient(to right bottom, #4b83eb, #84a9ef);
}

.login-box {
  width: 400px;
  padding: 40px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40px;

  .logo {
    width: 60px;
    height: 60px;
    margin-bottom: 16px;
  }

  .title {
    font-size: 24px;
    font-weight: 600;
    color: #000;
  }
}

.login-form {
  .remember-forgot {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
}
</style>
