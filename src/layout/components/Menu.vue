<template>
  <el-menu
    :default-active="activeMenu"
    :collapse="isCollapse"
    :unique-opened="true"
    class="sidebar-menu"
    background-color="transparent"
    text-color="rgba(255, 255, 255, 0.8)"
    active-text-color="#409eff"
    @select="handleMenuSelect"
  >
    <template v-for="item in menuItems" :key="item.path">
      <!-- 有子菜单的情况 -->
      <el-sub-menu v-if="item.children && item.children.length > 0" :index="item.path">
        <template #title>
          <el-icon v-if="item.meta?.icon" class="menu-icon">
            <component :is="item.meta.icon" />
          </el-icon>
          <span>{{ item.meta?.title }}</span>
        </template>
        <el-menu-item v-for="child in item.children" :key="child.path" :index="child.path">
          <el-icon v-if="child.meta?.icon" class="menu-icon">
            <component :is="child.meta.icon" />
          </el-icon>
          <span>{{ child.meta?.title }}</span>
        </el-menu-item>
      </el-sub-menu>

      <!-- 无子菜单的情况 -->
      <el-menu-item v-else :index="item.path">
        <el-icon v-if="item.meta?.icon" class="menu-icon">
          <component :is="item.meta.icon" />
        </el-icon>
        <span>{{ item.meta?.title }}</span>
      </el-menu-item>
    </template>
  </el-menu>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/store/modules/app'
import router from '@/router'
import { generateMenuFromRoutes } from '@/utils/menu'

defineOptions({
  name: 'SidebarMenu'
})

const route = useRoute()
const routerInstance = useRouter()
const appStore = useAppStore()

// 动态生成菜单数据
const menuItems = computed(() => {
  const routes = router.getRoutes()
  return generateMenuFromRoutes(routes)
})

const isCollapse = computed(() => appStore.isCollapse)
const activeMenu = computed(() => route.path)

const handleMenuSelect = (index: string) => {
  routerInstance.push(index)
}
</script>

<style lang="scss" scoped>
.sidebar-menu {
  border: none;
  background: transparent;

  :deep(.el-menu-item) {
    height: 48px;
    line-height: 48px;
    margin: 4px 8px;
    border-radius: 6px;
    color: rgba(255, 255, 255, 0.8);

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: #409eff;
    }

    &.is-active {
      background: rgba(64, 158, 255, 0.2);
      color: #409eff;
      border-right: 3px solid #409eff;
    }
  }

  :deep(.el-sub-menu__title) {
    height: 48px;
    line-height: 48px;
    margin: 4px 8px;
    border-radius: 6px;
    color: rgba(255, 255, 255, 0.8);

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: #409eff;
    }
  }

  :deep(.el-sub-menu) {
    .el-menu {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 6px;
      margin: 0 8px;
    }
  }
}

.menu-icon {
  margin-right: 8px;
  font-size: 16px;
}
</style>
