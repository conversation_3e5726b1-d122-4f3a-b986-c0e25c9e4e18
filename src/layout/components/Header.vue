<template>
  <div class="header">
    <!-- 左侧区域 -->
    <div class="header__left">
      <el-button type="text" class="collapse-btn" @click="toggleCollapse">
        <el-icon :size="20" class="collapse-icon">
          <Fold v-if="!isCollapse" />
          <Expand v-else />
        </el-icon>
      </el-button>

      <el-breadcrumb separator="/" class="breadcrumb">
        <el-breadcrumb-item v-for="item in breadcrumbs" :key="item.path">
          {{ item.title }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 右侧区域 -->
    <div class="header__right">
      <!-- 用户信息 -->
      <el-dropdown trigger="click" @command="handleUserCommand">
        <div class="user-info">
          <el-avatar :size="32" :src="userInfo.avatar">
            {{ userInfo.userName?.charAt(0) }}
          </el-avatar>
          <span class="user-name">{{ userInfo.userName }}</span>
          <el-icon class="user-arrow">
            <ArrowDown />
          </el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">
              <el-icon><User /></el-icon>
              个人中心
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <el-icon><Setting /></el-icon>
              系统设置
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/store/modules/app'
import { ElMessage, ElMessageBox } from 'element-plus'

defineOptions({
  name: 'LayoutHeader'
})

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()

const isCollapse = computed(() => appStore.isCollapse)
const userInfo = computed(() => appStore.userInfo)

// 面包屑导航
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta?.title)
  return matched.map(item => ({
    path: item.path,
    title: item.meta?.title as string
  }))
})

const toggleCollapse = () => {
  appStore.toggleCollapse()
}

const handleUserCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        appStore.logout()
        router.push('/login')
        ElMessage.success('已退出登录')
      } catch {
        // 用户取消
      }
      break
  }
}
</script>

<style lang="scss" scoped>
.header {
  height: 60px;
  background: $header-bg;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  &__left {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  &__right {
    display: flex;
    align-items: center;
  }
}

.collapse-btn {
  color: rgba(255, 255, 255, 0.8) !important;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.1) !important;
  border: none !important;

  &:hover {
    color: #409eff !important;
    background: rgba(255, 255, 255, 0.1) !important;
  }

  .el-icon {
    color: inherit;
  }

  .collapse-icon {
    color: #ffffff !important;
    font-size: 20px;
  }
}

.breadcrumb {
  :deep(.el-breadcrumb__item) {
    .el-breadcrumb__inner {
      color: rgba(255, 255, 255, 0.8);
      font-weight: normal;

      &:hover {
        color: #409eff;
      }
    }

    &:last-child .el-breadcrumb__inner {
      color: #409eff;
      font-weight: 600;
    }
  }

  :deep(.el-breadcrumb__separator) {
    color: rgba(255, 255, 255, 0.5);
  }
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .user-name {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
  }

  .user-arrow {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
  }
}

:deep(.el-dropdown-menu) {
  .el-dropdown-menu__item {
    display: flex;
    align-items: center;
    gap: 8px;

    .el-icon {
      margin-right: 0;
    }
  }
}
</style>
