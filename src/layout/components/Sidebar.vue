<template>
  <div class="sidebar" :class="{ 'sidebar--collapsed': isCollapse }">
    <!-- Logo 区域 -->
    <div class="sidebar__logo">
      <img src="@/assets/logo.svg" alt="Logo" class="sidebar__logo-img" />
      <span v-show="!isCollapse" class="sidebar__logo-text">VOC管理系统</span>
    </div>

    <!-- 菜单区域 -->
    <div class="sidebar__menu">
      <SidebarMenu />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAppStore } from '@/store/modules/app'
import SidebarMenu from './Menu.vue'

defineOptions({
  name: 'Sidebar'
})

const appStore = useAppStore()
const isCollapse = computed(() => appStore.isCollapse)
</script>

<style lang="scss" scoped>
.sidebar {
  width: 240px;
  height: 100vh;
  background: $sidebar-bg;
  transition: width 0.3s ease;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  &--collapsed {
    width: 64px;
  }

  &__logo {
    height: 60px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);

    &-img {
      width: 32px;
      height: 32px;
      border-radius: 6px;
    }

    &-text {
      margin-left: 12px;
      color: $text-white;
      font-size: 16px;
      font-weight: 600;
      white-space: nowrap;
      opacity: 1;
      transition: opacity 0.3s ease;
    }
  }

  &__menu {
    flex: 1;
    overflow-y: auto;
    padding: 16px 0;

    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}
</style>
