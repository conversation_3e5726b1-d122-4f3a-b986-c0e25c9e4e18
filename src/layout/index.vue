<template>
  <div class="layout">
    <!-- 侧边栏 -->
    <Sidebar />

    <!-- 主内容区域 -->
    <div class="layout__main">
      <!-- 顶部导航栏 -->
      <LayoutHeader />

      <!-- 页面内容区域 -->
      <div class="layout__content">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Sidebar from './components/Sidebar.vue'
import LayoutHeader from './components/Header.vue'

defineOptions({
  name: 'Layout'
})
</script>

<style lang="scss" scoped>
.layout {
  display: flex;
  width: 100%;
  height: 100vh;
  background: $content-bg;

  &__main {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; // 确保flex子元素可以正确收缩
  }

  &__content {
    flex: 1;
    padding: 20px;
    background: $content-bg;
    min-height: 0; // 确保flex子元素可以正确收缩

    // 如果内容超出容器，允许滚动
    overflow-y: auto;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }
}
</style>
