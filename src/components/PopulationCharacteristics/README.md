# PopulationCharacteristics 人群特征组件

## 组件用途

用于展示用户群体特征分析，支持性别、年龄、客户类型等多维度分布，中央显示人群总数，两侧和底部展示详细分布。

## 组件目录结构

```
src/components/PopulationCharacteristics/
├── index.vue          # 主组件文件
├── types.d.ts         # TypeScript类型定义
└── README.md          # 组件说明文档
```

## Props 参数

| 参数    | 说明         | 类型           | 是否必填 | 默认值 |
| ------- | ------------ | -------------- | -------- | ------ |
| data    | 人群特征数据 | PopulationData | 是       | -      |
| loading | 加载状态     | boolean        | 否       | false  |

### PopulationData 类型

```
interface PopulationDetail {
  type: string;        // 特征类型（如：性别、年龄、客户类型）
  secondType: string;  // 具体分类（如：男性、25-35岁、VIP客户）
  mentionRate: number; // 提及率（0-1之间的小数）
}

interface PopulationData {
  total: number; // 人群总数
  details: PopulationDetail[];
}
```

## 使用示例

```vue
<script setup lang="ts">
import PopulationCharacteristics from '@/components/PopulationCharacteristics/index.vue'
import type { PopulationData } from '@/components/PopulationCharacteristics/types.d'

const data: PopulationData = {
  total: 10000,
  details: [
    { type: '性别', secondType: '男', mentionRate: 0.6 },
    { type: '性别', secondType: '女', mentionRate: 0.4 },
    { type: '年龄段', secondType: '26-35', mentionRate: 0.5 },
    { type: '年龄段', secondType: '18-25', mentionRate: 0.2 },
    { type: '客户分类', secondType: '老客户', mentionRate: 0.7 },
    { type: '客户分类', secondType: '新客户', mentionRate: 0.3 },
    { type: '最近一次购车年限', secondType: '1年以内', mentionRate: 0.4 },
    { type: '最高学历', secondType: '本科', mentionRate: 0.5 }
  ]
}
</script>

<template>
  <PopulationCharacteristics :data="data" :loading="false" />
</template>
```

## 依赖

- Vue 3 + TypeScript
- Element Plus
- 项目统一SCSS变量
- 工具函数：formatPercent, Thousandth

## 设计说明

- 左右对称三栏布局，中央为总人数，两侧为特征分布
- 底部详细列表展示各维度Top5
- 支持加载、空数据、工具提示

## 维护人

- AI助手
