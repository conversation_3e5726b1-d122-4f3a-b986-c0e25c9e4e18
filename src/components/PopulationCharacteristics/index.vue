<template>
  <div class="population-characteristics-container">
    <!-- 标题栏 -->
    <div class="chart-header">
      <h3 class="chart-title">人群特征</h3>
      <div class="chart-actions">
        <el-button v-if="loading" type="primary" :loading="true" size="small">
          加载中...
        </el-button>
        <el-button v-else type="primary" size="small" @click="handleDownload"> 下载 </el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <!-- 主要内容 -->
    <div v-else-if="Object.keys(data).length" class="chart-content">
      <div class="population-characteristics p-t-10">
        <div class="each-part">
          <div
            class="left-part-detail mt-20 mb-20 pl-10 pt-5 pb-5 pt-10 pr-10"
            v-for="(item, index) in leftArr"
            :key="index"
          >
            <span style="font-weight: 500">{{ item.type }}：</span>
            {{ item.secondType }}为主（占{{ formatPercent(item.mentionRate) }}%）
            <div class="show-color" :style="{ background: colors[2 * index] }"></div>
          </div>
        </div>
        <div class="each-part each-part-center">
          <div class="each-part-center-img">
            <img src="@/assets/imgs/ren_qun_qun_ti.png" alt="" />
          </div>
          <div class="each-part-center-total total">
            人群总数：{{ formatThousandth(data.totalUsers || 0) }}人
          </div>
          <div class="circle1" v-if="leftArr.length >= 1"></div>
          <div class="circle2" v-if="leftArr.length >= 2"></div>
          <div class="circle3" v-if="leftArr.length >= 3"></div>

          <div class="circle4" v-if="rightArr.length >= 1"></div>
          <div class="circle5" v-if="rightArr.length >= 2"></div>
          <div class="circle6" v-if="rightArr.length >= 3"></div>
        </div>
        <div class="each-part">
          <div
            class="right-part-detail mt-20 mb-20 pl-10 pt-5 pb-5 pt-10 pr-10"
            v-for="(item, index) in rightArr"
            :key="index"
          >
            <span style="font-weight: 500">{{ item.type }}：</span>
            {{ item.secondType }}为主（占{{ formatPercent(item.mentionRate) }}%）
            <div class="show-color" :style="{ background: colors[2 * index + 1] }"></div>
          </div>
        </div>
      </div>

      <div class="list-area">
        <div
          class="each-list"
          :style="{ border: border[indexsec], boxShadow: boxShadow[indexsec] }"
          v-for="(item, key, indexsec) in chartObj"
          :key="indexsec"
        >
          <div class="each-list-title pl-15 pt-10 pb-10" :style="{ background: colors[indexsec] }">
            <span class="tooltips-name">{{ key }}</span>
            <span class="tooltips" v-if="key != '性别'">top5</span>
          </div>
          <div class="each-list-body">
            <div
              class="each-list-detail pt-8 pb-8"
              v-for="(itemsec, indexsec) in item"
              :key="indexsec"
            >
              <span
                v-if="key != '客户类型' && key != '性别' && indexsec < 3"
                class="number"
                :class="'no-' + indexsec"
                >NO{{ indexsec + 1 }}</span
              >
              <span v-if="key != '客户类型' && key != '性别' && indexsec >= 3" class="number">{{
                indexsec + 1
              }}</span>
              <span v-if="key == '客户类型' || key == '性别'" class="pl-15" style="width: 0"></span>
              <el-tooltip class="item" effect="dark" :content="itemsec.secondType" placement="top">
                <span>{{ itemsec.secondType }}</span>
              </el-tooltip>
              <span class="each-list-value">{{ formatPercent(itemsec.mentionRate) }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 无数据状态 -->
    <div v-else class="no-data-container">
      <el-empty description="暂无数据" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { PopulationCharacteristicsResponse } from '@/api/common/index.d'

// 组件名称定义
defineOptions({
  name: 'PopulationCharacteristics'
})

// 内部数据结构 (用于兼容现有显示逻辑)
interface PopulationDetail {
  type: string
  secondType: string
  mentionRate: number
}

// Props 接口定义
interface Props {
  chartId?: string
  data: PopulationCharacteristicsResponse
  loading?: boolean
  tabTitle?: string
}

// Props 和默认值
const props = withDefaults(defineProps<Props>(), {
  chartId: '',
  data: () => ({
    totalUsers: 0,
    genderDistributions: [],
    ageDistributions: [],
    customerTypeDistributions: [],
    carAgeDistributions: [],
    provinceDistributions: [],
    educationDistributions: []
  }),
  loading: false,
  tabTitle: '营销服务'
})

// Emits 定义
const emit = defineEmits<{
  download: [command: string, type: string, data: PopulationCharacteristicsResponse]
}>()

// 响应式数据
const chartObj = ref<Record<string, PopulationDetail[]>>({})
const leftArr = ref<PopulationDetail[]>([])
const rightArr = ref<PopulationDetail[]>([])

// 样式配置
const colors = ['#DAF0FF', '#C7CEDA', '#CFF4EA', '#DACEED', '#C7CEDA', '#CFF4EA']
const border = [
  '1px solid #2994FF',
  '1px solid #5D7092',
  '1px solid #3ED4A9',
  '1px solid #BA70CA',
  '1px solid #5D7092',
  '1px solid #3ED4A9'
]
const boxShadow = [
  '0px 0px 0px 3px #DAF0FF',
  '0px 0px 0px 3px #C7CEDA',
  '0px 0px 0px 3px #CFF4EA',
  '0px 0px 0px 3px #DACEED',
  '0px 0px 0px 3px #C7CEDA',
  '0px 0px 0px 3px #CFF4EA '
]

// 工具函数
const formatPercent = (value: number): string => {
  return (value * 100).toFixed(1)
}

const formatThousandth = (value: number): string => {
  return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// 将新的API数据结构转换为组件内部使用的数据结构
const convertApiDataToInternalFormat = (
  data: PopulationCharacteristicsResponse
): PopulationDetail[] => {
  const details: PopulationDetail[] = []

  // 性别分布
  if (data.genderDistributions) {
    data.genderDistributions.forEach(item => {
      if (item.gender && typeof item.proportion === 'number') {
        details.push({
          type: '性别',
          secondType: item.gender,
          mentionRate: item.proportion
        })
      }
    })
  }

  // 年龄分布
  if (data.ageDistributions) {
    data.ageDistributions.forEach(item => {
      if (item.ageRange && typeof item.proportion === 'number') {
        details.push({
          type: '年龄段',
          secondType: item.ageRange,
          mentionRate: item.proportion
        })
      }
    })
  }

  // 客户类型分布
  if (data.customerTypeDistributions) {
    data.customerTypeDistributions.forEach(item => {
      if (item.customerType && typeof item.proportion === 'number') {
        details.push({
          type: '客户分类',
          secondType: item.customerType,
          mentionRate: item.proportion
        })
      }
    })
  }

  // 车龄分布
  if (data.carAgeDistributions) {
    data.carAgeDistributions.forEach(item => {
      if (item.carAge && typeof item.proportion === 'number') {
        details.push({
          type: '最近一次购车年限',
          secondType: item.carAge,
          mentionRate: item.proportion
        })
      }
    })
  }

  // 省份分布
  if (data.provinceDistributions) {
    data.provinceDistributions.forEach(item => {
      if (item.provinceName && typeof item.proportion === 'number') {
        details.push({
          type: '客户常驻地所在省份',
          secondType: item.provinceName,
          mentionRate: item.proportion
        })
      }
    })
  }

  // 学历分布
  if (data.educationDistributions) {
    data.educationDistributions.forEach(item => {
      if (item.education && typeof item.proportion === 'number') {
        details.push({
          type: '最高学历',
          secondType: item.education,
          mentionRate: item.proportion
        })
      }
    })
  }

  return details
}

// 处理数据
const dataHandle = () => {
  const obj: Record<string, PopulationDetail[]> = {}
  const details = convertApiDataToInternalFormat(props.data)

  for (let i = 0; i < details.length; i++) {
    const firstname = details[i].type
    if (obj[firstname] === undefined) {
      obj[firstname] = []
    }
    obj[firstname].push(details[i])
  }

  const leftArrTemp: PopulationDetail[] = []
  const rightArrTemp: PopulationDetail[] = []
  let index = 0

  for (const key in obj) {
    obj[key].sort((a, b) => {
      return b.mentionRate - a.mentionRate
    })
    if (index % 2 === 0) leftArrTemp.push(obj[key][0])
    else rightArrTemp.push(obj[key][0])
    index++
    obj[key] = obj[key].slice(0, 5)
  }

  chartObj.value = obj
  leftArr.value = leftArrTemp
  rightArr.value = rightArrTemp
}

// 下载数据
const handleDownload = () => {
  emit('download', 'download', 'population', props.data)
}

// 监听数据变化
watch(
  () => props.data,
  () => {
    dataHandle()
  },
  { immediate: true, deep: true }
)
</script>

<style lang="scss" scoped>
.population-characteristics-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.03);
  overflow: hidden;
  font-size: 16px;
  line-height: 1.3;

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;

    .chart-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }

    .chart-actions {
      display: flex;
      gap: 8px;
    }
  }

  .loading-container {
    padding: 24px;
  }

  .chart-content {
    padding: 24px;
  }

  .no-data-container {
    padding: 60px 24px;
    text-align: center;
  }
}

.population-characteristics {
  width: 100%;
  display: flex;

  .each-part {
    flex: 1;
  }

  .left-part-detail {
    position: relative;
    text-align: right;
    float: right;
    width: 90%;
    background: #f9fafb;
    border: 1px solid rgba(222, 227, 233, 1);
    border-radius: 4px;
    color: rgba(0, 0, 0, 0.65);
    font-weight: 400;

    .show-color {
      width: 5px;
      height: 100%;
      position: absolute;
      top: 0;
      right: 0;
    }
  }

  .right-part-detail {
    position: relative;
    text-align: left;
    float: left;
    width: 80%;
    background: #f9fafb;
    border: 1px solid rgba(222, 227, 233, 1);
    border-radius: 4px;
    color: rgba(0, 0, 0, 0.65);
    font-weight: 400;

    .show-color {
      width: 5px;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background: pink;
    }
  }
}

.each-part-center {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  position: relative;

  .each-part-center-img {
    position: absolute;
    left: calc(50% - 87.5px);
    top: calc(50% - 80px);
    z-index: 100;
  }

  .each-part-center-total {
    position: absolute;
    // left: calc(50% - 97.5px);
    left: calc(50% - 80px);
    top: calc(50% + 80px);
    z-index: 100;
  }
}

.total {
  background: #f9fcfd;
  border: 1px solid rgba(41, 148, 255, 1);
  box-shadow: 0px 0px 0px 2px rgba(41, 148, 255, 0.24);
  border-radius: 31px;
  padding: 7px 20px;
  color: #0077ff;
  font-weight: 500;
  margin-top: -30px;
  z-index: 100;
}

.list-area {
  width: 100%;
  margin-top: 50px;
  display: flex;

  .each-list {
    width: calc(16.6% - 20px);
    border: solid 1px rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    margin: 0 10px;
  }
}

.each-list-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
}

.each-list-body {
  width: 100%;
  height: 250px;
  overflow: auto;
}

.tooltips-name {
  box-sizing: border-box;
  word-break: keep-all;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: calc(100% - 50px);
  display: inline-block;
}

.each-list-detail {
  color: #595959;
  font-weight: 400;
  font-size: 14px;
  border-bottom: solid 1px rgba(0, 0, 0, 0.06);
  display: flex;
  height: 20%;
  align-items: center;

  span {
    display: inline-block;
    line-height: 21px;
  }

  & span:nth-of-type(1) {
    width: 40px;
  }

  & span:nth-of-type(2) {
    flex: 1;
    padding-left: 10px;
    box-sizing: border-box;
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 5px;
  }

  & span:nth-of-type(3) {
    width: 55px;
  }
}

.number {
  font-size: 10px;
  text-align: center;
  line-height: 12px;
  font-weight: 400;
}

.no-0 {
  color: #ff1b1b;
}

.no-1 {
  color: #ff7a1b;
}

.no-2 {
  color: #ffb91b;
}

.each-list-value {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
}

.tooltips {
  line-height: 19px;
  font-size: 10px;
  color: #605656;
  text-align: center;
  line-height: 12px;
  font-weight: 400;
  float: right;
  padding-right: 20px;
}

.circle1 {
  width: 200px;
  height: 60px;
  border: 1px solid rgba(166, 176, 195, 1);
  border-radius: 0% 0% 0% 150%;
  border-top: none;
  border-right: none;
  position: absolute;
  left: 0;
  top: 33px;
}

.circle2 {
  width: 200px;
  height: 16px;
  border: 1px solid rgba(166, 176, 195, 1);
  border-radius: 0% 0% 0% 150%;
  border-top: none;
  border-right: none;
  position: absolute;
  left: 0;
  top: 114px;
}

.circle3 {
  width: 200px;
  height: 31px;
  border: 1px solid rgba(166, 176, 195, 1);
  border-radius: 150% 0% 0% 0%;
  border-bottom: none;
  border-right: none;
  position: absolute;
  left: 0;
  top: 164px;
}

.circle4 {
  width: 200px;
  height: 60px;
  border: 1px solid rgba(166, 176, 195, 1);
  border-radius: 0% 0% 150% 0%;
  border-top: none;
  border-left: none;
  position: absolute;
  right: 0;
  top: 33px;
}

.circle5 {
  width: 200px;
  height: 16px;
  border: 1px solid rgba(166, 176, 195, 1);
  border-radius: 0% 0% 150% 0%;
  border-top: none;
  border-left: none;
  position: absolute;
  right: 0;
  top: 114px;
}

.circle6 {
  width: 200px;
  height: 31px;
  border: 1px solid rgba(166, 176, 195, 1);
  border-radius: 0 150% 0% 0%;
  border-bottom: none;
  border-left: none;
  position: absolute;
  right: 0;
  top: 164px;
}

// 响应式设计
@media (max-width: 768px) {
  .population-characteristics-container {
    .chart-header {
      padding: 12px 16px;

      .chart-title {
        font-size: 14px;
      }
    }

    .chart-content {
      padding: 16px;
    }
  }

  .list-area {
    flex-direction: column;

    .each-list {
      width: 100%;
      margin: 0 0 16px 0;
    }
  }
}
</style>
