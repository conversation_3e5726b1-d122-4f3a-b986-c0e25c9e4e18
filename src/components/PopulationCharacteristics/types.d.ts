// 人群特征组件类型定义
import type { PopulationCharacteristicsResponse } from '@/api/common/index.d'

// 单个特征明细 (保留兼容性)
export interface PopulationDetail {
  type: string // 特征类型（如：性别、年龄、客户类型）
  secondType: string // 具体分类（如：男性、25-35岁、VIP客户）
  mentionRate: number // 提及率（0-1之间的小数）
}

// 组件主数据结构 (保留兼容性)
export interface PopulationData {
  total: number // 人群总数
  details: PopulationDetail[]
}

// 组件Props - 现在支持新的API数据结构
export interface PopulationCharacteristicsProps {
  data: PopulationCharacteristicsResponse
  loading?: boolean
}
