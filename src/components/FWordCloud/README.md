# FWordCloud 词云组件

基于 ECharts 的词云图组件，用于展示文本数据的词频分布。

## 功能特性

- 🎨 使用项目统一的图表主题色
- 📊 支持自定义字体大小范围
- 🔧 支持字段映射，兼容不同数据结构
- 💡 支持自定义 tooltip 格式化
- 🖱️ 支持点击事件交互
- 📱 响应式设计，支持自定义尺寸

## 基础用法

```vue
<template>
  <FWordCloud :data="wordData" height="300px" />
</template>

<script setup lang="ts">
const wordData = [
  { name: '充电', value: 100 },
  { name: '续航', value: 90 },
  { name: '服务', value: 80 },
  { name: '价格', value: 70 },
  { name: '质量', value: 60 }
]
</script>
```

## 高级用法

### 字段映射

当数据结构与默认结构不同时，可以使用字段映射：

```vue
<template>
  <FWordCloud
    :data="customData"
    :field-mapping="{ nameField: 'word', valueField: 'count' }"
    height="300px"
  />
</template>

<script setup lang="ts">
const customData = [
  { word: '充电', count: 100 },
  { word: '续航', count: 90 }
]
</script>
```

### 自定义样式

```vue
<template>
  <FWordCloud
    :data="wordData"
    :size-range="[14, 40]"
    :grid-size="10"
    font-family="Microsoft YaHei"
    font-weight="600"
    height="400px"
  />
</template>
```

### 事件处理

```vue
<template>
  <FWordCloud :data="wordData" @chart-click="handleWordClick" height="300px" />
</template>

<script setup lang="ts">
const handleWordClick = (params: any) => {
  console.log('点击了词汇:', params.data.name, '权重:', params.data.value)
}
</script>
```

### 自定义 Tooltip

```vue
<template>
  <FWordCloud :data="wordData" :tooltip-formatter="customTooltipFormatter" height="300px" />
</template>

<script setup lang="ts">
const customTooltipFormatter = (params: any) => {
  return `词汇: ${params.data.name}<br/>出现次数: ${params.data.value} 次`
}
</script>
```

## API

### Props

| 参数             | 类型                      | 默认值                                    | 说明                      |
| ---------------- | ------------------------- | ----------------------------------------- | ------------------------- |
| data             | `WordCloudDataItem[]`     | `[]`                                      | 词云数据                  |
| width            | `string`                  | `'100%'`                                  | 组件宽度                  |
| height           | `string`                  | `'100%'`                                  | 组件高度                  |
| sizeRange        | `[number, number]`        | `[12, 30]`                                | 字体大小范围              |
| gridSize         | `number`                  | `8`                                       | 网格大小，影响词汇间距    |
| left             | `string`                  | `'center'`                                | 水平位置                  |
| top              | `string`                  | `'center'`                                | 垂直位置                  |
| fontFamily       | `string`                  | `'sans-serif'`                            | 字体                      |
| fontWeight       | `string \| number`        | `'bold'`                                  | 字体粗细                  |
| theme            | `string`                  | `''`                                      | ECharts 主题              |
| tooltip          | `boolean`                 | `true`                                    | 是否显示 tooltip          |
| tooltipFormatter | `(params: any) => string` | -                                         | 自定义 tooltip 格式化函数 |
| fieldMapping     | `WordCloudFieldMapping`   | -                                         | 字段映射配置              |
| drawOutOfBound   | `boolean`                 | `false`                                   | 是否允许超出边界绘制      |
| emphasis         | `EmphasisStyle`           | `{ shadowBlur: 10, shadowColor: '#333' }` | 高亮样式                  |

### Events

| 事件名      | 说明           | 回调参数        |
| ----------- | -------------- | --------------- |
| chart-click | 点击词汇时触发 | `(params: any)` |

### 类型定义

```typescript
interface WordCloudDataItem {
  name: string // 词汇名称
  value: number // 词汇权重值
  [key: string]: any // 扩展字段
}

interface WordCloudFieldMapping {
  nameField: string // 名称字段，默认 'name'
  valueField: string // 数值字段，默认 'value'
}

interface EmphasisStyle {
  shadowBlur?: number
  shadowColor?: string
}
```

## 注意事项

1. 确保已安装 `echarts-wordcloud` 插件
2. 数据中的 `value` 值会影响词汇的显示大小
3. 词汇颜色会从项目主题色中随机选择
4. 组件会自动处理空数据情况
