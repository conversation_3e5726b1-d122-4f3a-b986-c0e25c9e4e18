<template>
  <div class="experience-tips">
    <div class="item">
      <img src="@/assets/imgs/ti_yan_zhi.png" alt="1" class="w-40 h-40" />
      <div class="item-content">
        <div class="title">体验值</div>
        <div class="content">{{ props.data.totalExperienceValue }}</div>
      </div>
    </div>
    <div class="item border-bottom">
      <img src="@/assets/imgs/ti_yan_zhi_huan_bi.png" alt="1" class="w-40 h-40" />
      <div class="item-content">
        <div class="title">体验值环比</div>
        <div class="content">
          <ShowCompare
            :compare-key="'momExperienceValueRate'"
            custom-class="data-value-inner"
            :compare-value="props.data.totalExperienceValueMoM || 0"
          />
        </div>
      </div>
    </div>

    <div class="item">
      <img src="@/assets/imgs/ti_ji_liang.png" alt="1" class="w-40 h-40" />
      <div class="item-content">
        <div class="title">提及量</div>
        <div class="content">{{ props.data.totalMentions }}</div>
      </div>
    </div>
    <div class="item">
      <img src="@/assets/imgs/ti_ji_liang_huan_bi.png" alt="1" class="w-40 h-40" />
      <div class="item-content">
        <div class="title">提及量环比</div>
        <div class="content">
          <ShowCompare
            :compare-key="'momTotalMentionValueRate'"
            custom-class="data-value-inner"
            :compare-value="props.data.totalMentionsMoM || 0"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ExperienceTipsProps } from './types.d'
import ShowCompare from '@/components/ShowCompare/index.vue'

defineOptions({
  name: 'ExperienceTips'
})

const props = defineProps<ExperienceTipsProps>()
</script>

<style scoped lang="scss">
.experience-tips {
  .item {
    display: flex;
    align-items: center;

    & + .item {
      margin-top: 20px;
    }

    .item-content {
      flex: 1;
      padding-left: 15px;
      .title {
        font-size: 16px;
        font-weight: 500;
        color: #5d7092;
        line-height: 22px;
      }
      .content {
        font-size: 22px;
        font-weight: 500;
        color: #202020;
        line-height: 30px;
      }
    }
  }

  .border-bottom {
    border-bottom: solid 1px #d8d8d8;
    padding-bottom: 20px;
  }

  :deep(.data-value-inner) {
    font-size: 22px;
    font-weight: 500;
    // color: #202020;
    line-height: 30px;
  }
}
</style>
