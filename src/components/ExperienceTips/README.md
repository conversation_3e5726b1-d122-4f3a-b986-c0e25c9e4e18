# ExperienceTips - 体验提示组件

## 概述

体验提示组件用于显示 VOC 数据的关键指标概览，包括体验值、体验值环比、提及量、提及量环比等核心数据。组件采用卡片式设计，直观地展示数据变化趋势。

## 特性

- ✅ **Vue 3 Composition API**: 使用 `<script setup>` 语法
- ✅ **TypeScript 支持**: 完整的类型定义
- ✅ **响应式设计**: 适配不同屏幕尺寸
- ✅ **数据对比**: 集成 ShowCompare 组件显示环比数据
- ✅ **视觉设计**: 配合图标和颜色的直观展示

## 使用示例

### 基础用法

```vue
<template>
  <ExperienceTips :data="experienceData" />
</template>

<script setup lang="ts">
import { ExperienceTips } from '@/components'
import type { CustomerTagExperienceResponse } from '@/api/common/index.d'

const experienceData: CustomerTagExperienceResponse = {
  totalExperienceValue: 85.6,
  totalExperienceValueMoM: 0.12,
  totalMentions: 12456,
  totalMentionsMoM: -0.08
}
</script>
```

## Props

| 属性 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| data | `CustomerTagExperienceResponse` | 是 | - | 客户标签体验数据 |

### data 数据结构

```typescript
interface CustomerTagExperienceResponse {
  totalExperienceValue: number    // 体验值
  totalExperienceValueMoM: number // 体验值环比
  totalMentions: number          // 提及量
  totalMentionsMoM: number       // 提及量环比
}
```

## 样式定制

组件使用 SCSS 编写样式，支持以下 CSS 变量自定义：

```scss
.experience-tips {
  // 自定义间距
  --item-spacing: 20px;
  
  // 自定义标题颜色
  --title-color: #5d7092;
  
  // 自定义内容颜色
  --content-color: #202020;
  
  // 自定义边框颜色
  --border-color: #d8d8d8;
}
```

## 依赖组件

- `ShowCompare`: 用于显示环比数据的对比组件

## 更新日志

### v1.0.0
- 基础功能实现
- 支持体验值和提及量数据展示
- 集成环比数据对比功能
- 响应式设计适配