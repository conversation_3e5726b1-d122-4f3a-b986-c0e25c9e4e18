<template>
  <FEcharts
    :options="chartOptions"
    :width="width"
    :height="height"
    :theme="theme"
    @chart-click="handleChartClick"
    @chart-ready="handleChartReady"
  />
</template>

<script setup lang="ts">
import { computed, shallowRef, watch } from 'vue'
import FEcharts from '@/components/FEcharts/index.vue'
import { CHART_THEME_COLORS } from '@/constants'
import type { EChartsOption } from 'echarts'
import type {
  StackedBarDataItem,
  StackedBarFieldMapping,
  ColorMapping,
  TooltipFormatter,
  LabelFormatter,
  GridConfig,
  LegendConfig,
  DataZoomConfig
} from './types'

interface Props {
  data: StackedBarDataItem[]
  width?: string
  height?: string
  theme?: string
  direction?: 'horizontal' | 'vertical'
  barWidth?: number | string
  grid?: GridConfig
  legend?: LegendConfig
  showLabel?: boolean
  showPercentage?: boolean
  fieldMapping?: StackedBarFieldMapping
  colorMapping?: ColorMapping
  tooltipFormatter?: TooltipFormatter
  labelFormatter?: LabelFormatter
  enableDataZoom?: boolean
  dataZoomConfig?: DataZoomConfig
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  width: '100%',
  height: '100%',
  theme: '',
  direction: 'horizontal',
  barWidth: 20,
  grid: () => ({
    left: '5%',
    right: '5%',
    top: '10%',
    bottom: '5%',
    containLabel: true
  }),
  legend: () => ({
    show: true,
    top: 0,
    right: 0,
    orient: 'horizontal'
  }),
  showLabel: false,
  showPercentage: true,
  fieldMapping: undefined,
  colorMapping: undefined,
  tooltipFormatter: undefined,
  labelFormatter: undefined,
  enableDataZoom: false,
  dataZoomConfig: undefined
})

const emit = defineEmits<{
  chartClick: [params: any]
  chartReady: [chart: any]
}>()

// 使用 shallowRef 管理图表配置
const chartOptions = shallowRef<EChartsOption>({})

// 计算处理后的数据和系列信息
const processedData = computed(() => {
  if (!props.data || props.data.length === 0) {
    return { categories: [], series: [] }
  }

  const categoryField = props.fieldMapping?.categoryField || 'category'
  let seriesFields: string[] = []
  let seriesNames: string[] = []

  if (props.fieldMapping?.seriesFields) {
    seriesFields = props.fieldMapping.seriesFields
    seriesNames = props.fieldMapping.seriesNames || seriesFields
  } else {
    // 自动检测系列字段
    const firstItem = props.data[0]
    seriesFields = Object.keys(firstItem).filter(
      key => key !== categoryField && typeof firstItem[key] === 'number'
    )
    seriesNames = seriesFields
  }

  // 提取分类数据
  const categories = props.data.map(item => item[categoryField] as string)

  // 构建系列数据
  const series = seriesFields.map((field, index) => {
    const seriesName = seriesNames[index] || field
    const data = props.data.map(item => item[field] as number)

    return {
      name: seriesName,
      type: 'bar' as const,
      stack: 'total',
      barWidth: props.barWidth,
      data,
      itemStyle: {
        color:
          props.colorMapping?.[seriesName] || CHART_THEME_COLORS[index % CHART_THEME_COLORS.length]
      },
      label: {
        show: props.showLabel,
        position: 'inside' as const,
        formatter: props.labelFormatter || '{c}'
      }
    }
  })

  return { categories, series, seriesNames }
})

// 默认的 tooltip 格式化函数
const defaultTooltipFormatter = (params: any) => {
  if (!Array.isArray(params)) {
    params = [params]
  }

  let total = 0
  params.forEach((param: any) => {
    total += param.value
  })

  let result = `${params[0].axisValue}<br/>`

  params.forEach((param: any) => {
    const percentage =
      props.showPercentage && total > 0 ? ` (${((param.value / total) * 100).toFixed(1)}%)` : ''
    result += `${param.marker}${param.seriesName}: ${param.value}${percentage}<br/>`
  })

  return result
}

// 监听数据变化，更新图表配置
watch(
  [
    () => props.data,
    () => processedData.value,
    () => props.direction,
    () => props.legend,
    () => props.showLabel,
    () => props.grid,
    () => props.enableDataZoom,
    () => props.dataZoomConfig
  ],
  () => {
    if (!props.data || props.data.length === 0) {
      chartOptions.value = {}
      return
    }

    const { categories, series } = processedData.value
    const formatter = props.tooltipFormatter || defaultTooltipFormatter

    const isHorizontal = props.direction === 'horizontal'

    // 构建 dataZoom 配置
    let dataZoom: any[] | undefined
    if (props.enableDataZoom) {
      // 默认的slider类型配置
      const defaultSliderConfig: any = {
        show: true,
        type: 'slider',
        showDetail: false,
        startValue: 100,
        endValue: 95, // 默认显示10条数据
        filterMode: 'empty',
        width: 3,
        height: '80%',
        right: 3,
        handleSize: 0,
        zoomLock: true,
        top: 'middle',
        backgroundColor: '#f5f5f5',
        borderColor: '#d9d9d9',
        borderWidth: 1,
        fillerColor: 'rgba(144, 144, 144, 0.2)',
        handleStyle: {
          color: '#fff',
          borderColor: '#d9d9d9',
          borderWidth: 1
        },
        textStyle: {
          color: '#666'
        },
        // 根据方向设置轴索引
        ...(isHorizontal ? { yAxisIndex: [0] } : { xAxisIndex: [0] })
      }

      // 默认的inside类型配置（支持鼠标操作）
      const defaultInsideConfig: any = {
        type: 'inside',
        zoomOnMouseWheel: false,
        moveOnMouseMove: true,
        moveOnMouseWheel: true,
        // 根据方向设置轴索引
        ...(isHorizontal ? { yAxisIndex: [0] } : { xAxisIndex: [0] })
      }

      // 如果用户提供了自定义配置，合并到slider配置中
      const sliderConfig = props.dataZoomConfig
        ? { ...defaultSliderConfig, ...props.dataZoomConfig }
        : defaultSliderConfig

      dataZoom = [sliderConfig, defaultInsideConfig]
    }

    chartOptions.value = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        backgroundColor: '#fff',
        borderWidth: 0,
        borderRadius: 4,
        padding: [8, 12],
        extraCssText: 'box-shadow: 0 1px 8px rgba(0,0,0,0.1);',
        formatter
      },
      legend: {
        ...props.legend
      },
      grid: props.grid,
      dataZoom,
      [isHorizontal ? 'xAxis' : 'yAxis']: {
        type: 'value',
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#666'
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0',
            type: 'dashed'
          }
        }
      },
      [isHorizontal ? 'yAxis' : 'xAxis']: {
        type: 'category',
        data: categories,
        axisLine: {
          lineStyle: {
            color: '#e0e0e0'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#666',
          interval: 0
        }
      },
      series
    }
  },
  { immediate: true, deep: true }
)

// 处理图表点击事件
const handleChartClick = (params: any) => {
  emit('chartClick', params)
}

// 处理图表就绪事件
const handleChartReady = (chart: any) => {
  emit('chartReady', chart)
}
</script>
