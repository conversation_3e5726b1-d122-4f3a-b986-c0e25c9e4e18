// 堆叠柱状图组件类型定义

/**
 * 堆叠柱状图数据项
 */
export interface StackedBarDataItem {
  [key: string]: number | string // 允许任意字段名，包含分类字段和数据字段
}

/**
 * 字段映射配置
 */
export interface StackedBarFieldMapping {
  categoryField: string // 分类字段，默认 'category'
  seriesFields: string[] // 系列字段数组
  seriesNames?: string[] // 系列显示名称（可选）
}

/**
 * 颜色映射配置
 */
export interface ColorMapping {
  [seriesName: string]: string // 系列名称对应的颜色
}

/**
 * 网格配置
 */
export interface GridConfig {
  left?: string | number
  right?: string | number
  top?: string | number
  bottom?: string | number
  containLabel?: boolean
}

/**
 * 图例配置
 */
export interface LegendConfig {
  type?: 'scroll' | 'plain'
  show?: boolean
  top?: string | number
  bottom?: string | number
  left?: string | number
  right?: string | number
  orient?: 'horizontal' | 'vertical'
  itemWidth?: number
  itemHeight?: number
  itemGap?: number
  textStyle?: {
    color?: string
    fontSize?: number
  }
}

/**
 * 数据缩放配置
 */
export interface DataZoomConfig {
  show?: boolean // 是否显示
  type?: 'slider' | 'inside' // 缩放类型
  orient?: 'horizontal' | 'vertical' // 方向
  start?: number // 开始位置百分比
  end?: number // 结束位置百分比
  startValue?: number | string // 开始值
  endValue?: number | string // 结束值
  minSpan?: number // 最小缩放范围百分比
  maxSpan?: number // 最大缩放范围百分比
  zoomLock?: boolean // 是否锁定缩放
  throttle?: number // 节流延迟
  xAxisIndex?: number | number[] // x轴索引
  yAxisIndex?: number | number[] // y轴索引
  filterMode?: 'filter' | 'weakFilter' | 'empty' | 'none' // 过滤模式
  showDetail?: boolean // 是否显示详细数值信息
  handleSize?: number // 控制手柄的尺寸
  // 位置配置
  left?: string | number
  right?: string | number
  top?: string | number
  bottom?: string | number
  width?: string | number
  height?: string | number
  // inside类型特有配置
  zoomOnMouseWheel?: boolean // 滚轮是否触发缩放
  moveOnMouseMove?: boolean // 鼠标移动能否触发平移
  moveOnMouseWheel?: boolean // 鼠标滚轮能否触发平移
  // 滑动条样式配置
  backgroundColor?: string
  borderColor?: string
  borderWidth?: number
  dataBackground?: {
    lineStyle?: {
      color?: string
      width?: number
    }
    areaStyle?: {
      color?: string
    }
  }
  selectedDataBackground?: {
    lineStyle?: {
      color?: string
      width?: number
    }
    areaStyle?: {
      color?: string
    }
  }
  fillerColor?: string
  handleStyle?: {
    color?: string
    borderColor?: string
    borderWidth?: number
  }
  moveHandleStyle?: {
    color?: string
    borderColor?: string
    borderWidth?: number
  }
  textStyle?: {
    color?: string
    fontSize?: number
  }
}

/**
 * Tooltip格式化函数
 */
export type TooltipFormatter = (params: any) => string

/**
 * 标签格式化函数
 */
export type LabelFormatter = (params: any) => string
