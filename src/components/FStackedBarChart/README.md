# FStackedBarChart 堆叠柱状图组件

基于 ECharts 封装的堆叠柱状图组件，支持横向和纵向堆叠显示，内置数据滚动功能。

## 快速开始

### 基础用法

```vue
<template>
  <FStackedBarChart :data="chartData" :width="'100%'" :height="'300px'" />
</template>

<script setup>
import FStackedBarChart from '@/components/FStackedBarChart/index.vue'

const chartData = [
  {
    category: '渠道情感',
    正面: 3492,
    负面: 800,
    中性: 200
  },
  {
    category: '东风风神',
    正面: 2800,
    负面: 600,
    中性: 100
  }
]
</script>
```

### 启用数据滚动

```vue
<template>
  <FStackedBarChart
    :data="chartData"
    :enable-data-zoom="true"
    :data-zoom-config="{
      startValue: 0,
      endValue: 5,
      width: 5,
      right: 10
    }"
    direction="horizontal"
  />
</template>
```

## 主要特性

- ✅ 支持横向和纵向堆叠
- ✅ 支持自定义字段映射
- ✅ 支持自定义颜色配置
- ✅ 支持标签显示和自定义格式化
- ✅ 支持自定义 tooltip 格式化
- ✅ **支持数据滚动和缩放功能**
- ✅ **双重交互模式（滚动条 + 区域内操作）**
- ✅ **可配置的滚动条样式和行为**
- ✅ 响应式数据更新
- ✅ 完整的 TypeScript 支持

## 数据滚动功能

### 基本配置

| 属性             | 类型             | 默认值  | 说明                   |
| ---------------- | ---------------- | ------- | ---------------------- |
| `enableDataZoom` | `boolean`        | `false` | 是否启用数据滚动       |
| `dataZoomConfig` | `DataZoomConfig` | `{}`    | 滚动配置，覆盖默认设置 |

### 默认滚动行为

启用 `enableDataZoom` 后，组件会自动配置：

- **Slider 滚动条**：右侧显示细窄滚动条，支持拖拽操作
- **Inside 操作**：图表区域内支持鼠标滚轮平移
- **数据窗口**：默认显示5条数据，可通过 `startValue`/`endValue` 调整
- **过滤模式**：使用 `empty` 模式，不影响其他轴的数据范围

### 自定义配置示例

```vue
<template>
  <FStackedBarChart
    :data="chartData"
    :enable-data-zoom="true"
    :data-zoom-config="{
      startValue: 0, // 起始数据索引
      endValue: 8, // 结束数据索引
      width: 5, // 滚动条宽度
      height: '90%', // 滚动条高度
      right: 5, // 距离右边距离
      handleSize: 10, // 手柄大小
      showDetail: true, // 显示拖拽详情
      zoomLock: false, // 是否锁定缩放
      top: 'center' // 垂直位置
    }"
  />
</template>
```

## 文档

详细文档请查看：[FStackedBarChart 文档](../../../docs/components/FStackedBarChart.md)

## 演示

演示页面：`src/views/examples/StackedBarChartExample.vue`
