<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { IndexAnalysisData, TopologicalData } from './types.d'
import TopologicalChart from './components/TopologicalChart.vue'
import IndexTable from './components/IndexTable.vue'
import { ElButton } from 'element-plus'

interface Props {
  chartId?: string
  loading?: boolean
  data: IndexAnalysisData
  remarkData: TopologicalData[]
  remarkData2?: string
  remarkData3?: string
  attr?: 'experienceValue' | 'negativeMentionRate'
  attrName?: string
}
const props = defineProps<Props>()

const tableData = computed(() => props.data.detail)
const topologicalData = computed(() => props.remarkData)

function handleDownload() {
  // 简单导出为 JSON 文件，实际可扩展为 Excel
  const blob = new Blob([JSON.stringify(props.data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'index-analysis-data.json'
  a.click()
  URL.revokeObjectURL(url)
}
</script>

<template>
  <div class="index-analysis">
    <!-- 标题栏 -->
    <div
      class="chart-header"
      style="display: flex; justify-content: space-between; align-items: center"
    >
      <h3>指标排名</h3>
    </div>
    <!-- 拓扑图 -->
    <div class="topological-chart" style="margin-bottom: 16px">
      <TopologicalChart
        :data="topologicalData"
        :chartId="props.chartId"
        :highlightId="props.remarkData3"
      />
    </div>
    <!-- 数据表格 -->
    <div class="data-table">
      <IndexTable
        :data="tableData"
        :attr="props.attr"
        :attrName="props.attrName"
        :loading="props.loading"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.index-analysis {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  .chart-header {
    margin-bottom: 12px;
  }
  .topological-chart {
    min-height: 320px;
  }
  .data-table {
    margin-top: 8px;
  }
}
</style>
