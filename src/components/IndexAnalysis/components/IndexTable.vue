<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElTable, ElTableColumn } from 'element-plus'
import type { IndexAnalysisData } from '../types.d'
import ShowCompare from '@/components/ShowCompare/index.vue'
import type { CompareKeyType } from '@/components/ShowCompare/types.d'

interface Props {
  data: IndexAnalysisData['detail']
  momData?: IndexAnalysisData['detail']
  attr?: 'experienceValue' | 'negativeMentionRate'
  attrName?: string
  loading?: boolean
}
const props = defineProps<Props>()

const sortField = ref<'experienceValue' | 'totalMentionValue' | 'negativeMentionRate' | 'nowIndex'>(
  'experienceValue'
)
const sortOrder = ref<'asc' | 'desc'>('desc')
const activeKeyword = ref<string | null>(null)

const tableData = ref<any[]>([])

function formatPercent(val?: number) {
  if (val === undefined || val === null) return '-'
  return (Math.round(val * 100) / 100).toFixed(2)
}
function formatNum(val?: number) {
  if (val === undefined || val === null) return '-'
  return Number(val).toLocaleString()
}
function makeDataUnit(val?: number) {
  if (val === undefined || val === null) return '--'
  if (val >= 10000) return (val / 10000).toFixed(2) + '万'
  return val.toString()
}

function buildMomIndexMap(momData: any[]) {
  const map: Record<string, number> = {}
  momData.forEach((item, idx) => {
    map[item.keyWord] = idx + 1
  })
  return map
}

function processTableData() {
  const detail = [...props.data]
  const momDetail = props.momData ? [...props.momData] : []
  // 排序
  detail.sort((a, b) => {
    const field = sortField.value
    const order = sortOrder.value === 'asc' ? 1 : -1
    // @ts-ignore
    if ((a[field] ?? 0) > (b[field] ?? 0)) return order
    // @ts-ignore
    if ((a[field] ?? 0) < (b[field] ?? 0)) return -order
    return 0
  })
  // 生成排名和上期排名
  const momMap = buildMomIndexMap(momDetail)
  detail.forEach((item, idx) => {
    item.nowIndex = idx + 1
    item.preIndex = momMap[item.keyWord] ?? '--'
  })
  tableData.value = detail
}

watch(() => [props.data, props.momData, sortField.value, sortOrder.value], processTableData, {
  immediate: true,
  deep: true
})

function handleSortChange({ prop, order }: any) {
  sortField.value = prop
  sortOrder.value = order === 'ascending' ? 'asc' : 'desc'
}

function handleKeywordClick(keyword: string) {
  activeKeyword.value = keyword
}
</script>

<template>
  <el-table
    :data="tableData"
    @sort-change="handleSortChange"
    :loading="loading"
    stripe
    style="width: 100%"
  >
    <el-table-column label="排名/上期排名" width="120" align="left">
      <template #default="{ row }">
        <span>{{ row.nowIndex }}/</span>
        <span>{{ row.preIndex }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="keyWord" :label="attrName || '一级分类'" align="left">
      <template #default="{ row }">
        <span
          :style="{ color: activeKeyword === row.keyWord ? '#0077FF' : '' }"
          @click="handleKeywordClick(row.keyWord)"
          style="cursor: pointer"
          >{{ row.keyWord }}</span
        >
      </template>
    </el-table-column>
    <el-table-column prop="experienceValue" label="体验值" sortable="custom" align="left">
      <template #default="{ row }">{{ formatNum(row.experienceValue) }}</template>
    </el-table-column>
    <el-table-column
      prop="momExperienceValueRate"
      label="体验值环比"
      sortable="custom"
      align="left"
    >
      <template #default="{ row }">
        <ShowCompare
          compareKey="momExperienceValueRate"
          :compareValue="row.momExperienceValueRate"
        />
      </template>
    </el-table-column>
    <el-table-column prop="totalMentionValue" label="提及量" sortable="custom" align="center">
      <template #default="{ row }">{{ makeDataUnit(row.totalMentionValue) }}</template>
    </el-table-column>
    <el-table-column
      prop="momTotalMentionValueRate"
      label="提及量环比"
      sortable="custom"
      align="left"
    >
      <template #default="{ row }">
        <ShowCompare
          compareKey="momTotalMentionValueRate"
          :compareValue="row.momTotalMentionValueRate"
        />
      </template>
    </el-table-column>
    <el-table-column prop="negativeMentionRate" label="负面提及率" sortable="custom" align="left">
      <template #default="{ row }">{{ formatPercent(row.negativeMentionRate) }}%</template>
    </el-table-column>
    <el-table-column prop="momNegativeMentionRate" label="负面提及率环比" align="left">
      <template #default="{ row }">
        <ShowCompare
          compareKey="momNegativeMentionRate"
          :compareValue="row.momNegativeMentionRate"
        />
      </template>
    </el-table-column>
  </el-table>
</template>
