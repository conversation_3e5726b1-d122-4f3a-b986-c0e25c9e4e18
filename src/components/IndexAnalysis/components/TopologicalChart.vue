<script setup lang="ts">
import { ref, watch, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts/core'
import { TreeChart } from 'echarts/charts'
import { TooltipComponent, TitleComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import type { TopologicalData } from '../types.d'

echarts.use([TreeChart, TooltipComponent, TitleComponent, CanvasRenderer])

interface Props {
  data: TopologicalData[]
  highlightId?: string
  chartId?: string
  attr?: string
  attrName?: string
  maxChildren?: number
}
const props = defineProps<Props>()
const chartRef = ref<HTMLDivElement | null>(null)
let chartInstance: echarts.ECharts | null = null

function formatPercent(val?: number) {
  if (val === undefined || val === null) return '-'
  return (Math.round(val * 100) / 100).toFixed(2)
}

function formatNum(val?: number) {
  if (val === undefined || val === null) return '-'
  return Number(val).toLocaleString()
}

function makeDataUnit(val?: number) {
  if (val === undefined || val === null) return '-'
  if (val >= 10000) return (val / 10000).toFixed(2) + '万'
  return val.toString()
}

const getLabelWidth = () => {
  return props.maxChildren ? 700 / props.maxChildren : 120
}

const renderChart = () => {
  if (!chartRef.value) return
  nextTick(() => {
    if (!chartInstance) {
      chartInstance = echarts.init(chartRef.value)
    }
    const option = {
      tooltip: {
        trigger: 'item',
        triggerOn: 'mousemove',
        formatter: (value: any) => {
          const data = value.data
          let attrData =
            props.attr === 'experienceValue'
              ? formatNum(data.experienceValue)
              : formatPercent(data.negativeMentionRate) + '%'
          let str = `<div>${value.marker + data.name}</div>`
          str += `<div>${props.attrName || ''}：${attrData}</div>`
          str += `<div>提及量：${makeDataUnit(data.totalMentionValue)}</div>`
          return str
        }
      },
      series: [
        {
          type: 'tree',
          edgeShape: 'curve',
          initialTreeDepth: -1,
          data: props.data,
          left: '1%',
          right: '2%',
          top: '8%',
          bottom: '10%',
          symbolSize: 12,
          symbol: 'emptyCircle',
          orient: 'vertical',
          itemStyle: {
            color: '#0077FF'
          },
          lineStyle: {
            color: '#C2CDD6',
            width: 0.8,
            curveness: 0.8
          },
          label: {
            position: 'right',
            verticalAlign: 'middle',
            fontWeight: 500,
            color: 'rgba(0, 0, 0, 0.75)',
            width: getLabelWidth(),
            overflow: 'breakAll',
            lineHeight: 18,
            formatter: (value: any) => {
              let data = value.data || {}
              let name = data.name || ''
              if (name.length > 6) name = name.slice(0, 5) + '...'
              return `{title|${name}}`
            },
            rich: {
              title: {
                fontSize: 14,
                overflow: 'truncate',
                align: 'center',
                fontWeight: 600,
                color: 'rgba(0, 0, 0, 0.75)'
              },
              desc: {
                fontSize: 13,
                fontWeight: 400,
                overflow: 'truncate',
                color: '#5F7483'
              }
            }
          },
          animationDurationUpdate: 750
        }
      ]
    }
    chartInstance.setOption(option)
  })
}

watch(
  () => [props.data, props.highlightId, props.attr, props.attrName, props.maxChildren],
  renderChart,
  { deep: true }
)
onMounted(renderChart)
</script>

<template>
  <div
    :id="props.chartId || 'topological-chart'"
    ref="chartRef"
    style="width: 100%; height: 450px"
  ></div>
</template>
