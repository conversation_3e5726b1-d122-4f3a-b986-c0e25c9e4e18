export interface IndexAnalysisData {
  detail: Array<{
    indexId: string
    keyWord: string
    totalMentionValue: number
    experienceValue?: number
    negativeMentionRate?: number
    momTotalMentionValueRate: number
    momExperienceValueRate?: number
    momNegativeMentionRate?: number
    nowIndex: number
    momIndex: number | string
    preIndex?: number | string
  }>
  detailMom: Array<any>
}

export interface TopologicalData {
  name: string
  value: number
  indexId: string
  children?: TopologicalData[]
  itemStyle?: {
    color: string
  }
}
