# IndexAnalysis 指标分析组件

## 组件用途

- 展示指标的层级拓扑关系（树形图）
- 展示详细的指标排名、环比等数据（表格）
- 支持排序、下载、点击高亮等交互

## Props

- `chartId?: string` 图表ID
- `loading?: boolean` 加载状态
- `data: IndexAnalysisData` 表格数据
- `remarkData: TopologicalData[]` 拓扑图数据
- `remarkData2?: string` 关键词列标题
- `remarkData3?: string` 高亮节点ID
- `attr?: string` 显示属性（experienceValue/negativeMentionRate）
- `attrName?: string` 属性名称

## 依赖

- ECharts（树形图）
- Element Plus（表格、按钮）
- ShowCompare（环比显示）

## 目录结构

```
IndexAnalysis/
├── index.vue          # 主组件
├── types.d.ts         # 类型定义
├── README.md          # 组件说明
└── components/
    ├── TopologicalChart.vue   # 拓扑图子组件
    └── IndexTable.vue         # 表格子组件
```

## 用法示例

```vue
<IndexAnalysis :data="data" :remarkData="remarkData" />
```
