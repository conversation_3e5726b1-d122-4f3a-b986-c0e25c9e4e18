import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import FPieChart from './index.vue'
import type { PieDataItem } from './types.d'

// Mock FEcharts 组件
vi.mock('@/components/FEcharts/index.vue', () => ({
  default: {
    name: 'FEcharts',
    template:
      '<div class="mock-echarts" :data-options="JSON.stringify(options)" :style="{ width, height }"></div>',
    props: ['options', 'width', 'height', 'theme'],
    emits: ['chart-click', 'chart-ready'],
    setup(props: any, { emit }: any) {
      // 模拟图表准备就绪
      setTimeout(() => {
        emit('chart-ready', { mockChart: true })
      }, 0)

      return {
        handleClick: () => emit('chart-click', { mockEvent: true })
      }
    }
  }
}))

describe('FPieChart', () => {
  const mockData: PieDataItem[] = [
    { name: '直接访问', value: 335 },
    { name: '邮件营销', value: 310 },
    { name: '联盟广告', value: 234 },
    { name: '视频广告', value: 135 },
    { name: '搜索引擎', value: 1548 }
  ]

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('组件渲染', () => {
    it('应该正确渲染饼图组件', () => {
      const wrapper = mount(FPieChart, {
        props: {
          data: mockData
        }
      })

      const echarts = wrapper.findComponent({ name: 'FEcharts' })
      expect(echarts.exists()).toBe(true)
    })

    it('应该传递正确的宽高属性', () => {
      const wrapper = mount(FPieChart, {
        props: {
          data: mockData,
          width: '500px',
          height: '400px'
        }
      })

      const echarts = wrapper.findComponent({ name: 'FEcharts' })
      expect(echarts.props('width')).toBe('500px')
      expect(echarts.props('height')).toBe('400px')
    })

    it('应该使用默认的宽高值', () => {
      const wrapper = mount(FPieChart, {
        props: {
          data: mockData
        }
      })

      const echarts = wrapper.findComponent({ name: 'FEcharts' })
      expect(echarts.props('width')).toBe('100%')
      expect(echarts.props('height')).toBe('100%')
    })

    it('应该传递主题属性', () => {
      const wrapper = mount(FPieChart, {
        props: {
          data: mockData,
          theme: 'dark'
        }
      })

      const echarts = wrapper.findComponent({ name: 'FEcharts' })
      expect(echarts.props('theme')).toBe('dark')
    })
  })

  describe('数据处理', () => {
    it('应该正确处理饼图数据', () => {
      const wrapper = mount(FPieChart, {
        props: {
          data: mockData
        }
      })

      const echarts = wrapper.findComponent({ name: 'FEcharts' })
      const options = echarts.props('options')

      expect(options.series).toHaveLength(1)
      expect(options.series[0].type).toBe('pie')
      expect(options.series[0].data).toEqual(mockData)
    })

    it('应该正确设置饼图半径', () => {
      const wrapper = mount(FPieChart, {
        props: {
          data: mockData,
          radius: ['50%', '80%']
        }
      })

      const echarts = wrapper.findComponent({ name: 'FEcharts' })
      const options = echarts.props('options')

      expect(options.series[0].radius).toEqual(['50%', '80%'])
    })

    it('应该正确设置饼图中心位置', () => {
      const wrapper = mount(FPieChart, {
        props: {
          data: mockData,
          center: ['60%', '40%']
        }
      })

      const echarts = wrapper.findComponent({ name: 'FEcharts' })
      const options = echarts.props('options')

      expect(options.series[0].center).toEqual(['60%', '40%'])
    })

    it('应该处理空数据', () => {
      const wrapper = mount(FPieChart, {
        props: {
          data: []
        }
      })

      const echarts = wrapper.findComponent({ name: 'FEcharts' })
      const options = echarts.props('options')

      expect(options.series[0].data).toEqual([])
    })
  })

  describe('标签配置', () => {
    it('应该根据 showLabel 属性显示或隐藏标签', () => {
      // 显示标签
      const wrapperWithLabel = mount(FPieChart, {
        props: {
          data: mockData,
          showLabel: true
        }
      })

      let echarts = wrapperWithLabel.findComponent({ name: 'FEcharts' })
      let options = echarts.props('options')
      expect(options.series[0].label.show).toBe(true)

      // 隐藏标签
      const wrapperWithoutLabel = mount(FPieChart, {
        props: {
          data: mockData,
          showLabel: false
        }
      })

      echarts = wrapperWithoutLabel.findComponent({ name: 'FEcharts' })
      options = echarts.props('options')
      expect(options.series[0].label.show).toBe(false)
    })

    it('应该正确设置标签位置', () => {
      // 外部标签
      const wrapperOutside = mount(FPieChart, {
        props: {
          data: mockData,
          labelPosition: 'outside'
        }
      })

      let echarts = wrapperOutside.findComponent({ name: 'FEcharts' })
      let options = echarts.props('options')
      expect(options.series[0].label.position).toBe('outside')

      // 内部标签
      const wrapperInside = mount(FPieChart, {
        props: {
          data: mockData,
          labelPosition: 'inside'
        }
      })

      echarts = wrapperInside.findComponent({ name: 'FEcharts' })
      options = echarts.props('options')
      expect(options.series[0].label.position).toBe('inside')
    })

    it('应该根据 showPercentage 属性格式化标签', () => {
      // 显示百分比
      const wrapperWithPercentage = mount(FPieChart, {
        props: {
          data: mockData,
          showPercentage: true
        }
      })

      let echarts = wrapperWithPercentage.findComponent({ name: 'FEcharts' })
      let options = echarts.props('options')
      expect(typeof options.series[0].label.formatter).toBe('function')

      // 不显示百分比
      const wrapperWithoutPercentage = mount(FPieChart, {
        props: {
          data: mockData,
          showPercentage: false
        }
      })

      echarts = wrapperWithoutPercentage.findComponent({ name: 'FEcharts' })
      options = echarts.props('options')
      expect(options.series[0].label.formatter).toBe('{b}')
    })
  })

  describe('工具提示配置', () => {
    it('应该有默认的工具提示配置', () => {
      const wrapper = mount(FPieChart, {
        props: {
          data: mockData
        }
      })

      const echarts = wrapper.findComponent({ name: 'FEcharts' })
      const options = echarts.props('options')

      expect(options.tooltip).toBeDefined()
      expect(options.tooltip.trigger).toBe('item')
    })

    it('应该支持自定义工具提示格式化器', () => {
      const customFormatter = vi.fn((params: any) => `自定义: ${params.name} - ${params.value}`)

      const wrapper = mount(FPieChart, {
        props: {
          data: mockData,
          tooltipFormatter: customFormatter
        }
      })

      const echarts = wrapper.findComponent({ name: 'FEcharts' })
      const options = echarts.props('options')

      expect(typeof options.tooltip.formatter).toBe('function')
    })
  })

  describe('字段映射', () => {
    it('应该支持自定义字段映射', () => {
      const customData = [
        { label: '类型A', count: 100 },
        { label: '类型B', count: 200 }
      ]

      const fieldMapping = {
        nameField: 'label',
        valueField: 'count'
      }

      const wrapper = mount(FPieChart, {
        props: {
          data: customData as any,
          fieldMapping
        }
      })

      const echarts = wrapper.findComponent({ name: 'FEcharts' })
      const options = echarts.props('options')

      // 数据应该被转换为标准格式
      expect(options.series[0].data).toEqual([
        { name: '类型A', value: 100 },
        { name: '类型B', value: 200 }
      ])
    })
  })

  describe('事件处理', () => {
    it('应该正确转发图表点击事件', async () => {
      const wrapper = mount(FPieChart, {
        props: {
          data: mockData
        }
      })

      const echarts = wrapper.findComponent({ name: 'FEcharts' })

      // 触发图表点击事件
      await echarts.vm.$emit('chart-click', { mockEvent: true })

      expect(wrapper.emitted('chartClick')).toBeTruthy()
      expect(wrapper.emitted('chartClick')![0]).toEqual([{ mockEvent: true }])
    })

    it('应该正确转发图表准备就绪事件', async () => {
      const wrapper = mount(FPieChart, {
        props: {
          data: mockData
        }
      })

      // 等待组件挂载和事件触发
      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 10))

      expect(wrapper.emitted('chartReady')).toBeTruthy()
      expect(wrapper.emitted('chartReady')![0]).toEqual([{ mockChart: true }])
    })
  })

  describe('响应式更新', () => {
    it('应该在数据变化时更新图表', async () => {
      const wrapper = mount(FPieChart, {
        props: {
          data: mockData
        }
      })

      const newData = [
        { name: '新数据1', value: 100 },
        { name: '新数据2', value: 200 }
      ]

      await wrapper.setProps({ data: newData })

      const echarts = wrapper.findComponent({ name: 'FEcharts' })
      const options = echarts.props('options')

      expect(options.series[0].data).toEqual(newData)
    })

    it('应该在配置变化时更新图表选项', async () => {
      const wrapper = mount(FPieChart, {
        props: {
          data: mockData,
          radius: ['40%', '70%']
        }
      })

      await wrapper.setProps({ radius: ['30%', '60%'] })

      const echarts = wrapper.findComponent({ name: 'FEcharts' })
      const options = echarts.props('options')

      expect(options.series[0].radius).toEqual(['30%', '60%'])
    })
  })

  describe('边界情况', () => {
    it('应该处理包含零值的数据', () => {
      const dataWithZero = [
        { name: '类型A', value: 0 },
        { name: '类型B', value: 100 },
        { name: '类型C', value: 0 }
      ]

      const wrapper = mount(FPieChart, {
        props: {
          data: dataWithZero
        }
      })

      const echarts = wrapper.findComponent({ name: 'FEcharts' })
      const options = echarts.props('options')

      expect(options.series[0].data).toEqual(dataWithZero)
    })

    it('应该处理包含负值的数据', () => {
      const dataWithNegative = [
        { name: '类型A', value: -10 },
        { name: '类型B', value: 100 },
        { name: '类型C', value: 50 }
      ]

      const wrapper = mount(FPieChart, {
        props: {
          data: dataWithNegative
        }
      })

      const echarts = wrapper.findComponent({ name: 'FEcharts' })
      const options = echarts.props('options')

      expect(options.series[0].data).toEqual(dataWithNegative)
    })

    it('应该处理包含特殊字符的名称', () => {
      const dataWithSpecialChars = [
        { name: '类型 & 特殊', value: 100 },
        { name: '类型<test>', value: 200 },
        { name: '类型"引号"', value: 150 }
      ]

      const wrapper = mount(FPieChart, {
        props: {
          data: dataWithSpecialChars
        }
      })

      const echarts = wrapper.findComponent({ name: 'FEcharts' })
      const options = echarts.props('options')

      expect(options.series[0].data).toEqual(dataWithSpecialChars)
    })
  })
})
