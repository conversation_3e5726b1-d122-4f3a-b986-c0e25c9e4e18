import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import ButtonGroup from './index.vue'

describe('ButtonGroup', () => {
  const mockOptions = [
    { label: '选项1', value: 'option1' },
    { label: '选项2', value: 'option2' },
    { label: '选项3', value: 'option3' }
  ]

  describe('组件渲染', () => {
    it('应该正确渲染按钮组', () => {
      const wrapper = mount(ButtonGroup, {
        props: {
          options: mockOptions,
          modelValue: 'option1'
        }
      })

      const buttons = wrapper.findAll('.button')
      expect(buttons).toHaveLength(3)
      expect(buttons[0].text()).toBe('选项1')
      expect(buttons[1].text()).toBe('选项2')
      expect(buttons[2].text()).toBe('选项3')
    })

    it('应该正确显示选中状态', () => {
      const wrapper = mount(ButtonGroup, {
        props: {
          options: mockOptions,
          modelValue: 'option2'
        }
      })

      const buttons = wrapper.findAll('.button')
      expect(buttons[0].classes()).not.toContain('selected')
      expect(buttons[1].classes()).toContain('selected')
      expect(buttons[2].classes()).not.toContain('selected')
    })

    it('应该在没有选中值时显示正确状态', () => {
      const wrapper = mount(ButtonGroup, {
        props: {
          options: mockOptions,
          modelValue: ''
        }
      })

      const buttons = wrapper.findAll('.button')
      buttons.forEach(button => {
        expect(button.classes()).not.toContain('selected')
      })
    })

    it('应该处理空选项列表', () => {
      const wrapper = mount(ButtonGroup, {
        props: {
          options: [],
          modelValue: ''
        }
      })

      const buttons = wrapper.findAll('.button')
      expect(buttons).toHaveLength(0)
    })
  })

  describe('交互行为', () => {
    it('应该在点击按钮时触发事件', async () => {
      const wrapper = mount(ButtonGroup, {
        props: {
          options: mockOptions,
          modelValue: 'option1'
        }
      })

      const buttons = wrapper.findAll('.button')
      await buttons[1].trigger('click')

      // 检查是否触发了 update:modelValue 事件
      expect(wrapper.emitted('update:modelValue')).toBeTruthy()
      expect(wrapper.emitted('update:modelValue')![0]).toEqual(['option2'])

      // 检查是否触发了 change 事件
      expect(wrapper.emitted('change')).toBeTruthy()
      expect(wrapper.emitted('change')![0]).toEqual(['option2'])
    })

    it('应该在点击已选中按钮时不触发事件', async () => {
      const wrapper = mount(ButtonGroup, {
        props: {
          options: mockOptions,
          modelValue: 'option1'
        }
      })

      const buttons = wrapper.findAll('.button')
      await buttons[0].trigger('click') // 点击已选中的按钮

      // 不应该触发事件
      expect(wrapper.emitted('update:modelValue')).toBeFalsy()
      expect(wrapper.emitted('change')).toBeFalsy()
    })

    it('应该正确处理多次点击', async () => {
      const wrapper = mount(ButtonGroup, {
        props: {
          options: mockOptions,
          modelValue: 'option1'
        }
      })

      const buttons = wrapper.findAll('.button')

      // 第一次点击
      await buttons[1].trigger('click')
      expect(wrapper.emitted('update:modelValue')![0]).toEqual(['option2'])

      // 更新 props 模拟父组件状态更新
      await wrapper.setProps({ modelValue: 'option2' })

      // 第二次点击
      await buttons[2].trigger('click')
      expect(wrapper.emitted('update:modelValue')![1]).toEqual(['option3'])
    })
  })

  describe('Props 验证', () => {
    it('应该接受正确的 options 格式', () => {
      const wrapper = mount(ButtonGroup, {
        props: {
          options: [
            { label: '测试标签', value: 'test_value' },
            { label: '另一个标签', value: 'another_value' }
          ],
          modelValue: 'test_value'
        }
      })

      const buttons = wrapper.findAll('.button')
      expect(buttons[0].text()).toBe('测试标签')
      expect(buttons[1].text()).toBe('另一个标签')
      expect(buttons[0].classes()).toContain('selected')
    })

    it('应该处理包含特殊字符的标签', () => {
      const specialOptions = [
        { label: '选项 & 特殊字符', value: 'special1' },
        { label: '选项<>符号', value: 'special2' },
        { label: '选项"引号"', value: 'special3' }
      ]

      const wrapper = mount(ButtonGroup, {
        props: {
          options: specialOptions,
          modelValue: 'special1'
        }
      })

      const buttons = wrapper.findAll('.button')
      expect(buttons[0].text()).toBe('选项 & 特殊字符')
      expect(buttons[1].text()).toBe('选项<>符号')
      expect(buttons[2].text()).toBe('选项"引号"')
    })

    it('应该处理长标签文本', () => {
      const longOptions = [
        { label: '这是一个非常长的按钮标签文本用来测试组件的显示效果', value: 'long1' },
        { label: '短标签', value: 'short1' }
      ]

      const wrapper = mount(ButtonGroup, {
        props: {
          options: longOptions,
          modelValue: 'long1'
        }
      })

      const buttons = wrapper.findAll('.button')
      expect(buttons[0].text()).toBe('这是一个非常长的按钮标签文本用来测试组件的显示效果')
      expect(buttons[1].text()).toBe('短标签')
    })
  })

  describe('响应式更新', () => {
    it('应该在 modelValue 变化时更新选中状态', async () => {
      const wrapper = mount(ButtonGroup, {
        props: {
          options: mockOptions,
          modelValue: 'option1'
        }
      })

      let buttons = wrapper.findAll('.button')
      expect(buttons[0].classes()).toContain('selected')
      expect(buttons[1].classes()).not.toContain('selected')

      // 更新 modelValue
      await wrapper.setProps({ modelValue: 'option2' })

      buttons = wrapper.findAll('.button')
      expect(buttons[0].classes()).not.toContain('selected')
      expect(buttons[1].classes()).toContain('selected')
    })

    it('应该在 options 变化时重新渲染', async () => {
      const wrapper = mount(ButtonGroup, {
        props: {
          options: mockOptions,
          modelValue: 'option1'
        }
      })

      expect(wrapper.findAll('.button')).toHaveLength(3)

      // 更新 options
      const newOptions = [
        { label: '新选项1', value: 'new1' },
        { label: '新选项2', value: 'new2' }
      ]
      await wrapper.setProps({
        options: newOptions,
        modelValue: 'new1'
      })

      const buttons = wrapper.findAll('.button')
      expect(buttons).toHaveLength(2)
      expect(buttons[0].text()).toBe('新选项1')
      expect(buttons[1].text()).toBe('新选项2')
      expect(buttons[0].classes()).toContain('selected')
    })
  })

  describe('边界情况', () => {
    it('应该处理 modelValue 不在 options 中的情况', () => {
      const wrapper = mount(ButtonGroup, {
        props: {
          options: mockOptions,
          modelValue: 'nonexistent'
        }
      })

      const buttons = wrapper.findAll('.button')
      buttons.forEach(button => {
        expect(button.classes()).not.toContain('selected')
      })
    })

    it('应该处理重复的 value 值', () => {
      const duplicateOptions = [
        { label: '选项1', value: 'same' },
        { label: '选项2', value: 'same' },
        { label: '选项3', value: 'different' }
      ]

      const wrapper = mount(ButtonGroup, {
        props: {
          options: duplicateOptions,
          modelValue: 'same'
        }
      })

      const buttons = wrapper.findAll('.button')
      // 由于 Vue 的 v-for key，应该渲染3个按钮
      expect(buttons).toHaveLength(3)
      // 由于 value 相同，前两个按钮都应该被选中
      expect(buttons[0].classes()).toContain('selected')
      expect(buttons[1].classes()).toContain('selected')
      expect(buttons[2].classes()).not.toContain('selected')
    })

    it('应该正确处理快速连续点击', async () => {
      const wrapper = mount(ButtonGroup, {
        props: {
          options: mockOptions,
          modelValue: 'option1'
        }
      })

      const button = wrapper.findAll('.button')[1]

      // 快速连续点击
      await button.trigger('click')
      await button.trigger('click')
      await button.trigger('click')

      // 应该只触发一次事件（因为值没有变化）
      expect(wrapper.emitted('update:modelValue')).toHaveLength(1)
      expect(wrapper.emitted('change')).toHaveLength(1)
    })
  })
})
