<template>
  <FEcharts
    :options="chartOptions"
    :width="width"
    :height="height"
    :theme="theme"
    :auto-resize="true"
    :auto-update="true"
    @chart-ready="handleChartReady"
    @chart-click="handleChartClick"
  />
</template>

<script setup lang="ts">
/**
 * FMapChart 地图图表组件
 * @description 基于 FEcharts 的中国地图数据可视化组件，支持自定义数据源、字段映射、tooltips 等功能
 * <AUTHOR> Team
 * @since 1.0.0
 */

import { computed, onMounted } from 'vue'
import * as echarts from 'echarts'
import type { EChartsOption } from 'echarts'
import FEcharts from '@/components/FEcharts/index.vue'
import china from '@/constants/china.json'
// import { getExperienceIndexMapTooltip } from '@/utils/echartsTooltipConfig'
import type {
  VisualMapConfig,
  GeoConfig,
  TooltipFormatter,
  FMapChartProps,
  FMapChartEmits
} from './types'

// 定义 Props
const props = withDefaults(defineProps<FMapChartProps>(), {
  data: () => [],
  width: '100%',
  height: '100%',
  theme: '',
  zoom: 1.1,
  roam: false,
  showVisualMap: true,
  fieldMapping: () => ({
    nameField: 'name',
    valueField: 'value'
  })
})

// 定义事件
const emit = defineEmits<FMapChartEmits>()

// 注册中国地图数据
onMounted(() => {
  echarts.registerMap('china', china as any)
  console.log('data', props.data)
})

// 默认视觉映射配置（体验指数专用）
const defaultVisualMapConfig: VisualMapConfig = {
  type: 'continuous',
  min: -100,
  max: 100,
  left: 'left',
  itemHeight: 100,
  itemWidth: 10,
  text: ['', '体验指数'],
  calculable: true,
  align: 'right',
  orient: 'horizontal',
  inRange: {
    color: ['#C4E8FE', '#2B4664']
  },
  show: true
}

// 默认地理配置（体验指数专用）
const defaultGeoConfig: GeoConfig = {
  map: 'china',
  roam: false,
  zoom: 1.1,
  top: 80,
  left: 0,
  right: 0,
  width: '90%',
  height: '95%',
  label: { show: false },
  itemStyle: {
    borderColor: '#fff',
    areaColor: '#d6dfea'
  },
  emphasis: {
    label: { show: false },
    itemStyle: {
      areaColor: '#00B0F0',
      borderWidth: 0
    }
  },
  select: {
    label: {
      show: false
    },
    itemStyle: {
      areaColor: '#00B0F0',
      shadowOffsetX: 0,
      shadowOffsetY: 0,
      borderWidth: 0
    }
  }
}

// 默认 Tooltip 格式化函数（体验指数专用）
// const defaultTooltipFormatter: TooltipFormatter = getExperienceIndexMapTooltip().formatter

// 计算合并后的配置
const mergedVisualMapConfig = computed(() => ({
  ...defaultVisualMapConfig,
  ...props.visualMapConfig
}))

const mergedGeoConfig = computed(() => ({
  ...defaultGeoConfig,
  ...props.geoConfig,
  roam: props.roam,
  zoom: props.zoom
}))

// 处理地图数据
const processedMapData = computed(() => {
  const { nameField, valueField } = props.fieldMapping

  return props.data.map(item => {
    const result = { ...item }
    result.name = item[nameField]
    result.value = item[valueField]
    return result
  })
})

// 生成图表配置选项
const chartOptions = computed<EChartsOption>(() => {
  const visualMapConfig = mergedVisualMapConfig.value
  const geoConfig = mergedGeoConfig.value
  const tooltipFormatter = props.tooltipFormatter
  // || defaultTooltipFormatter

  return {
    tooltip: {
      show: true,
      trigger: 'item',
      backgroundColor: '#fff',
      borderWidth: 0,
      borderRadius: 4,
      extraCssText: 'box-shadow: 0 2px 12px 0 rgba(0,0,0,0.08)',
      textStyle: {
        color: '#606266',
        fontSize: 14
      },
      formatter: tooltipFormatter
    },
    visualMap: props.showVisualMap
      ? {
          ...visualMapConfig,
          show: visualMapConfig.show
        }
      : {
          show: false
        },
    geo: geoConfig as any,
    series: [
      {
        type: 'map',
        map: 'china',
        geoIndex: 0,
        data: processedMapData.value,
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1
        },
        emphasis: {
          itemStyle: {
            areaColor: '#00B0F0'
          }
        },
        select: {
          itemStyle: {
            areaColor: '#00B0F0'
          }
        }
      }
    ]
  }
})

// 事件处理
const handleChartReady = (chart: any) => {
  emit('chart-ready', chart)
}

const handleChartClick = (params: any) => {
  emit('chart-click', params)
}
</script>
