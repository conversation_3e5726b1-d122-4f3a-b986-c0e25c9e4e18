# FMapChart 地图图表组件

基于 FEcharts 的中国地图数据可视化组件，支持自定义数据源、字段映射、tooltips 等功能。

## 快速开始

### 基础用法

```vue
<template>
  <FMapChart :data="mapData" width="400px" height="300px" />
</template>

<script setup>
import FMapChart from '@/components/FMapChart'

const mapData = [
  { name: '北京', value: 85, mom: 5.2, yoy: -2.1 },
  { name: '上海', value: 92, mom: -1.5, yoy: 3.8 },
  { name: '广东', value: 78, mom: 2.3, yoy: 1.2 }
]
</script>
```

### 自定义字段映射

```vue
<template>
  <FMapChart
    :data="mapData"
    :field-mapping="{ nameField: 'province', valueField: 'experienceIndex' }"
    width="400px"
    height="300px"
  />
</template>

<script setup>
const mapData = [
  { province: '北京', experienceIndex: 85 },
  { province: '上海', experienceIndex: 92 }
]
</script>
```

### 自定义配置

```vue
<template>
  <FMapChart
    :data="mapData"
    :visual-map-config="visualMapConfig"
    :tooltip-formatter="tooltipFormatter"
    width="400px"
    height="300px"
  />
</template>

<script setup>
const visualMapConfig = {
  type: 'continuous',
  min: 0,
  max: 100,
  text: ['高', '低'],
  inRange: {
    color: ['#50a3ba', '#eac736', '#d94e5d']
  }
}

const tooltipFormatter = params => {
  return `<div>${params.name}: ${params.value}</div>`
}
</script>
```

## Props

| 属性名           | 类型               | 默认值                                       | 说明                      |
| ---------------- | ------------------ | -------------------------------------------- | ------------------------- |
| data             | `MapDataItem[]`    | `[]`                                         | 地图数据源                |
| width            | `string`           | `'100%'`                                     | 组件宽度                  |
| height           | `string`           | `'100%'`                                     | 组件高度                  |
| theme            | `string`           | `''`                                         | 图表主题                  |
| zoom             | `number`           | `1.1`                                        | 地图缩放比例              |
| roam             | `boolean`          | `false`                                      | 是否开启地图漫游          |
| showVisualMap    | `boolean`          | `true`                                       | 是否显示视觉映射组件      |
| visualMapConfig  | `VisualMapConfig`  | 体验指数默认配置                             | 视觉映射配置              |
| geoConfig        | `GeoConfig`        | 体验指数默认配置                             | 地理坐标系配置            |
| tooltipFormatter | `TooltipFormatter` | 体验指数格式化                               | 自定义 tooltip 格式化函数 |
| fieldMapping     | `MapFieldMapping`  | `{ nameField: 'name', valueField: 'value' }` | 字段映射配置              |

## Events

| 事件名      | 参数          | 说明               |
| ----------- | ------------- | ------------------ |
| chart-click | `params: any` | 地图区域点击事件   |
| chart-ready | `chart: any`  | 图表初始化完成事件 |

## 数据格式

```typescript
interface MapDataItem {
  name: string // 省份/地区名称
  value: number // 数值
  mom?: number // 环比变化
  yoy?: number // 同比变化
  [key: string]: any // 其他自定义字段
}
```

## 依赖关系

- 基于 `FEcharts` 组件实现，继承其所有功能特性
- 自动注册中国地图数据
- 利用 FEcharts 的自动调整大小、事件处理等功能

## 注意事项

1. 地图数据中的 `name` 字段必须与中国地图的省份名称保持一致
2. 默认配置适用于体验指数数据，其他业务场景建议自定义配置
3. 组件基于 FEcharts 实现，享有其性能优化和完整的生命周期管理
4. 支持响应式数据更新，配置变化时自动重新渲染

## 优势特性

- **继承 FEcharts 功能**：自动调整大小、主题切换、事件处理等
- **简化配置**：专注于地图特有的配置，复用 FEcharts 的通用功能
- **高性能**：利用 FEcharts 的性能优化策略
- **一致性**：与项目中其他图表组件保持一致的 API 设计

## 示例

查看 `demo.vue` 文件获取完整的使用示例。
