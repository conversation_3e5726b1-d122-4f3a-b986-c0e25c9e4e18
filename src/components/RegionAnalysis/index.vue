<template>
  <div class="region-analysis">
    <div class="chart-header">
      <h3 class="chart-title">区域对比</h3>
    </div>

    <div class="chart-content" v-loading="loading">
      <!-- 控制区域 -->
      <div class="control-area">
        <div class="control-right">
          <el-select v-model="selectPageNum" placeholder="请选择显示条数" class="control-select">
            <el-option
              v-for="option in pageNumOptions"
              :key="option.key"
              :label="option.label"
              :value="option.key"
            />
          </el-select>
          <el-select v-model="selectSortType" placeholder="请选择排序方式" class="control-select">
            <el-option
              v-for="option in sortTypeOptions"
              :key="option.key"
              :label="option.label"
              :value="option.key"
            />
          </el-select>
        </div>
      </div>

      <!-- 表格区域 -->
      <div class="table-area">
        <el-table
          :data="tableData"
          style="width: 100%"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          stripe
        >
          <el-table-column
            align="center"
            type="index"
            label="排名"
            width="80"
            :index="(index: number) => index + 1"
          />
          <el-table-column align="center" prop="province" label="区域" min-width="120">
            <template #default="{ row }">
              <span class="region-link" @click="handleSeeAreaDetail(row.province)">
                {{ row.province }}
              </span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="experienceValue" label="体验值" min-width="100">
            <template #default="{ row }">
              {{ formatNumber(row.experienceValue) }}
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            prop="momExperienceValueRate"
            label="体验值环比"
            min-width="120"
          >
            <template #default="{ row }">
              <ShowCompare
                compare-key="momExperienceValueRate"
                :compare-value="row.momExperienceValueRate"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" prop="totalMentionValue" label="提及量" min-width="100">
            <template #default="{ row }">
              {{ formatDataUnit(row.totalMentionValue) }}
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            prop="momTotalMentionValueRate"
            label="提及量环比"
            min-width="120"
          >
            <template #default="{ row }">
              <ShowCompare
                compare-key="momTotalMentionValueRate"
                :compare-value="row.momTotalMentionValueRate"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import ShowCompare from '@/components/ShowCompare/index.vue'
import { Thousandth, toFixTwo } from '@/utils'
import type {
  RegionAnalysisProps,
  RegionAnalysisEmits,
  RegionData,
  SortOption,
  PageOption
} from './types'

// 组件名称定义
defineOptions({
  name: 'RegionAnalysis'
})

// Props 定义
const props = withDefaults(defineProps<RegionAnalysisProps>(), {
  chartId: '',
  loading: false,
  data: () => []
})

// Emits 定义
const emit = defineEmits<RegionAnalysisEmits>()

// 响应式数据
const selectPageNum = ref<number>(10)
const selectSortType = ref<string>('none')

// 排序选项
const sortTypeOptions: SortOption[] = [
  { key: 'none', label: '原始顺序' },
  { key: 'experienceValue', label: '体验值' },
  { key: 'momExperienceValueRate', label: '体验值环比' },
  { key: 'totalMentionValue', label: '提及量' },
  { key: 'momTotalMentionValueRate', label: '提及量环比' }
]

// 分页选项
const pageNumOptions: PageOption[] = [
  { key: 10, label: '前10条' },
  { key: 20, label: '前20条' },
  { key: 50, label: '前50条' }
]

// 计算属性：表格数据
const tableData = computed(() => {
  let resultData = [...props.data]

  // 只有在非"原始顺序"时才进行排序
  if (selectSortType.value !== 'none') {
    resultData = resultData.sort((a, b) => {
      const aValue = a[selectSortType.value as keyof RegionData] as number
      const bValue = b[selectSortType.value as keyof RegionData] as number
      return bValue - aValue
    })
  }

  return resultData.slice(0, selectPageNum.value)
})

/**
 * 格式化数字显示
 * @param value 数值
 * @returns 格式化后的字符串
 */
const formatNumber = (value: number): string => {
  if (isNaN(value)) return '-'
  return toFixTwo(value)
}

/**
 * 格式化数据单位显示
 * @param value 数值
 * @returns 格式化后的字符串
 */
const formatDataUnit = (value: number): string => {
  if (isNaN(value)) return '-'

  if (value >= 10000) {
    return `${toFixTwo(value / 10000)}万`
  } else if (value >= 1000) {
    return `${toFixTwo(value / 1000)}千`
  } else {
    return Thousandth(value)
  }
}

/**
 * 处理下载事件
 * @param type 下载类型
 */
const handleDownload = (type: string) => {
  emit('download', type, 'regionAnalysis')
  ElMessage.success(`正在导出${type.toUpperCase()}文件...`)
}

/**
 * 处理查看区域详情
 * @param province 省份名称
 */
const handleSeeAreaDetail = (province: string) => {
  emit('seeAreaDetail', province)
  ElMessage.info(`查看${province}详情`)
}
</script>

<style lang="scss" scoped>
.region-analysis {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
    background: #fafafa;

    .chart-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .chart-actions {
      display: flex;
      gap: 8px;
    }
  }

  .chart-content {
    padding: 20px;

    .control-area {
      margin-bottom: 16px;

      .control-right {
        display: flex;
        justify-content: flex-end;
        gap: 12px;

        .control-select {
          width: 140px;
        }
      }
    }

    .table-area {
      .region-link {
        color: #409eff;
        cursor: pointer;
        font-weight: 500;
        transition: color 0.2s;

        &:hover {
          color: #66b1ff;
          text-decoration: underline;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .region-analysis {
    .chart-header {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;

      .chart-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }

    .chart-content {
      padding: 16px;

      .control-area {
        .control-right {
          flex-direction: column;
          align-items: stretch;

          .control-select {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
