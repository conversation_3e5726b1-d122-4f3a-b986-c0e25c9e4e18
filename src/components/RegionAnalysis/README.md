# RegionAnalysis 地域分析组件

## 组件介绍

地域分析组件用于展示各省份/地区的VOC数据对比，支持排序、分页和数据导出功能。

## 功能特性

- ✅ **区域对比表格** - 展示各省份/地区的VOC数据对比
- ✅ **排序功能** - 支持按体验值、体验值环比、提及量、提及量环比排序
- ✅ **分页控制** - 支持选择显示前N条数据（10/20/50条）
- ✅ **数据展示** - 包含排名、区域、体验值、体验值环比、提及量、提及量环比
- ✅ **交互功能** - 点击区域名称可查看详情，支持数据下载
- ✅ **响应式设计** - 适配不同屏幕尺寸

## 组件接口

### Props

| 参数    | 类型         | 默认值 | 说明     |
| ------- | ------------ | ------ | -------- |
| chartId | string       | ''     | 图表ID   |
| loading | boolean      | false  | 加载状态 |
| data    | RegionData[] | []     | 地域数据 |

### Events

| 事件名        | 参数                            | 说明         |
| ------------- | ------------------------------- | ------------ |
| download      | [command: string, type: string] | 下载事件     |
| seeAreaDetail | [province: string]              | 查看详情事件 |

### 数据类型

```typescript
interface RegionData {
  province: string // 区域名称（省份/城市）
  experienceValue: number // 体验值
  momExperienceValueRate: number // 体验值环比变化率
  totalMentionValue: number // 提及量
  momTotalMentionValueRate: number // 提及量环比变化率
}
```

## 使用示例

```vue
<template>
  <RegionAnalysis
    :data="regionData"
    :loading="loading"
    @download="handleDownload"
    @see-area-detail="handleSeeDetail"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import RegionAnalysis from '@/components/RegionAnalysis/index.vue'
import type { RegionData } from '@/components/RegionAnalysis/types'

const loading = ref(false)
const regionData = ref<RegionData[]>([
  {
    province: '广东',
    experienceValue: 78.5,
    momExperienceValueRate: 2.3,
    totalMentionValue: 15432,
    momTotalMentionValueRate: 1.5
  },
  {
    province: '北京',
    experienceValue: 76.2,
    momExperienceValueRate: 1.8,
    totalMentionValue: 12345,
    momTotalMentionValueRate: 0.8
  }
])

const handleDownload = (command: string, type: string) => {
  console.log('下载:', command, type)
}

const handleSeeDetail = (province: string) => {
  console.log('查看详情:', province)
}
</script>
```

## 样式定制

组件使用SCSS编写样式，支持以下自定义：

```scss
.region-analysis {
  // 组件容器样式
  .chart-header {
    // 头部样式
  }

  .chart-content {
    // 内容区域样式
    .control-area {
      // 控制区域样式
    }

    .table-area {
      // 表格区域样式
      .region-link {
        // 区域链接样式
      }
    }
  }
}
```

## 依赖组件

- Element Plus: `el-table`, `el-table-column`, `el-select`, `el-option`, `el-button`
- 项目组件: `ShowCompare` (环比显示组件)
- 工具函数: `Thousandth`, `toFixTwo` (数字格式化)

## 注意事项

1. 数据排序默认按体验值降序排列
2. 分页默认显示前10条数据
3. 体验值环比和提及量环比使用 `ShowCompare` 组件显示
4. 提及量自动根据数值大小显示单位（千/万）
5. 组件支持响应式布局，在移动端会自动调整布局
