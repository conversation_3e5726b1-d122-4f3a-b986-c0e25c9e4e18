# FTable 自定义表格组件

基于 Element Plus 的 `el-table` 组件进行功能封装，提供支持动态列配置、render函数和自定义高度的表格组件。

## 特性

- 🎨 **样式统一**: 覆盖 el-table 默认样式，符合项目设计规范
- 🔧 **动态列配置**: 支持通过 columns 配置动态渲染表格列
- 🚀 **Render函数**: 支持列标题和单元格的自定义渲染函数
- 📏 **自定义高度**: 支持灵活的高度配置
- 📱 **响应式**: 支持响应式布局
- ⚡ **TSX 支持**: 支持 TypeScript 和 JSX 语法的 render 函数

## 基础用法

```vue
<template>
  <FTable :columns="columns" :data="data" />
</template>

<script setup>
import { ref } from 'vue'

const columns = [
  { title: '姓名', dataIndex: 'name' },
  { title: '年龄', dataIndex: 'age' },
  { title: '地址', dataIndex: 'address' }
]

const data = ref([
  { name: '张三', age: 25, address: '北京市朝阳区' },
  { name: '李四', age: 30, address: '上海市浦东新区' }
])
</script>
```

## Render 函数示例

### 示例 1: 排行榜表格（参考文档示例）

```vue
<template>
  <FTable :columns="columns" :data="data" :height="330" />
</template>

<script setup lang="tsx">
import RankDisplay from '@/components/RankDisplay/index.vue'

interface TrendingWord {
  keyword: string
  growthRate: number
  userCount: number
}

const columns = [
  {
    title: '#',
    width: 50,
    render: ({ rowIndex }: { rowIndex: number }) => {
      return <RankDisplay value={rowIndex + 1} />
    }
  },
  {
    title: '关键词',
    dataIndex: 'keyword'
  },
  {
    title: () => {
      return (
        <div>
          <span>飙升率</span>
          <el-tooltip content="飙升率说明">
            <i class="ri-information-line text-gray-400 cursor-pointer ml-4px"></i>
          </el-tooltip>
        </div>
      )
    },
    width: 100,
    dataIndex: 'growthRate',
    render: ({ record }: { record: TrendingWord }) => {
      return <span>{record.growthRate}%</span>
    }
  },
  {
    title: '用户数',
    dataIndex: 'userCount',
    width: 100
  }
]

const data = ref([
  { keyword: '电池', growthRate: 25.6, userCount: 150 },
  { keyword: '充电', growthRate: 18.3, userCount: 120 }
])
</script>
```

### 示例 2: 数据列表表格

```vue
<template>
  <FTable :columns="dataColumns" :data="listData" :height="400" />
</template>

<script setup lang="tsx">
import ExperienceIndex from '@/components/ExperienceIndex/index.vue'

interface DataItem {
  id: number
  name: string
  status: 'active' | 'inactive'
  score: number
  createTime: string
}

const dataColumns = [
  {
    title: '名称',
    dataIndex: 'name'
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    render: ({ record }: { record: DataItem }) => (
      <el-tag type={record.status === 'active' ? 'success' : 'danger'}>
        {record.status === 'active' ? '正常' : '停用'}
      </el-tag>
    )
  },
  {
    title: '评分',
    dataIndex: 'score',
    width: 120,
    render: ({ record }: { record: DataItem }) => <ExperienceIndex value={record.score} />
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160
  },
  {
    title: '操作',
    width: 120,
    render: ({ record }: { record: DataItem }) => (
      <div class="flex gap-8">
        <el-button type="primary" link size="small" onClick={() => handleEdit(record)}>
          编辑
        </el-button>
        <el-button type="danger" link size="small" onClick={() => handleDelete(record)}>
          删除
        </el-button>
      </div>
    )
  }
]

const listData = ref([
  { id: 1, name: '项目A', status: 'active', score: 85, createTime: '2024-01-15' },
  { id: 2, name: '项目B', status: 'inactive', score: 72, createTime: '2024-01-14' }
])

const handleEdit = (record: DataItem) => {
  console.log('编辑:', record)
}

const handleDelete = (record: DataItem) => {
  console.log('删除:', record)
}
</script>
```

## API

### Props

| 参数       | 说明     | 类型                | 默认值  |
| ---------- | -------- | ------------------- | ------- |
| columns    | 列配置   | `TableColumn[]`     | `[]`    |
| data       | 数据源   | `any[]`             | `[]`    |
| loading    | 加载状态 | `boolean`           | `false` |
| height     | 表格高度 | `string \| number`  | `330`   |
| pagination | 分页配置 | `boolean \| object` | `false` |
| bordered   | 边框配置 | `boolean \| object` | `false` |
| scroll     | 滚动配置 | `object`            | -       |

### TableColumn

| 参数      | 说明       | 类型                            | 默认值   |
| --------- | ---------- | ------------------------------- | -------- |
| title     | 列标题     | `string \| () => any`           | -        |
| dataIndex | 数据字段名 | `string`                        | -        |
| width     | 列宽度     | `number \| string`              | -        |
| minWidth  | 最小宽度   | `number \| string`              | -        |
| align     | 对齐方式   | `'left' \| 'center' \| 'right'` | `'left'` |
| fixed     | 固定列     | `boolean \| 'left' \| 'right'`  | `false`  |
| render    | 自定义渲染 | `(params) => any`               | -        |

### Render 函数参数

render 函数接收以下参数：

```typescript
{
  record: any,        // 当前行数据
  rowIndex: number,   // 行索引
  column: TableColumn // 列配置
}
```

## 功能特性

### 1. 可以根据传入的columns动态渲染表单

支持通过 columns 配置数组动态生成表格列，无需手动编写 `el-table-column`。

### 2. 支持columns中传入函数

列标题支持函数形式，可以返回自定义的组件或JSX元素。

### 3. 支持render函数

每个列都支持 render 函数，可以完全自定义单元格的渲染逻辑。

### 4. 支持自定义高度

通过 height 属性可以灵活设置表格高度，支持数字和字符串格式。

## 注意事项

1. 使用 render 函数时，请确保返回的组件能够正确渲染
2. title 为函数时，需要返回可渲染的内容
3. 建议为复杂的 render 函数添加适当的类型定义
4. 组件基于 Element Plus，请确保项目中已正确引入
