<script setup lang="tsx">
import { computed } from 'vue'

// 定义列配置类型
interface TableColumn {
  title?: string | any
  dataIndex?: string
  width?: number | string
  minWidth?: number | string
  align?: 'left' | 'center' | 'right'
  fixed?: boolean | 'left' | 'right'
  render?: any
}

interface Props {
  columns?: TableColumn[]
  data?: any[]
  loading?: boolean
  height?: string | number
  pagination?: boolean | object
  bordered?: boolean | object
  scroll?: object
}

const props = withDefaults(defineProps<Props>(), {
  columns: () => [],
  data: () => [],
  loading: false,
  height: 330,
  pagination: false,
  bordered: false
})

defineOptions({
  name: 'FTable'
})

// 处理高度
const tableHeight = computed(() => {
  if (typeof props.height === 'number') {
    return props.height
  }
  return props.height
})

// 处理列配置
const processedColumns = computed(() => {
  return props.columns.map(column => {
    // 如果有render函数，直接使用
    if (column.render) {
      return column
    }

    // 如果title是函数，处理title
    if (typeof column.title === 'function') {
      return {
        ...column,
        title: column.title
      }
    }

    return column
  })
})
</script>

<template>
  <el-table :data="data" :height="tableHeight" :border="false" v-loading="loading" class="f-table">
    <el-table-column
      v-for="(column, index) in processedColumns"
      :key="index"
      :prop="column.dataIndex"
      :width="column.width"
      :min-width="column.minWidth"
      :align="column.align"
      :fixed="column.fixed"
    >
      <template #header v-if="typeof column.title === 'function'">
        <component :is="column.title" />
      </template>

      <template #header v-else-if="typeof column.title === 'string'">
        {{ column.title }}
      </template>

      <template #default="scope" v-if="column.render">
        <component
          :is="column.render"
          :record="scope.row"
          :rowIndex="scope.$index"
          :column="column"
        />
      </template>
    </el-table-column>
  </el-table>
</template>

<style lang="scss" scoped>
.f-table {
  :deep(.el-table) {
    thead {
      .el-table__header-wrapper th {
        background-color: #fff;
        color: #26292e;
        font-weight: 500;
        font-size: 14px;
        border-bottom: 1px solid #f0f0f0;
      }
    }

    tbody {
      .el-table__body-wrapper td {
        border-bottom: 1px solid #f6f6f6;
        color: #26292e;
        font-size: 14px;
      }

      .el-table__row:hover td {
        background-color: #f5f7fa;
      }
    }
  }

  :deep(.el-loading-mask) {
    background-color: rgba(255, 255, 255, 0.9);
  }
}
</style>
