// FTable 组件类型定义

export interface TableColumn {
  /** 列标题 */
  title?: string | (() => any)
  /** 数据字段名 */
  dataIndex?: string
  /** 列宽度 */
  width?: number | string
  /** 最小宽度 */
  minWidth?: number | string
  /** 对齐方式 */
  align?: 'left' | 'center' | 'right'
  /** 固定列 */
  fixed?: boolean | 'left' | 'right'
  /** 自定义渲染函数 */
  render?: (params: { record: any; rowIndex: number; column: TableColumn }) => any
}

export interface FTableProps {
  /** 列配置 */
  columns?: TableColumn[]
  /** 表格数据 */
  data?: any[]
  /** 加载状态 */
  loading?: boolean
  /** 表格高度 */
  height?: string | number
  /** 分页配置 */
  pagination?: boolean | object
  /** 边框配置 */
  bordered?: boolean | object
  /** 滚动配置 */
  scroll?: object
}

// 示例数据接口
export interface TrendingWord {
  keyword: string
  growthRate: number
  userCount: number
}

export interface RankingItem {
  rank: number
  name: string
  value: number
  ratio: number
}
