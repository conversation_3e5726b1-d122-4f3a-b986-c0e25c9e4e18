import { computed, ref } from 'vue'
import { useSystemStore } from '@/store/modules/system'
import type { User, Role, Permission } from '@/types/system'

// 模拟当前用户信息，实际项目中应该从登录状态获取
const currentUser = ref<User | null>(null)

export function usePermission() {
  const systemStore = useSystemStore()

  // 当前用户的角色
  const userRoles = computed<Role[]>(() => {
    return currentUser.value?.roles || []
  })

  // 当前用户的所有权限
  const userPermissions = computed<Permission[]>(() => {
    const permissions: Permission[] = []
    userRoles.value.forEach(role => {
      if (role.permissions) {
        permissions.push(...role.permissions)
      }
    })
    // 去重
    return permissions.filter(
      (permission, index, self) => index === self.findIndex(p => p.id === permission.id)
    )
  })

  // 当前用户的最高角色级别
  const userMaxLevel = computed<number>(() => {
    if (userRoles.value.length === 0) return 999 // 没有角色时设为最低权限
    return Math.min(...userRoles.value.map(role => role.level))
  })

  /**
   * 检查是否有指定权限
   */
  const checkPermission = (permissionCode: string): boolean => {
    // 超级管理员拥有所有权限
    if (userRoles.value.some(role => role.code === 'SUPER_ADMIN')) {
      return true
    }

    // 检查用户是否有该权限
    return userPermissions.value.some(permission => permission.code === permissionCode)
  }

  /**
   * 检查是否有指定角色
   */
  const checkRole = (roleCode: string): boolean => {
    return userRoles.value.some(role => role.code === roleCode)
  }

  /**
   * 检查角色级别是否满足要求
   * @param requiredLevel 要求的最低级别（数字越小级别越高）
   */
  const checkLevel = (requiredLevel: number): boolean => {
    return userMaxLevel.value <= requiredLevel
  }

  /**
   * 检查是否是超级管理员
   */
  const isSuperAdmin = computed<boolean>(() => {
    return userRoles.value.some(role => role.code === 'SUPER_ADMIN')
  })

  /**
   * 检查是否是管理员（包括超级管理员）
   */
  const isAdmin = computed<boolean>(() => {
    return userRoles.value.some(role => role.code === 'SUPER_ADMIN' || role.level <= 2)
  })

  /**
   * 设置当前用户
   */
  const setCurrentUser = (user: User | null) => {
    currentUser.value = user
  }

  /**
   * 获取当前用户
   */
  const getCurrentUser = () => {
    return currentUser.value
  }

  /**
   * 批量检查权限
   */
  const checkPermissions = (permissionCodes: string[], mode: 'and' | 'or' = 'or'): boolean => {
    if (mode === 'and') {
      return permissionCodes.every(code => checkPermission(code))
    } else {
      return permissionCodes.some(code => checkPermission(code))
    }
  }

  /**
   * 批量检查角色
   */
  const checkRoles = (roleCodes: string[], mode: 'and' | 'or' = 'or'): boolean => {
    if (mode === 'and') {
      return roleCodes.every(code => checkRole(code))
    } else {
      return roleCodes.some(code => checkRole(code))
    }
  }

  /**
   * 获取用户权限代码列表
   */
  const getPermissionCodes = computed<string[]>(() => {
    return userPermissions.value.map(permission => permission.code)
  })

  /**
   * 获取用户角色代码列表
   */
  const getRoleCodes = computed<string[]>(() => {
    return userRoles.value.map(role => role.code)
  })

  return {
    // 状态
    currentUser: computed(() => currentUser.value),
    userRoles,
    userPermissions,
    userMaxLevel,
    isSuperAdmin,
    isAdmin,

    // 权限检查方法
    checkPermission,
    checkRole,
    checkLevel,
    checkPermissions,
    checkRoles,

    // 工具方法
    setCurrentUser,
    getCurrentUser,
    getPermissionCodes,
    getRoleCodes
  }
}
