<template>
  <slot v-if="hasPermission" />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { usePermission } from './usePermission'
import type { PermissionProps } from './types.d'

const props = withDefaults(defineProps<PermissionProps>(), {
  mode: 'or'
})

const { checkPermission, checkRole, checkLevel } = usePermission()

// 计算是否有权限
const hasPermission = computed(() => {
  const { code, role, level, mode } = props

  // 构建检查条件
  const checks: boolean[] = []

  // 权限代码检查
  if (code) {
    if (Array.isArray(code)) {
      if (mode === 'and') {
        checks.push(code.every(c => checkPermission(c)))
      } else {
        checks.push(code.some(c => checkPermission(c)))
      }
    } else {
      checks.push(checkPermission(code))
    }
  }

  // 角色检查
  if (role) {
    if (Array.isArray(role)) {
      if (mode === 'and') {
        checks.push(role.every(r => checkRole(r)))
      } else {
        checks.push(role.some(r => checkRole(r)))
      }
    } else {
      checks.push(checkRole(role))
    }
  }

  // 级别检查
  if (level !== undefined) {
    checks.push(checkLevel(level))
  }

  // 如果没有任何检查条件，默认允许
  if (checks.length === 0) {
    return true
  }

  // 根据模式返回结果
  return mode === 'and' ? checks.every(check => check) : checks.some(check => check)
})
</script>
