/**
 * 权限组件的 Props 类型定义
 */
export interface PermissionProps {
  /** 权限代码，可以是单个字符串或字符串数组 */
  code?: string | string[]
  /** 角色代码，可以是单个字符串或字符串数组 */
  role?: string | string[]
  /** 角色级别，用于级别权限控制 */
  level?: number
  /** 多个权限的验证模式，默认 'or' */
  mode?: 'and' | 'or'
}

/**
 * 权限验证模式枚举
 */
export type PermissionMode = 'and' | 'or'

/**
 * 权限检查函数类型
 */
export interface PermissionChecker {
  /** 检查权限代码 */
  checkPermission: (code: string) => boolean
  /** 检查角色代码 */
  checkRole: (role: string) => boolean
  /** 检查角色级别 */
  checkLevel: (level: number) => boolean
}

/**
 * 权限组件的 Emits 类型定义
 */
export interface PermissionEmits {
  // 目前组件暂无事件发射
}