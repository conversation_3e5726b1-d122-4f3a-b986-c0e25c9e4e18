import type { App, DirectiveBinding } from 'vue'
import { usePermission } from './usePermission'

interface PermissionValue {
  code?: string | string[]
  role?: string | string[]
  level?: number
  mode?: 'and' | 'or'
}

/**
 * 权限指令处理函数
 */
function handlePermission(
  el: HTMLElement,
  binding: DirectiveBinding<string | string[] | PermissionValue>
) {
  const { checkPermission, checkRole, checkLevel } = usePermission()

  let hasPermission = false

  if (typeof binding.value === 'string') {
    // v-permission="'permission:code'"
    hasPermission = checkPermission(binding.value)
  } else if (Array.isArray(binding.value)) {
    // v-permission="['code1', 'code2']"
    hasPermission = binding.value.some(code => checkPermission(code))
  } else if (typeof binding.value === 'object' && binding.value !== null) {
    // v-permission="{ code: 'xxx', role: 'xxx', level: 1, mode: 'and' }"
    const { code, role, level, mode = 'or' } = binding.value
    const checks: boolean[] = []

    if (code) {
      if (Array.isArray(code)) {
        if (mode === 'and') {
          checks.push(code.every(c => checkPermission(c)))
        } else {
          checks.push(code.some(c => checkPermission(c)))
        }
      } else {
        checks.push(checkPermission(code))
      }
    }

    if (role) {
      if (Array.isArray(role)) {
        if (mode === 'and') {
          checks.push(role.every(r => checkRole(r)))
        } else {
          checks.push(role.some(r => checkRole(r)))
        }
      } else {
        checks.push(checkRole(role))
      }
    }

    if (level !== undefined) {
      checks.push(checkLevel(level))
    }

    if (checks.length === 0) {
      hasPermission = true
    } else {
      hasPermission = mode === 'and' ? checks.every(check => check) : checks.some(check => check)
    }
  }

  if (!hasPermission) {
    // 移除元素
    el.remove()
  }
}

/**
 * 角色指令处理函数
 */
function handleRole(el: HTMLElement, binding: DirectiveBinding<string | string[]>) {
  const { checkRole } = usePermission()

  let hasRole = false

  if (typeof binding.value === 'string') {
    hasRole = checkRole(binding.value)
  } else if (Array.isArray(binding.value)) {
    hasRole = binding.value.some(role => checkRole(role))
  }

  if (!hasRole) {
    el.remove()
  }
}

/**
 * 级别指令处理函数
 */
function handleLevel(el: HTMLElement, binding: DirectiveBinding<number>) {
  const { checkLevel } = usePermission()

  if (!checkLevel(binding.value)) {
    el.remove()
  }
}

/**
 * 权限指令集合
 */
export const permissionDirectives = {
  // 权限指令 v-permission
  permission: {
    mounted(el: HTMLElement, binding: DirectiveBinding<string | string[] | PermissionValue>) {
      handlePermission(el, binding)
    },
    updated(el: HTMLElement, binding: DirectiveBinding<string | string[] | PermissionValue>) {
      if (binding.value !== binding.oldValue) {
        handlePermission(el, binding)
      }
    }
  },

  // 角色指令 v-role
  role: {
    mounted(el: HTMLElement, binding: DirectiveBinding<string | string[]>) {
      handleRole(el, binding)
    },
    updated(el: HTMLElement, binding: DirectiveBinding<string | string[]>) {
      if (binding.value !== binding.oldValue) {
        handleRole(el, binding)
      }
    }
  },

  // 级别指令 v-level
  level: {
    mounted(el: HTMLElement, binding: DirectiveBinding<number>) {
      handleLevel(el, binding)
    },
    updated(el: HTMLElement, binding: DirectiveBinding<number>) {
      if (binding.value !== binding.oldValue) {
        handleLevel(el, binding)
      }
    }
  }
}

/**
 * 安装权限指令
 */
export function setupPermissionDirectives(app: App) {
  // 注册权限指令
  app.directive('permission', permissionDirectives.permission)
  app.directive('role', permissionDirectives.role)
  app.directive('level', permissionDirectives.level)

  // 别名指令
  app.directive('auth', permissionDirectives.permission)
  app.directive('can', permissionDirectives.permission)
}

export default permissionDirectives
