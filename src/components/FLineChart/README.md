# FLineChart 折线图组件

基于 ECharts 封装的统一折线图组件，用于项目中各种折线图表的展示。

## 特性

- 📊 **统一主题色**：自动使用项目 CHART_THEME_COLORS 主题色配置
- 🔄 **多种模式**：支持单线和多线折线图
- 📱 **响应式设计**：支持自适应宽高配置
- 🎨 **灵活配置**：支持自定义 tooltip、字段映射等
- ⚡ **性能优化**：使用 shallowRef 管理图表配置项
- 🎯 **易用性**：简化配置，开箱即用

## 快速使用

```vue
<FLineChart
  :data="[
    { month: '1月', value: 120 },
    { month: '2月', value: 132 },
    { month: '3月', value: 101 }
  ]"
  :field-mapping="{
    xAxis: 'month',
    series: [{ name: '访问量', field: 'value' }]
  }"
  height="400px"
/>
```

## 文件结构

```
src/components/FLineChart/
├── index.vue          # 组件主文件
├── types.d.ts         # TypeScript 类型定义
├── utils.ts          # 工具函数
└── README.md         # 说明文档
```

## 示例页面

访问 `/examples/flinechart` 查看完整的组件示例和参数配置演示。
