// 组件导出文件
// 在这里导出公共组件

/**
 * 按钮组组件
 * 用于在页面中展示一组功能相关的按钮，常用于切换视图、筛选数据等场景
 * 支持单选模式和选中状态展示
 *
 * @example
 * <ButtonGroup
 *   :options="[
 *     { label: '概览', value: 'overview' },
 *     { label: 'TOP问题', value: 'top' },
 *     { label: '人群特征', value: 'crowd' }
 *   ]"
 *   v-model="activeTab"
 *   @change="handleTabChange"
 * />
 */
export { default as ButtonGroup } from './ButtonGroup'

/**
 * 数据对比组件
 * 用于显示数据的环比/同比变化情况
 * 支持提及数、体验值、负面提及率三种指标类型
 * 根据数值正负自动显示不同颜色（绿色表示正向，红色表示负向）
 *
 * @example
 * <ShowCompare
 *   :value="0.15"
 *   :type="'mention'"
 *   :show-arrow="true"
 * />
 */
export { default as ShowCompare } from './ShowCompare/index.vue'

/**
 * VOC趋势图表组件
 * 基于 ECharts 的柱状图和折线图组合组件
 * 支持柱状图堆叠显示（正面、中性、负面提及量）
 * 支持折线图显示（体验值）
 * 支持双Y轴配置和自定义尺寸
 *
 * @example
 * <VocTrendChart
 *   :data="chartData"
 *   :loading="false"
 *   :need-details="true"
 *   @bar-click="handleBarClick"
 * />
 */
export { default as VocTrendChart } from './VocTrendChart/index.vue'

/**
 * 体验提示组件
 * 用于显示数据分析和图表相关的使用提示
 * 提供用户体验指导和建议
 *
 * @example
 * <ExperienceTips />
 */
export { default as ExperienceTips } from './ExperienceTips/index.vue'

/**
 * TOP问题排行榜组件
 * 用于展示问题排行榜数据
 * 支持问题列表展示、排序、筛选等功能
 *
 * @example
 * <TopQuestion
 *   :data="topQuestionData"
 *   :config="topQuestionConfig"
 * />
 */
export { default as TopQuestion } from './TopQuestion'

/**
 * 人群特征分析组件
 * 用于展示和分析人群特征数据
 * 支持人群特征分布图表和详细列表展示
 * 内置加载状态、无数据状态和错误处理
 * 支持响应式设计和移动端适配
 *
 * @example
 * <PopulationCharacteristics
 *   :data="populationData"
 *   :loading="false"
 *   @download="handleDownload"
 * />
 */
export { default as PopulationCharacteristics } from './PopulationCharacteristics/index.vue'

/**
 * 地域分析组件
 * 用于展示各省份/地区的VOC数据对比
 * 支持排序、分页和数据导出功能
 * 包含排名、区域、体验值、体验值环比、提及量、提及量环比等数据展示
 * 支持点击区域名称查看详情，支持Excel和PDF导出
 * 内置响应式设计和移动端适配
 *
 * @example
 * <RegionAnalysis
 *   :data="regionData"
 *   :loading="false"
 *   @download="handleDownload"
 *   @see-area-detail="handleSeeDetail"
 * />
 */
export { default as RegionAnalysis } from './RegionAnalysis/index.vue'

/**
 * 图表组件集合
 * 包含各种基于 ECharts 的图表组件
 *
 * @example
 * import { BarAndPointChart, BarOrLineChart } from '@/components'
 *
 * // BarAndPointChart: 柱状图加散点图混合组件
 * <BarAndPointChart
 *   :data="chartData"
 *   :width="600"
 *   :height="400"
 *   :need-details="true"
 *   @bar-click="handleBarClick"
 * />
 *
 * // BarOrLineChart: 柱状图和折线图组合组件
 * <BarOrLineChart
 *   :data="chartData"
 *   :width="600"
 *   :height="400"
 *   :need-details="true"
 *   @bar-click="handleBarClick"
 * />
 */
export * from './Charts'

export { default as IndexAnalysis } from './IndexAnalysis'

export { default as DataSourceAnalysis } from './DataSourceAnalysis/index.vue'

/**
 * 原文明细组件
 * 用于展示VOC（客户之声）数据的详细信息
 * 支持表格展示、分页、搜索等功能
 * 包含数据源信息、情感标签、关键词分析等功能
 * 支持查看用户详情和原文详情
 *
 * @example
 * <OriginalDetails
 *   :data="originalData"
 *   :total="totalCount"
 *   :loading="loading"
 *   :page-size="pageSize"
 *   :current-page="currentPage"
 *   title="原文明细"
 *   @page-change="handlePageChange"
 *   @user-detail="handleUserDetail"
 * />
 */
export { default as OriginalDetails } from './OriginalDetails'

/**
 * 权限控制组件
 * 用于根据用户权限控制组件或内容的显示
 * 支持权限代码、角色代码和角色级别的多重验证
 * 支持 'and' 和 'or' 两种验证模式
 *
 * @example
 * <!-- 基础权限控制 -->
 * <Permission code="user:create">
 *   <el-button>创建用户</el-button>
 * </Permission>
 *
 * <!-- 多权限验证 -->
 * <Permission :code="['user:edit', 'user:delete']" mode="or">
 *   <el-button>编辑用户</el-button>
 * </Permission>
 *
 * <!-- 角色权限控制 -->
 * <Permission role="admin">
 *   <AdminPanel />
 * </Permission>
 */
export { default as Permission } from './Permission'

// 类型导出
export type * from './ButtonGroup/types.d'
export type * from './Charts'
export type * from './VocTrendChart/types.d'
export type * from './TopQuestion/types.d'
export type * from './PopulationCharacteristics/types.d'
export type * from './RegionAnalysis/types.d'
export type * from './IndexAnalysis/types.d'
export type * from './ShowCompare/types.d'
export type * from './OriginalDetails/types.d'
export type * from './ExperienceTips/types.d'
export type * from './Permission/types.d'
export type * from './DataSourceAnalysis/types.d'

export default {}
