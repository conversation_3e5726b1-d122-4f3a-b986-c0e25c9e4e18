<template>
  <div class="text-infos">
    <span v-if="data.dataSourceName" class="info-item">
      {{ data.dataSourceName }}
    </span>

    <span v-if="data.brandName || data.seriesName" class="info-item">
      {{ brandSeriesText }}
    </span>

    <span v-if="data.createTime" class="info-item">
      {{ data.createTime }}
    </span>

    <span v-if="data.vin" class="info-item">
      <span class="vin">{{ data.vin }}</span>
      <span v-if="carOwnerText" class="car-owner">{{ carOwnerText }}</span>
    </span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { OriginalTextItem } from '../types.d'

interface Props {
  data: OriginalTextItem
  textType?: string
}

const props = withDefaults(defineProps<Props>(), {
  textType: ''
})

// 品牌车系文本
const brandSeriesText = computed(() => {
  const parts: string[] = []
  if (props.data.brandName) {
    parts.push(`【${props.data.brandName}】`)
  }
  if (props.data.seriesName) {
    parts.push(props.data.seriesName)
  }
  return parts.join('')
})

// 车主身份文本
const carOwnerText = computed(() => {
  if (props.data.isCarOwner === '是') {
    return '车主'
  } else if (props.data.isCarOwner === '否') {
    return '非车主'
  }
  return ''
})
</script>

<style lang="scss" scoped>
.text-infos {
  margin-top: 8px;
  font-size: 12px;
  line-height: 1.5;

  .info-item {
    display: inline-block;
    padding-right: 12px;
    margin-right: 12px;
    border-right: 1px solid #d8d8d8;
    color: rgba(0, 0, 0, 0.45);

    &:last-child {
      border-right: none;
      margin-right: 0;
      padding-right: 0;
    }

    .vin {
      margin-right: 8px;
    }

    .car-owner {
      font-weight: 500;
    }
  }
}
</style>
