<template>
  <div class="keyword-tags" :class="{ 'mt-5': showTitle }">
    <span v-if="showTitle" class="title">标准关键词：</span>
    <template v-if="displayData.length">
      <span
        v-for="(item, index) in displayData"
        :key="`${item.standardkeyword}-${index}`"
        class="keyword-tag"
        :class="getTagClass(item.extractedsense)"
      >
        {{ item.standardkeyword }}
      </span>
    </template>
    <span v-else class="no-keywords">暂无关键词</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { AnalysisResult, ExtractedInfo } from '../types.d'

interface Props {
  showTitle?: boolean
  indexTypeName?: string
  data?: AnalysisResult
}

const props = withDefaults(defineProps<Props>(), {
  showTitle: true,
  indexTypeName: '全领域业务',
  data: () => ({})
})

// 计算显示的数据
const displayData = computed<ExtractedInfo[]>(() => {
  const extractedinfo = props.data.extractedinfo || []
  const seen = new Set<string>()
  const result: ExtractedInfo[] = []

  for (const item of extractedinfo) {
    if (
      item.extracteddomain === props.indexTypeName &&
      item.standardkeyword &&
      !seen.has(item.standardkeyword)
    ) {
      result.push(item)
      seen.add(item.standardkeyword)
    }
  }

  return result
})

// 获取标签样式类
const getTagClass = (sense: string) => {
  switch (sense) {
    case '正面':
      return 'positive'
    case '中性':
      return 'neutral'
    case '负面':
      return 'negative'
    default:
      return 'neutral'
  }
}
</script>

<style lang="scss" scoped>
.keyword-tags {
  font-size: 12px;
  line-height: 1.5;

  .title {
    color: rgba(0, 0, 0, 0.65);
    margin-right: 8px;
  }

  .keyword-tag {
    display: inline-block;
    padding: 4px 8px;
    margin-right: 8px;
    margin-bottom: 4px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    height: 24px;
    line-height: 16px;

    &.positive {
      background: rgba(1, 198, 142, 0.08);
      color: #01c68e;
    }

    &.neutral {
      background: rgba(0, 119, 255, 0.08);
      color: #0077ff;
    }

    &.negative {
      background: rgba(247, 61, 71, 0.08);
      color: #f73d47;
    }
  }

  .no-keywords {
    color: rgba(0, 0, 0, 0.45);
    font-style: italic;
  }
}
</style>
