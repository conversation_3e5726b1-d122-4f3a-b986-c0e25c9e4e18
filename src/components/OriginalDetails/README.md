# OriginalDetails 原文明细组件

## 📋 概述

原文明细组件用于展示VOC（客户之声）数据的详细信息，支持表格展示、分页、搜索等功能。组件包含数据源信息、情感标签、关键词分析等功能，支持查看用户详情和原文详情。

## 🚀 功能特性

### 核心功能

- **表格展示**：序号 + 内容 + 操作三列布局
- **内容展示**：文章/工单标题、详细内容、数据源信息、情感标签
- **分页功能**：支持页面大小切换（10/20/30/50条）
- **用户交互**：查看用户详情、查看原文详情
- **响应式设计**：适配桌面端和移动端

### 数据源支持

- 帖子评论 (post_comments)
- 工单 (work_order)
- 意见反馈 (feedback)
- 咨询服务 (consulting_service)
- 问卷调研 (questionnaire)

## 🔧 使用方法

### 基础用法

```vue
<template>
  <OriginalDetails
    :data="originalData"
    :total="totalCount"
    :loading="loading"
    :page-size="pageSize"
    :current-page="currentPage"
    title="原文明细"
    @page-change="handlePageChange"
    @user-detail="handleUserDetail"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { OriginalDetails } from '@/components'
import type { OriginalTextItem } from '@/components/OriginalDetails/types.d'

const originalData = ref<OriginalTextItem[]>([
  {
    id: '1',
    dataSourceName: '汽车之家-用户发帖',
    brandName: '长安汽车',
    seriesName: 'CS75 PLUS',
    createTime: '2024-12-28 10:30:00',
    vin: 'LS1234567890',
    isCarOwner: '是',
    oneId: 'user001',
    user: '车友小张',
    postsTitle: '长安CS75 PLUS驾驶体验分享',
    postsContent: '这车的空间确实不错...',
    analysisResult: {
      extractedinfo: [
        {
          standardkeyword: '空间大',
          extractedsense: '正面',
          extracteddomain: '全领域业务'
        }
      ]
    }
  }
])

const totalCount = ref(158)
const loading = ref(false)
const pageSize = ref(10)
const currentPage = ref(1)

function handlePageChange(params: { pageNum: number; pageSize?: number }) {
  // 处理分页变化
}

function handleUserDetail(params: { oneId: string; userName: string }) {
  // 处理查看用户详情
}
</script>
```

## 📖 API 说明

### Props

| 参数          | 类型                 | 默认值         | 说明         |
| ------------- | -------------------- | -------------- | ------------ |
| data          | `OriginalTextItem[]` | `[]`           | 表格数据     |
| total         | `number`             | `0`            | 数据总数     |
| loading       | `boolean`            | `false`        | 加载状态     |
| pageSize      | `number`             | `10`           | 每页条数     |
| currentPage   | `number`             | `1`            | 当前页码     |
| indexTypeName | `string`             | `'全领域业务'` | 指标类型名称 |
| title         | `string`             | `'原文明细'`   | 组件标题     |

### Events

| 事件名      | 参数                                     | 说明                   |
| ----------- | ---------------------------------------- | ---------------------- |
| page-change | `{ pageNum: number; pageSize?: number }` | 分页变化时触发         |
| user-detail | `{ oneId: string; userName: string }`    | 点击查看用户详情时触发 |

### 数据类型

#### OriginalTextItem

```typescript
interface OriginalTextItem {
  id: string | number
  dataSourceName: string // 数据源名称
  brandName?: string // 品牌名称
  seriesName?: string // 车系名称
  createTime?: string // 创建时间
  vin?: string // 车架号
  isCarOwner?: string // 是否车主
  oneId?: string // 用户ID
  user?: string // 用户名
  name?: string // 姓名
  analysisResult?: AnalysisResult // 分析结果

  // 动态字段（根据数据源类型变化）
  postsTitle?: string // 帖子标题
  postsContent?: string // 帖子内容
  title?: string // 标题
  content?: string // 内容
  answer_content?: string // 问卷回答内容
  [key: string]: any
}
```

#### AnalysisResult

```typescript
interface AnalysisResult {
  extractedinfo?: ExtractedInfo[]
}

interface ExtractedInfo {
  standardkeyword: string // 标准关键词
  extractedsense: '正面' | '中性' | '负面' // 情感倾向
  extracteddomain: string // 提取域
}
```

## 🎨 样式定制

### CSS 变量

组件使用项目全局SCSS变量，支持以下样式定制：

```scss
// 主要颜色
$text-color-primary: #303133;
$text-color-secondary: #606266;
$text-color-regular: #909399;
$text-color-placeholder: #c0c4cc;

// 背景颜色
$bg-color: #f5f7fa;
$bg-color-secondary: #fafafa;
$bg-color-page: #f2f6fc;

// 边框
$border-color: #dcdfe6;
$border-radius: 4px;

// 间距
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
```

### 关键词标签样式

```scss
.keyword-tag {
  &.positive {
    background: rgba(1, 198, 142, 0.08);
    color: #01c68e;
  }
  &.neutral {
    background: rgba(0, 119, 255, 0.08);
    color: #0077ff;
  }
  &.negative {
    background: rgba(247, 61, 71, 0.08);
    color: #f73d47;
  }
}
```

## 📱 响应式设计

组件支持响应式设计，在不同屏幕尺寸下自动调整布局：

- **桌面端**：完整功能展示，三列布局
- **移动端**：简化显示，自适应列宽，字体大小调整

## 🔧 技术实现

### 技术栈

- Vue 3 Composition API
- TypeScript
- Element Plus
- SCSS

### 关键特性

- 类型安全的TypeScript支持
- 响应式数据管理
- 组件化架构设计
- 性能优化（虚拟滚动、事件节流）

## 📝 注意事项

1. **数据格式**：确保传入的数据符合`OriginalTextItem`类型定义
2. **事件处理**：及时处理`page-change`和`user-detail`事件
3. **加载状态**：适当使用loading状态提升用户体验
4. **错误处理**：组件内部已包含基础错误处理，建议在父组件中添加额外的错误边界

## 🚀 未来规划

- [ ] 支持表格列自定义配置
- [ ] 增加数据导出功能
- [ ] 支持高级筛选和搜索
- [ ] 增加无限滚动加载
- [ ] 支持主题定制
