// 原文明细组件类型定义

// 分析结果项
export interface ExtractedInfo {
  standardkeyword: string
  extractedsense: '正面' | '中性' | '负面'
  extracteddomain: string
}

// 分析结果
export interface AnalysisResult {
  extractedinfo?: ExtractedInfo[]
}

// 原文数据项
export interface OriginalTextItem {
  id: string | number
  dataSourceName: string // 数据源名称
  brandName?: string // 品牌名称
  seriesName?: string // 车系名称
  createTime?: string // 创建时间
  vin?: string // 车架号
  isCarOwner?: string // 是否车主
  oneId?: string // 用户ID
  user?: string // 用户名
  name?: string // 姓名
  analysisResult?: AnalysisResult // 分析结果

  // 帖子评论字段
  postsTitle?: string
  postsContent?: string
  comment?: string

  // 工单字段
  title?: string
  content?: string

  // 意见反馈字段
  feedback?: string

  // 咨询服务字段
  consulting?: string

  // 问卷调研字段
  answer_content?: string
  answer_fraction?: string

  // 其他动态字段
  [key: string]: any
}

// 组件Props
export interface OriginalDetailsProps {
  data: OriginalTextItem[] // 表格数据
  total: number // 数据总数
  loading?: boolean // 加载状态
  pageSize?: number // 每页条数
  currentPage?: number // 当前页码
  indexTypeName?: string // 指标类型名称
  title?: string // 组件标题
}

// 组件Events
export interface OriginalDetailsEmits {
  'page-change': [{ pageNum: number; pageSize?: number }] // 分页变化
  'user-detail': [{ oneId: string; userName: string }] // 查看用户详情
}

// 数据源类型映射
export interface DataSourceTypeMap {
  [key: string]:
    | 'post_comments'
    | 'work_order'
    | 'feedback'
    | 'consulting_service'
    | 'questionnaire'
}

// 字段映射配置
export interface FieldMapping {
  title?: string
  content?: string
  comment?: string
  answer_content?: string
  answer_fraction?: string
}

// 数据源字段映射
export interface DataSourceFieldMap {
  post_comments: FieldMapping
  work_order: FieldMapping
  feedback: FieldMapping
  consulting_service: FieldMapping
  questionnaire: FieldMapping
}
