export interface TopQuestionItem {
  id: string
  keyword: string
  emotionAttribute: 'positive' | 'negative' | 'neutral'
  totalMentionValue: number
  momTotalMentionValue: number
  momTotalMentionValueRate: number
  mentionRate: number
  category?: string
  priority?: string
}

export interface TopQuestionConfig {
  showTitle?: boolean
  title?: string
  pageSize?: number
  sortType?: 'mention' | 'rate'
  emotionFilter?: 'all' | 'positive' | 'negative' | 'neutral'
  exportEnabled?: boolean
  keywordClickEnabled?: boolean
}
