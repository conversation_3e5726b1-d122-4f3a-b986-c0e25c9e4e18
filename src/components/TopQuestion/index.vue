<template>
  <div class="top-question-container">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-left">
        <span class="filter-label">情感筛选</span>
        <el-select v-model="selectedEmotion" @change="onEmotionChange" style="width: 100px">
          <el-option label="全部" value="all" />
          <el-option label="正面" value="positive" />
          <el-option label="负面" value="negative" />
          <el-option label="中性" value="neutral" />
        </el-select>
      </div>
      <div class="filter-right">
        <el-select v-model="pageSize" @change="onPageSizeChange" style="width: 100px">
          <el-option label="10条" :value="10" />
          <el-option label="20条" :value="20" />
          <el-option label="50条" :value="50" />
        </el-select>
        <el-select v-model="sortType" @change="onSortChange" style="width: 140px">
          <el-option label="按提及量排序" value="mention" />
          <el-option label="按提及率排序" value="rate" />
        </el-select>
      </div>
    </div>
    <!-- 数据表格 -->
    <el-table
      :data="displayData"
      border
      style="width: 100%"
      :header-cell-style="{ background: '#fafafa', fontWeight: 500, color: '#333' }"
    >
      <el-table-column label="排名" width="60" align="center">
        <template #default="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>
      <el-table-column label="标准关键词" min-width="120">
        <template #default="scope">
          <span class="keyword" @click="onKeywordClick(scope.row.keyword)">{{
            scope.row.keyword
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="情感" width="80" align="center">
        <template #default="scope">
          <span :class="getEmotionClass(scope.row.emotionAttribute)">{{
            getEmotionLabel(scope.row.emotionAttribute)
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="提及量" min-width="100" align="center">
        <template #header>
          <div>提及量</div>
          <div>提及量变化</div>
        </template>
        <template #default="scope">
          <div class="mention-value">{{ formatNumber(scope.row.mentions) }}</div>
          <div class="mention-change" :class="getChangeClass(scope.row.mentionsChange)">
            {{ formatChange(scope.row.mentionsChange) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="提及量环比" min-width="100" align="center">
        <template #default="scope">
          <span :class="getRateClass(scope.row.momTotalMentionValueRate)">{{
            formatRate(scope.row.mentionsMoM || 0)
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="提及率" min-width="80" align="center">
        <template #default="scope">{{ formatPercent(scope.row.mentionRate) }}%</template>
      </el-table-column>
    </el-table>
    <!-- 导出按钮 -->
    <!-- <div class="export-section">
      <el-button type="primary" @click="exportData">导出Excel</el-button>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { TopQuestionConfig } from './types.d'
import type { KeywordData } from '@/api/common/index.d'

const props = defineProps<{
  data: KeywordData[]
  config?: TopQuestionConfig
}>()
const emit = defineEmits<{
  (e: 'filter-change', filters: { emotion: string; sortType: string }): void
  (e: 'page-size-change', pageSize: number): void
  (e: 'sort-change', sortConfig: { emotion: string; sortType: string }): void
  (e: 'keyword-click', keyword: string): void
  (e: 'export', exportConfig: { type: string; data: KeywordData[] }): void
}>()

const selectedEmotion = ref(props.config?.emotionFilter || 'all')
const pageSize = ref(props.config?.pageSize || 10)
const sortType = ref(props.config?.sortType || 'mention')

watch(
  () => props.config,
  val => {
    if (val) {
      selectedEmotion.value = val.emotionFilter || 'all'
      pageSize.value = val.pageSize || 10
      sortType.value = val.sortType || 'mention'
    }
  }
)

const filteredData = computed(() => {
  if (selectedEmotion.value === 'all') return props.data
  return props.data.filter(item => item.sentiment === selectedEmotion.value)
})
const sortedData = computed(() => {
  const data = [...filteredData.value]
  if (sortType.value === 'mention') {
    return data.sort((a, b) => (b.mentions || 0) - (a.mentions || 0))
  } else if (sortType.value === 'rate') {
    return data.sort((a, b) => (b.mentionRate || 0) - (a.mentionRate || 0))
  }
  return data
})
const displayData = computed(() => sortedData.value.slice(0, pageSize.value))

function onEmotionChange() {
  emit('filter-change', { emotion: selectedEmotion.value, sortType: sortType.value })
}
function onPageSizeChange() {
  emit('page-size-change', pageSize.value)
}
function onSortChange() {
  emit('sort-change', { emotion: selectedEmotion.value, sortType: sortType.value })
}
function onKeywordClick(keyword: string) {
  emit('keyword-click', keyword)
}
function exportData() {
  emit('export', { type: 'topQuestion', data: displayData.value })
  ElMessage.success('导出成功（模拟）')
}
function formatNumber(num: number) {
  if (num >= 10000) return (num / 10000).toFixed(1) + '万'
  return num
}
function formatChange(change: number) {
  if (change > 0) return '+' + formatNumber(change)
  if (change < 0) return '-' + formatNumber(Math.abs(change))
  return '-'
}
function formatRate(rate: number) {
  return (rate * 100).toFixed(1) + '%'
}
function formatPercent(percent: number) {
  return (percent * 100).toFixed(2)
}
function getEmotionClass(emotion: string) {
  const classes: Record<string, string> = {
    positive: 'emotion-positive',
    negative: 'emotion-negative',
    neutral: 'emotion-neutral'
  }
  return classes[emotion] || 'emotion-neutral'
}
function getEmotionLabel(emotion: string) {
  const labels: Record<string, string> = {
    positive: '正面',
    negative: '负面',
    neutral: '中性'
  }
  return labels[emotion] || '中性'
}
function getChangeClass(change: number) {
  if (change > 0) return 'change-positive'
  if (change < 0) return 'change-negative'
  return 'change-neutral'
}
function getRateClass(rate: number) {
  if (rate > 0) return 'rate-positive'
  if (rate < 0) return 'rate-negative'
  return 'rate-neutral'
}
</script>

<style lang="scss" scoped>
.top-question-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}
.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px 0;
}
.filter-left,
.filter-right {
  display: flex;
  align-items: center;
  gap: 10px;
}
.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}
.keyword {
  color: #0077ff;
  cursor: pointer;
  font-size: 14px;
  line-height: 22px;
}
.keyword:hover {
  text-decoration: underline;
}
.emotion-positive {
  background: #f6ffed;
  color: #52c41a;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}
.emotion-negative {
  background: #fff2f0;
  color: #ff4d4f;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}
.emotion-neutral {
  background: #f5f5f5;
  color: #666;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}
.mention-value {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  line-height: 22px;
}
.mention-change {
  font-size: 14px;
  line-height: 22px;
  margin-top: 4px;
}
.change-positive {
  color: #52c41a;
}
.change-negative {
  color: #ff4d4f;
}
.change-neutral {
  color: #666;
}
.rate-positive {
  color: #52c41a;
}
.rate-negative {
  color: #ff4d4f;
}
.rate-neutral {
  color: #666;
}
.export-section {
  margin-top: 20px;
  text-align: right;
}
</style>
