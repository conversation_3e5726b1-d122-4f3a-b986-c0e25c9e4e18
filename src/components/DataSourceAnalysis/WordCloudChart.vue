<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts/core'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { TooltipComponent } from 'echarts/components'
import 'echarts-wordcloud'
import type { WordCloudItem } from './types.d'

use([CanvasRenderer, TooltipComponent])

const props = defineProps<{ data: WordCloudItem[] }>()
const emit = defineEmits<{ (e: 'wordClick', word: WordCloudItem): void }>()
const chartRef = ref<HTMLDivElement | null>(null)
let chart: echarts.ECharts | null = null

const renderChart = () => {
  if (!chartRef.value) return
  if (!chart) {
    chart = echarts.init(chartRef.value)
    chart.on('click', (params: any) => {
      emit('wordClick', params.data)
    })
  }
  chart.setOption({
    tooltip: { show: true },
    series: [
      {
        type: 'wordCloud',
        shape: 'circle',
        left: 'center',
        top: 'center',
        width: '100%',
        height: '100%',
        sizeRange: [16, 48],
        rotationRange: [0, 0],
        gridSize: 8,
        drawOutOfBound: false,
        textStyle: {
          fontFamily: 'sans-serif',
          fontWeight: 'bold',
          color: () => `hsl(${Math.random() * 360},70%,60%)`
        },
        data: props.data
      }
    ]
  })
}

onMounted(renderChart)
watch(() => props.data, renderChart, { deep: true })
</script>

<template>
  <div ref="chartRef" style="width: 100%; height: 100%"></div>
</template>
