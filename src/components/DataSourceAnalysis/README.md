# DataSourceAnalysis 数据来源分析组件

## 组件用途

用于展示多数据源的主图（柱状+折线）、趋势图和词云图，支持数据联动、详情查看、词条点击等交互，适用于数据分析场景。

## Props

| 参数       | 说明                    | 类型             | 是否必填 | 默认值 |
| ---------- | ----------------------- | ---------------- | -------- | ------ |
| chartId    | 图表唯一标识            | string           | 否       | ''     |
| loading    | 加载状态                | boolean          | 否       | false  |
| data       | 主图数据                | DataSourceItem[] | 是       | []     |
| remarkData | 趋势/词云等补充数据     | DataSourceRemark | 否       | {}     |
| attr       | 显示的属性              | string           | 否       | ''     |
| attrName   | 显示的属性名称          | string           | 否       | ''     |
| preDivId   | dom前缀，便于多实例区分 | string           | 否       | ''     |

## 事件

| 事件名              | 说明             | 回调参数            |
| ------------------- | ---------------- | ------------------- |
| download            | 下载当前图表数据 | 无                  |
| changeDatasource    | 切换数据源       | dataSource: string  |
| datasourceSeeDetail | 趋势图详情点击   | trendItem: any      |
| wordCloudChartClick | 词云词条点击     | word: WordCloudItem |

## 类型定义

见 types.d.ts 文件。

## 用法示例

```vue
<DataSourceAnalysis
  :data="dataSourceList"
  :remarkData="remarkData"
  :loading="loading"
  @download="onDownload"
  @changeDatasource="onChangeDatasource"
  @datasourceSeeDetail="onSeeDetail"
  @wordCloudChartClick="onWordClick"
/>
```

## 依赖子组件

- BarAndPointChart（主图）
- BarOrLineChart（趋势图）
- WordCloudChart（词云图）

</rewritten_file>
