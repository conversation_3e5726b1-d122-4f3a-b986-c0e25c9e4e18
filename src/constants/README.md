# 常量管理目录

本目录统一管理项目中的常量定义，遵循简洁实用的原则。

## 📁 文件结构

```
src/constants/
├── index.ts       # 统一导出入口
└── README.md      # 说明文档（本文件）
```

## 🎯 设计原则

### 简洁实用

- **按需添加**: 只添加项目实际使用的常量
- **避免过度设计**: 不提前定义可能用到的常量
- **保持精简**: 当前项目实际没有使用常量，所以暂时为空

### 命名规范

- **常量对象**: SCREAMING_SNAKE_CASE，如 `USER_STATUS`
- **常量属性**: SCREAMING_SNAKE_CASE，如 `USER_STATUS.ACTIVE`
- **描述性后缀**: `_MAP`、`_CONFIG`、`_DEFAULT` 等

## 🔧 当前状态

当前项目中实际**没有使用任何常量**，所以 `index.ts` 文件为空。

这是正常的，体现了"按需添加，避免过度设计"的原则。

## 🚀 扩展指南

### 何时添加常量

当项目中确实需要使用常量时，可以添加：

```typescript
// 示例：添加用户状态常量
export const USER_STATUS = {
  ACTIVE: 1,
  INACTIVE: 0,
  LOCKED: -1
} as const

// 示例：添加API状态码常量
export const API_CODE = {
  SUCCESS: 200,
  ERROR: 500,
  UNAUTHORIZED: 401
} as const
```

### 使用方式

```typescript
// 从常量目录导入
import { USER_STATUS, API_CODE } from '@/constants'

// 在代码中使用
const isActive = user.status === USER_STATUS.ACTIVE
const isSuccess = response.code === API_CODE.SUCCESS
```

### 添加新常量的步骤

1. **确认必要性**: 确保常量确实被项目使用
2. **创建文件**: 如果是新分类，创建对应的 `.ts` 文件
3. **定义常量**: 使用 `as const` 确保类型推断
4. **导出常量**: 在 `index.ts` 中导出
5. **更新文档**: 更新本 README 文件

### 常量设计建议

```typescript
// ✅ 推荐：使用对象组织相关常量
export const THEME_COLORS = {
  PRIMARY: 'primary',
  SUCCESS: 'success',
  WARNING: 'warning'
} as const

// ✅ 推荐：提供类型推断
type ThemeColor = (typeof THEME_COLORS)[keyof typeof THEME_COLORS]

// ❌ 避免：分散的常量定义
export const PRIMARY_COLOR = 'primary'
export const SUCCESS_COLOR = 'success'
```

## 🔗 与其他模块的关系

- **类型定义**: `src/types/index.d.ts` - 定义常量相关的接口类型
- **工具函数**: `src/utils/*.ts` - 使用常量的业务逻辑
- **组件使用**: 在 Vue 组件中导入和使用常量

```typescript
// 类型定义
declare interface ApiResponse {
  code: number
  // ...
}

// 常量定义（当需要时）
export const API_CODE = {
  SUCCESS: 200,
  ERROR: 500
} as const

// 使用示例
import { API_CODE } from '@/constants'
const isSuccess = response.code === API_CODE.SUCCESS
```

---

**维护原则**: 简洁实用，按需添加，避免过度设计
