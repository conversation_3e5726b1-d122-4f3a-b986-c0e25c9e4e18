import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { isDev, isProd } from './env'

describe('环境工具函数测试', () => {
  // 保存原始环境变量值
  let originalDev: boolean
  let originalProd: boolean

  beforeEach(() => {
    // 保存原始值
    originalDev = import.meta.env.DEV
    originalProd = import.meta.env.PROD
  })

  afterEach(() => {
    // 恢复原始值
    vi.mocked(import.meta.env).DEV = originalDev
    vi.mocked(import.meta.env).PROD = originalProd
  })

  describe('isDev', () => {
    it('应该在开发环境返回true', () => {
      vi.mocked(import.meta.env).DEV = true

      const result = isDev()
      expect(result).toBe(true)
    })

    it('应该在非开发环境返回false', () => {
      vi.mocked(import.meta.env).DEV = false

      const result = isDev()
      expect(result).toBe(false)
    })

    it('应该直接使用import.meta.env.DEV的值', () => {
      // 测试函数直接返回DEV环境变量的值
      expect(isDev()).toBe(import.meta.env.DEV)
    })
  })

  describe('isProd', () => {
    it('应该在生产环境返回true', () => {
      vi.mocked(import.meta.env).PROD = true

      const result = isProd()
      expect(result).toBe(true)
    })

    it('应该在非生产环境返回false', () => {
      vi.mocked(import.meta.env).PROD = false

      const result = isProd()
      expect(result).toBe(false)
    })

    it('应该直接使用import.meta.env.PROD的值', () => {
      // 测试函数直接返回PROD环境变量的值
      expect(isProd()).toBe(import.meta.env.PROD)
    })
  })

  describe('环境函数互斥性测试', () => {
    it('在开发环境下，isDev应该为true，isProd应该为false', () => {
      vi.mocked(import.meta.env).DEV = true
      vi.mocked(import.meta.env).PROD = false

      expect(isDev()).toBe(true)
      expect(isProd()).toBe(false)
    })

    it('在生产环境下，isProd应该为true，isDev应该为false', () => {
      vi.mocked(import.meta.env).DEV = false
      vi.mocked(import.meta.env).PROD = true

      expect(isDev()).toBe(false)
      expect(isProd()).toBe(true)
    })
  })

  describe('函数稳定性测试', () => {
    it('isDev函数应该返回布尔值', () => {
      const result = isDev()
      expect(typeof result).toBe('boolean')
    })

    it('isProd函数应该返回布尔值', () => {
      const result = isProd()
      expect(typeof result).toBe('boolean')
    })

    it('多次调用应该返回一致的结果', () => {
      const dev1 = isDev()
      const dev2 = isDev()
      const prod1 = isProd()
      const prod2 = isProd()

      expect(dev1).toBe(dev2)
      expect(prod1).toBe(prod2)
    })
  })
})
