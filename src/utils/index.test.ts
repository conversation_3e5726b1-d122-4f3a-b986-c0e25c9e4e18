import { describe, it, expect } from 'vitest'

describe('工具函数模块导出测试', () => {
  describe('模块导入', () => {
    it('应该能够正确导入env模块', async () => {
      const envModule = await import('./env')

      expect(envModule).toBeDefined()
      expect(typeof envModule.isDev).toBe('function')
      expect(typeof envModule.isProd).toBe('function')
    })

    it('应该能够正确导入chart模块', async () => {
      const chartModule = await import('./chart')

      expect(chartModule).toBeDefined()
      expect(typeof chartModule.processChartData).toBe('function')
      expect(typeof chartModule.formatAxisValue).toBe('function')
      expect(typeof chartModule.makeDataUnit).toBe('function')
      expect(typeof chartModule.getChartColors).toBe('function')
      expect(typeof chartModule.getGridConfig).toBe('function')
      expect(typeof chartModule.getYAxisConfig).toBe('function')
      expect(typeof chartModule.getXAxisConfig).toBe('function')
      expect(typeof chartModule.getTooltipConfig).toBe('function')
      expect(typeof chartModule.getLegendConfig).toBe('function')
    })

    it('应该能够正确导入index模块', async () => {
      const indexModule = await import('./index')

      expect(indexModule).toBeDefined()
      // index模块应该导出其他模块的主要功能
    })
  })

  describe('函数可用性验证', () => {
    it('环境函数应该正常工作', async () => {
      const { isDev, isProd } = await import('./env')

      // 这些函数应该返回布尔值
      expect(typeof isDev()).toBe('boolean')
      expect(typeof isProd()).toBe('boolean')

      // 开发和生产环境不能同时为true
      expect(isDev() && isProd()).toBe(false)
    })

    it('图表工具函数应该正常工作', async () => {
      const { makeDataUnit, getChartColors, getGridConfig } = await import('./chart')

      // 测试基本功能
      expect(makeDataUnit(1000)).toBe('1.0K')
      expect(getChartColors()).toBeInstanceOf(Array)
      expect(getGridConfig()).toBeInstanceOf(Object)
    })
  })

  describe('模块结构验证', () => {
    it('应该只导出预期的函数', async () => {
      const envModule = await import('./env')
      const envKeys = Object.keys(envModule)

      expect(envKeys).toContain('isDev')
      expect(envKeys).toContain('isProd')
      // 确保没有意外导出其他内容
      expect(envKeys.length).toBe(2)
    })

    it('图表模块应该导出所有必要的函数', async () => {
      const chartModule = await import('./chart')
      const chartKeys = Object.keys(chartModule)

      const expectedFunctions = [
        'processChartData',
        'formatAxisValue',
        'makeDataUnit',
        'getChartColors',
        'getGridConfig',
        'getYAxisConfig',
        'getXAxisConfig',
        'getTooltipConfig',
        'getLegendConfig'
      ]

      expectedFunctions.forEach(func => {
        expect(chartKeys).toContain(func)
      })
    })
  })

  describe('模块稳定性测试', () => {
    it('重复导入应该返回相同的引用', async () => {
      const envModule1 = await import('./env')
      const envModule2 = await import('./env')

      expect(envModule1.isDev).toBe(envModule2.isDev)
      expect(envModule1.isProd).toBe(envModule2.isProd)
    })

    it('函数引用应该保持稳定', async () => {
      const { makeDataUnit } = await import('./chart')
      const { makeDataUnit: makeDataUnit2 } = await import('./chart')

      expect(makeDataUnit).toBe(makeDataUnit2)
    })
  })
})
