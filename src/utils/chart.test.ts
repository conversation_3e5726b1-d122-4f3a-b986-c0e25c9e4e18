import { describe, it, expect } from 'vitest'
import {
  processChartData,
  formatAxisValue,
  makeDataUnit,
  getChartColors,
  getGridConfig,
  getYAxisConfig,
  getXAxisConfig,
  getTooltipConfig,
  getLegendConfig
} from './chart'
import type { ChartData } from '@/types/chart'

describe('图表工具函数测试', () => {
  describe('processChartData', () => {
    it('应该正确处理基础图表数据', () => {
      const mockData: ChartData = {
        data: [
          { date: '2024-01', positive: 100, negative: 30 },
          { date: '2024-02', positive: 120, negative: 25 },
          { date: '2024-03', positive: 90, negative: 35 }
        ],
        xDataKey: 'date',
        seriesDataKey: [
          {
            name: '正面提及量',
            key: 'positive',
            type: 'bar',
            color: '#3ED4A9'
          },
          {
            name: '负面提及量',
            key: 'negative',
            type: 'bar',
            color: '#5D7092'
          }
        ]
      }

      const result = processChartData(mockData)

      // 验证X轴数据
      expect(result.xDataArr).toEqual(['2024-01', '2024-02', '2024-03'])

      // 验证系列数据
      expect(result.seriesData).toHaveLength(2)
      expect(result.seriesData[0].name).toBe('正面提及量')
      expect(result.seriesData[0].data).toEqual([100, 120, 90])
      expect(result.seriesData[1].name).toBe('负面提及量')
      expect(result.seriesData[1].data).toEqual([30, 25, 35])

      // 验证图例数据
      expect(result.legendData).toContain('正面提及量')
      expect(result.legendData).toContain('负面提及量')
    })

    it('应该正确处理空数据', () => {
      const emptyData: ChartData = {
        data: [],
        xDataKey: 'date',
        seriesDataKey: []
      }

      const result = processChartData(emptyData)

      expect(result.xDataArr).toEqual([])
      expect(result.seriesData).toEqual([])
      expect(result.legendData).toEqual([])
    })

    it('应该正确处理散点图类型数据', () => {
      const scatterData: ChartData = {
        data: [
          { date: '2024-01', experience: 0.8 },
          { date: '2024-02', experience: 0.85 }
        ],
        xDataKey: 'date',
        seriesDataKey: [
          {
            name: '体验值',
            key: 'experience',
            type: 'scatter',
            color: '#FFC157'
          }
        ]
      }

      const result = processChartData(scatterData)

      // 验证散点图转换为线图
      expect(result.seriesData[0].type).toBe('line')
      expect(result.seriesData[0].symbol).toBe('circle')
      expect(result.seriesData[0].smooth).toBe(true)
      expect(result.seriesData[0].symbolSize).toBe(9)
    })

    it('应该正确处理缺失数据', () => {
      const dataWithMissing: ChartData = {
        data: [
          { date: '2024-01', positive: 100 },
          { date: '2024-02', negative: 25 },
          { date: '2024-03', positive: 90, negative: 35 }
        ],
        xDataKey: 'date',
        seriesDataKey: [
          { name: '正面提及量', key: 'positive', type: 'bar' },
          { name: '负面提及量', key: 'negative', type: 'bar' }
        ]
      }

      const result = processChartData(dataWithMissing)

      // 验证缺失数据用 '-' 表示
      expect(result.seriesData[0].data).toEqual([100, '-', 90])
      expect(result.seriesData[1].data).toEqual(['-', 25, 35])
    })

    it('应该正确排序图例数据', () => {
      const dataWithLegend: ChartData = {
        data: [{ date: '2024-01', positive: 100, neutral: 50, negative: 30 }],
        xDataKey: 'date',
        seriesDataKey: [
          { name: '负面提及量', key: 'negative', type: 'bar' },
          { name: '中性提及量', key: 'neutral', type: 'bar' },
          { name: '正面提及量', key: 'positive', type: 'bar' },
          { name: '其他数据', key: 'other', type: 'bar' }
        ]
      }

      const result = processChartData(dataWithLegend)

      // 验证图例按指定顺序排序
      const expectedOrder = ['正面提及量', '中性提及量', '负面提及量', '其他数据']
      expect(result.legendData).toEqual(expectedOrder)
    })
  })

  describe('formatAxisValue', () => {
    it('应该正确格式化负面提及率', () => {
      expect(formatAxisValue(0.15, '负面提及率')).toBe('15.0%')
      expect(formatAxisValue(0.08, '负面提及率')).toBe('8.0%')
      expect(formatAxisValue(0.001, '负面提及率')).toBe('0.1%')
      expect(formatAxisValue(1, '负面提及率')).toBe('100.0%')
    })

    it('应该正确格式化提及量', () => {
      expect(formatAxisValue(15000, '提及量')).toBe('1.5万')
      expect(formatAxisValue(*********, '提及量')).toBe('1.5亿')
      expect(formatAxisValue(1500, '提及量')).toBe('1.5K')
      expect(formatAxisValue(999, '提及量')).toBe('999')
    })

    it('应该正确处理其他Y轴名称', () => {
      expect(formatAxisValue(123, '用户数')).toBe('123')
      expect(formatAxisValue(456, undefined)).toBe('456')
      expect(formatAxisValue(789, '')).toBe('789')
    })
  })

  describe('makeDataUnit', () => {
    it('应该正确转换亿级单位', () => {
      expect(makeDataUnit(*********)).toBe('1.5亿')
      expect(makeDataUnit(*********)).toBe('1.0亿')
      expect(makeDataUnit(*********)).toBe('2.5亿')
      expect(makeDataUnit(1234567890)).toBe('12.3亿')
    })

    it('应该正确转换万级单位', () => {
      expect(makeDataUnit(15000)).toBe('1.5万')
      expect(makeDataUnit(10000)).toBe('1.0万')
      expect(makeDataUnit(25000)).toBe('2.5万')
      expect(makeDataUnit(99999)).toBe('10.0万')
    })

    it('应该正确转换千级单位', () => {
      expect(makeDataUnit(1500)).toBe('1.5K')
      expect(makeDataUnit(1000)).toBe('1.0K')
      expect(makeDataUnit(2500)).toBe('2.5K')
      expect(makeDataUnit(9999)).toBe('10.0K')
    })

    it('应该保持小数值不变', () => {
      expect(makeDataUnit(999)).toBe('999')
      expect(makeDataUnit(500)).toBe('500')
      expect(makeDataUnit(1)).toBe('1')
      expect(makeDataUnit(0)).toBe('0')
    })

    it('应该正确处理边界值', () => {
      expect(makeDataUnit(99999999)).toBe('10000.0万')
      expect(makeDataUnit(9999)).toBe('10.0K')
      expect(makeDataUnit(999)).toBe('999')
    })
  })

  describe('getChartColors', () => {
    it('应该返回正确的颜色数组', () => {
      const colors = getChartColors()

      expect(colors).toBeInstanceOf(Array)
      expect(colors.length).toBeGreaterThan(0)
      expect(colors[0]).toBe('#0077FF')
      expect(colors[1]).toBe('#3ED4A9')
      expect(colors[2]).toBe('#5D7092')
    })

    it('应该提供足够的颜色选项', () => {
      const colors = getChartColors()
      expect(colors.length).toBeGreaterThanOrEqual(10)
    })
  })

  describe('getGridConfig', () => {
    it('应该返回正确的网格配置', () => {
      const grid = getGridConfig()

      expect(grid).toHaveProperty('top', 55)
      expect(grid).toHaveProperty('right', 10)
      expect(grid).toHaveProperty('bottom', 40)
      expect(grid).toHaveProperty('left', 10)
      expect(grid).toHaveProperty('containLabel', true)
    })
  })

  describe('getYAxisConfig', () => {
    it('应该正确生成Y轴配置', () => {
      const config = getYAxisConfig('提及量')

      expect(config.name).toBe('提及量')
      expect(config.show).toBe(true)
      expect(config.axisLabel.show).toBe(true)
      expect(config.splitLine.show).toBe(true)
      expect(config.axisTick.show).toBe(false)
      expect(config.axisLine.show).toBe(false)
    })

    it('应该正确处理数组形式的Y轴名称', () => {
      const config = getYAxisConfig(['提及量', '体验值'], false)
      expect(config.name).toBe('提及量')

      const secondConfig = getYAxisConfig(['提及量', '体验值'], true)
      expect(secondConfig.name).toBe('体验值')
    })

    it('应该包含格式化函数', () => {
      const config = getYAxisConfig('负面提及率')
      expect(config.axisLabel.formatter).toBeInstanceOf(Function)
    })
  })

  describe('getXAxisConfig', () => {
    it('应该正确生成X轴配置', () => {
      const xdata = ['2024-01', '2024-02', '2024-03']
      const config = getXAxisConfig(xdata, '月份', false, '2024-02')

      expect(config.name).toBe('月份')
      expect(config.data).toEqual(xdata)
      expect(config.triggerEvent).toBe(true)
      expect(config.axisTick.show).toBe(false)
      expect(config.axisLine.show).toBe(false)
    })

    it('应该根据数据量调整标签旋转', () => {
      const longXdata = ['1', '2', '3', '4', '5', '6']
      const config = getXAxisConfig(longXdata, '序号', false, '3')

      expect(config.axisLabel.rotate).toBe(35)
    })

    it('应该在数据量少时不旋转标签', () => {
      const shortXdata = ['1', '2', '3']
      const config = getXAxisConfig(shortXdata, '序号', false, '2')

      expect(config.axisLabel.rotate).toBe(0)
    })
  })

  describe('getTooltipConfig', () => {
    it('应该返回基础提示框配置', () => {
      const config = getTooltipConfig()

      expect(config.trigger).toBe('axis')
      expect(config.borderColor).toBe('#0077FF')
      expect(config.padding).toBe(0)
      expect(config.axisPointer.type).toBe('cross')
    })

    it('应该支持自定义格式化函数', () => {
      const customFormatter = (params: any) => 'custom tooltip'
      const config = getTooltipConfig(customFormatter)

      expect(config.formatter).toBe(customFormatter)
    })
  })

  describe('getLegendConfig', () => {
    it('应该返回正确的图例配置', () => {
      const legendData = ['正面提及量', '负面提及量']
      const config = getLegendConfig(legendData)

      expect(config.show).toBe(true)
      expect(config.bottom).toBe(0)
      expect(config.itemWidth).toBe(14)
      expect(config.data).toEqual(legendData)
    })

    it('应该正确处理空图例数据', () => {
      const config = getLegendConfig([])

      expect(config.data).toEqual([])
      expect(config.show).toBe(true)
    })
  })
})
