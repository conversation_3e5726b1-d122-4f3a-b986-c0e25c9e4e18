import type { RouteRecordRaw } from 'vue-router'

/**
 * 菜单项接口定义
 */
export interface MenuItem {
  path: string
  name: string
  meta: {
    title: string
    icon?: string
    hidden?: boolean
    permission?: string
  }
  children?: MenuItem[]
}

/**
 * 过滤路由，只保留需要显示在菜单中的路由
 * @param routes 路由配置
 * @returns 过滤后的菜单项
 */
export const filterMenuRoutes = (routes: RouteRecordRaw[]): MenuItem[] => {
  const menuItems: MenuItem[] = []

  routes.forEach(route => {
    // 跳过不需要显示在菜单中的路由
    if (
      route.meta?.hidden ||
      route.path === '/' ||
      route.path === '/login' ||
      route.path === '/404'
    ) {
      return
    }

    const menuItem: MenuItem = {
      path: route.path,
      name: route.name as string,
      meta: {
        title: (route.meta?.title as string) || (route.name as string),
        icon: route.meta?.icon as string,
        hidden: route.meta?.hidden as boolean
      }
    }

    // 处理子路由
    if (route.children && route.children.length > 0) {
      const children = filterMenuRoutes(route.children)
      if (children.length > 0) {
        menuItem.children = children
      }
    }

    // 只有有组件或者有子菜单的路由才添加到菜单中
    if (route.component || (menuItem.children && menuItem.children.length > 0)) {
      menuItems.push(menuItem)
    }
  })

  return menuItems
}

/**
 * 从路由配置生成菜单数据
 * @param routes 路由配置
 * @param layoutName 布局路由名称，默认为 'Layout'
 * @returns 菜单项数组
 */
export const generateMenuFromRoutes = (
  routes: RouteRecordRaw[],
  layoutName: string = 'Layout'
): MenuItem[] => {
  // 查找布局路由
  const layoutRoute = routes.find(route => route.name === layoutName)

  if (layoutRoute && layoutRoute.children) {
    return filterMenuRoutes(layoutRoute.children)
  }

  return []
}

/**
 * 根据用户权限过滤菜单
 * @param menuItems 菜单项
 * @param userPermissions 用户权限列表
 * @returns 过滤后的菜单项
 */
export const filterMenuByPermissions = (
  menuItems: MenuItem[],
  userPermissions: string[]
): MenuItem[] => {
  return menuItems.filter(item => {
    // 检查当前菜单项权限
    const hasPermission = !item.meta.permission || userPermissions.includes(item.meta.permission)

    // 递归过滤子菜单
    if (item.children && item.children.length > 0) {
      item.children = filterMenuByPermissions(item.children, userPermissions)
    }

    return hasPermission && (!item.children || item.children.length > 0)
  })
}
