/**
 * 工具函数统一导出
 * 只保留项目实际需要的工具
 */

// 导出基础环境工具
export { isDev, isProd } from './env'

// 导出菜单工具函数
export { generateMenuFromRoutes, filterMenuByPermissions, type MenuItem } from './menu'

// 数据千分位显示
export const Thousandth = (num: number): string => {
  if (num != undefined) {
    const reg = /\d{1,3}(?=(\d{3})+$)/g
    return (num + '').replace(reg, '$&,')
  }
  return '-'
}

// 精确两位小数
export const toFixTwo = (data: number): string => {
  const result =
    parseFloat(data.toString()).toString() == 'NaN' ? '-' : parseFloat(data.toString()).toFixed(2)
  return result
}

/**
 * 格式化百分比数值
 * @param data 原始数值
 * @returns 格式化后的百分比字符串
 */
export const formatPercent = (data: number | string): string => {
  let numValue = parseFloat(data as string)
  if (isNaN(numValue)) {
    return '-'
  }
  // 保留两位小数
  numValue = Number(toFixTwo(numValue))
  // 添加千分位分隔符
  return Thousandth(numValue)
}

/**
 * 格式化日期
 * @param date 日期对象或日期字符串
 * @param format 格式化模板，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date: Date | string, format: string = 'YYYY-MM-DD HH:mm:ss'): string => {
  if (!date) return '-'

  const d = new Date(date)
  if (isNaN(d.getTime())) return '-'

  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')

  return format
    .replace('YYYY', year.toString())
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}
