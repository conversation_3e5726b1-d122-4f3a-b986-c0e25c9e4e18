/**
 * 设计规范工具函数
 * 提供设计规范相关的实用函数和常量
 */

import type { 
  BrandColor, 
  FunctionalColor, 
  TextColor, 
  FontSize, 
  Spacing, 
  BorderRadius, 
  Shadow,
  DesignTokens 
} from '@/types/design-tokens'

// 设计规范常量
export const DESIGN_TOKENS: DesignTokens = {
  brand: {
    primary: '#1677FF',
    secondary: '#0B457F'
  },
  functional: {
    error: '#FF5959',
    warning: '#FAB007',
    success: '#14CA64',
    info: '#1677FF',
    errorLight: '#FFD1C9',
    warningLight: '#FEF2B4',
    successLight: '#B3F2C6',
    infoLight: '#BDE2FF'
  },
  text: {
    primary: '#1F2733',
    secondary: '#5F6A7A',
    tertiary: '#929AA6',
    placeholder: '#C9CED6',
    link: '#1677FF',
    danger: '#FF5959',
    warning: '#FAB007',
    success: '#14CA64'
  },
  base: {
    borderDark: '#DFE2E8',
    borderRegular: '#EBEDF0',
    bgRegular: '#F2F4F7',
    bgLight: '#F5F7FA',
    disabled: 'rgba(31, 39, 51, 0.2)',
    mask: 'rgba(31, 39, 51, 0.3)'
  },
  neutral: {
    black: '#000000',
    900: '#1F2733',
    700: '#5F6A7A',
    500: '#929AA6',
    400: '#C9CED6',
    300: '#DFE2E8',
    200: '#EBEDF0',
    100: '#F2F4F7',
    50: '#F5F7FA',
    white: '#FFFFFF'
  },
  chart: [
    '#1677FF', '#0AADFF', '#28C7C7', '#14CA64', '#FACE0C',
    '#FAB007', '#FE7840', '#FF5959', '#9772FB', '#6675FF', '#7298D0'
  ],
  font: {
    family: 'ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
    size: {
      h1: '32px',
      h2: '24px',
      h3: '20px',
      h4: '16px',
      body: '14px',
      caption: '12px'
    },
    lineHeight: {
      h1: '40px',
      h2: '32px',
      h3: '28px',
      h4: '24px',
      body: '22px',
      caption: '20px'
    },
    weight: {
      thin: 200,
      normal: 400,
      medium: 500,
      semibold: 600
    }
  },
  spacing: {
    0: '0',
    1: '2px',
    2: '4px',
    4: '8px',
    6: '12px',
    8: '16px',
    10: '20px',
    12: '24px',
    16: '32px',
    24: '48px'
  },
  borderRadius: {
    s: '2px',
    m: '4px',
    l: '6px',
    xl: '8px',
    c: '50%'
  },
  shadow: {
    s: '0 1px 2px 0 rgba(216, 39, 20, 0.1)',
    mLeft: '4px 0 8px 0 rgba(216, 39, 20, 0.1)',
    mBottom: '0 4px 8px 0 rgba(216, 39, 20, 0.1)',
    l: '0 8px 16px 6px rgba(216, 39, 20, 0.1)',
    xl: '0 12px 24px 8px rgba(216, 39, 20, 0.08)'
  }
}

/**
 * 获取品牌色
 * @param color 品牌色类型
 * @returns 颜色值
 */
export function getBrandColor(color: BrandColor): string {
  return DESIGN_TOKENS.brand[color]
}

/**
 * 获取功能色
 * @param color 功能色类型
 * @returns 颜色值
 */
export function getFunctionalColor(color: FunctionalColor): string {
  return DESIGN_TOKENS.functional[color]
}

/**
 * 获取文字色
 * @param color 文字色类型
 * @returns 颜色值
 */
export function getTextColor(color: TextColor): string {
  return DESIGN_TOKENS.text[color]
}

/**
 * 获取间距值
 * @param spacing 间距类型
 * @returns 间距值
 */
export function getSpacing(spacing: Spacing): string {
  return DESIGN_TOKENS.spacing[spacing]
}

/**
 * 获取字体大小和行高
 * @param size 字体大小类型
 * @returns 包含字体大小和行高的对象
 */
export function getFontSize(size: FontSize): { fontSize: string; lineHeight: string } {
  return {
    fontSize: DESIGN_TOKENS.font.size[size],
    lineHeight: DESIGN_TOKENS.font.lineHeight[size]
  }
}

/**
 * 获取圆角值
 * @param radius 圆角类型
 * @returns 圆角值
 */
export function getBorderRadius(radius: BorderRadius): string {
  return DESIGN_TOKENS.borderRadius[radius]
}

/**
 * 获取阴影值
 * @param shadow 阴影类型
 * @returns 阴影值
 */
export function getShadow(shadow: Shadow): string {
  return DESIGN_TOKENS.shadow[shadow]
}

/**
 * 获取图表颜色
 * @param index 颜色索引 (0-10)
 * @returns 颜色值
 */
export function getChartColor(index: number): string {
  return DESIGN_TOKENS.chart[index % DESIGN_TOKENS.chart.length]
}

/**
 * 获取图表颜色数组
 * @param count 需要的颜色数量
 * @returns 颜色数组
 */
export function getChartColors(count: number): string[] {
  const colors: string[] = []
  for (let i = 0; i < count; i++) {
    colors.push(getChartColor(i))
  }
  return colors
}

/**
 * 生成 CSS 变量名
 * @param path 变量路径，如 'text.primary'
 * @returns CSS 变量名，如 '--text-primary'
 */
export function getCSSVariableName(path: string): string {
  return `--${path.replace(/\./g, '-')}`
}

/**
 * 获取 CSS 变量值
 * @param path 变量路径
 * @returns CSS 变量引用，如 'var(--text-primary)'
 */
export function getCSSVariable(path: string): string {
  return `var(${getCSSVariableName(path)})`
}

/**
 * 根据背景色自动选择合适的文字颜色
 * @param backgroundColor 背景色
 * @returns 文字颜色
 */
export function getContrastTextColor(backgroundColor: string): string {
  // 简单的对比度计算，实际项目中可能需要更复杂的算法
  const hex = backgroundColor.replace('#', '')
  const r = parseInt(hex.substr(0, 2), 16)
  const g = parseInt(hex.substr(2, 2), 16)
  const b = parseInt(hex.substr(4, 2), 16)
  const brightness = (r * 299 + g * 587 + b * 114) / 1000
  
  return brightness > 128 ? getTextColor('primary') : DESIGN_TOKENS.neutral.white
}

/**
 * 生成响应式间距
 * @param base 基础间距
 * @param scale 缩放比例
 * @returns 响应式间距对象
 */
export function getResponsiveSpacing(base: Spacing, scale: { sm?: number; md?: number; lg?: number } = {}) {
  const baseValue = parseInt(getSpacing(base))
  return {
    base: getSpacing(base),
    sm: `${baseValue * (scale.sm || 0.75)}px`,
    md: `${baseValue * (scale.md || 1)}px`,
    lg: `${baseValue * (scale.lg || 1.25)}px`
  }
}

/**
 * 验证颜色值是否符合设计规范
 * @param color 颜色值
 * @returns 是否符合规范
 */
export function isValidDesignColor(color: string): boolean {
  const allColors = [
    ...Object.values(DESIGN_TOKENS.brand),
    ...Object.values(DESIGN_TOKENS.functional),
    ...Object.values(DESIGN_TOKENS.text),
    ...Object.values(DESIGN_TOKENS.base),
    ...Object.values(DESIGN_TOKENS.neutral),
    ...DESIGN_TOKENS.chart
  ]
  return allColors.includes(color)
}

/**
 * 生成主题配置
 * @param overrides 覆盖的配置
 * @returns 完整的主题配置
 */
export function createTheme(overrides: Partial<DesignTokens> = {}): DesignTokens {
  return {
    ...DESIGN_TOKENS,
    ...overrides,
    brand: { ...DESIGN_TOKENS.brand, ...overrides.brand },
    functional: { ...DESIGN_TOKENS.functional, ...overrides.functional },
    text: { ...DESIGN_TOKENS.text, ...overrides.text },
    base: { ...DESIGN_TOKENS.base, ...overrides.base },
    neutral: { ...DESIGN_TOKENS.neutral, ...overrides.neutral },
    font: {
      ...DESIGN_TOKENS.font,
      ...overrides.font,
      size: { ...DESIGN_TOKENS.font.size, ...overrides.font?.size },
      lineHeight: { ...DESIGN_TOKENS.font.lineHeight, ...overrides.font?.lineHeight },
      weight: { ...DESIGN_TOKENS.font.weight, ...overrides.font?.weight }
    },
    spacing: { ...DESIGN_TOKENS.spacing, ...overrides.spacing },
    borderRadius: { ...DESIGN_TOKENS.borderRadius, ...overrides.borderRadius },
    shadow: { ...DESIGN_TOKENS.shadow, ...overrides.shadow }
  }
}
