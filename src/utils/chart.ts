// 此文件保留为全局通用图表工具函数入口，BarAndPointChart专用逻辑已迁移到组件目录下。

// 目前无全局通用函数，后续如有可在此补充。

/**
 * 图表工具函数
 */
import type { ChartData, ChartSeries, ProcessedChartData } from '@/types/chart'

/**
 * 处理图表数据
 * @param data 原始数据
 * @returns 处理后的数据
 */
export function processChartData(data: ChartData): ProcessedChartData {
  const { data: rawData, xDataKey, seriesDataKey } = data
  const xDataArr: string[] = []
  const legendData: string[] = []

  // 初始化系列数据
  seriesDataKey.forEach(series => {
    legendData.push(series.name)
    series.itemStyle = {
      borderRadius: 3,
      borderColor: '#fff',
      borderWidth: 0.5
    }
    series.barMaxWidth = 25
    series.data = {}
  })

  // 提取X轴数据并构建系列数据
  rawData.forEach(item => {
    const xdata = item[xDataKey]
    if (xDataArr.indexOf(xdata) === -1) {
      xDataArr.push(xdata)
    }

    seriesDataKey.forEach(series => {
      series.data[xdata] = item[series.key]
    })
  })

  // 处理系列数据格式
  seriesDataKey.forEach(series => {
    const each: any[] = []

    xDataArr.forEach(xdata => {
      let eachData = series.data[xdata]
      if (eachData === undefined) eachData = '-'

      // 处理散点图/线图样式
      if (series.type === 'scatter' || series.type === 'line') {
        series.type = 'line'
        series.symbol = 'circle'
        series.smooth = true
        series.symbolSize = 9
        series.color = '#5D7092'
        series.lineStyle = {
          type: 'dashed',
          color: '#5D7092'
        }
        series.itemStyle = {
          borderWidth: 2,
          color: 'rgba(93, 112, 146, .3)',
          borderColor: '#5D7092'
        }
      }

      each.push(eachData)
    })

    series.data = each
  })

  // 图例数据排序
  const legendDataSort = ['正面提及量', '中性提及量', '负面提及量']
  legendData.sort((a, b) => {
    const indexa = legendDataSort.indexOf(a) === -1 ? 100 : legendDataSort.indexOf(a)
    const indexb = legendDataSort.indexOf(b) === -1 ? 100 : legendDataSort.indexOf(b)
    return indexa - indexb
  })

  return {
    xDataArr,
    seriesData: seriesDataKey,
    legendData
  }
}

/**
 * 格式化数值
 * @param value 数值
 * @param yAxisName Y轴名称
 * @returns 格式化后的字符串
 */
export function formatAxisValue(value: number, yAxisName?: string): string {
  if (yAxisName === '负面提及率') {
    return `${(value * 100).toFixed(1)}%`
  } else if (yAxisName === '提及量') {
    return makeDataUnit(value)
  }
  return value.toString()
}

/**
 * 数据单位转换
 * @param value 数值
 * @returns 带单位的字符串
 */
export function makeDataUnit(value: number): string {
  if (value >= *********) {
    return `${(value / *********).toFixed(1)}亿`
  } else if (value >= 10000) {
    return `${(value / 10000).toFixed(1)}万`
  } else if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}K`
  }
  return value.toString()
}

/**
 * 生成图表颜色配置
 * @returns 颜色数组
 */
export function getChartColors(): string[] {
  return [
    '#0077FF',
    '#3ED4A9',
    '#5D7092',
    '#FFC157',
    '#7163FD',
    '#95D8F2',
    '#BA70CA',
    '#FA9C78',
    '#11999C',
    '#FEBAD6'
  ]
}

/**
 * 生成网格配置
 * @returns 网格配置对象
 */
export function getGridConfig() {
  return {
    top: 55,
    right: 10,
    bottom: 40,
    left: 10,
    containLabel: true
  }
}

/**
 * 生成Y轴配置
 * @param yAxisName Y轴名称
 * @param isSecond 是否为第二个Y轴
 * @returns Y轴配置对象
 */
export function getYAxisConfig(yAxisName: string | string[], isSecond = false) {
  const name = Array.isArray(yAxisName) ? yAxisName[isSecond ? 1 : 0] : yAxisName

  return {
    name,
    show: true,
    axisLabel: {
      show: true,
      formatter: (value: number) => formatAxisValue(value, name)
    },
    splitLine: {
      show: true,
      lineStyle: { color: '#F0F0F0' }
    },
    splitArea: {
      interval: 1,
      show: true,
      areaStyle: {
        color: ['rgba(255, 255, 255, 0)', 'rgba(250, 250, 250, 1)']
      }
    },
    axisTick: { show: false },
    axisLine: { show: false },
    nameTextStyle: {
      color: 'rgba(0,0,0,0.45)'
    }
  }
}

/**
 * 生成X轴配置
 * @param xdata X轴数据
 * @param xAxisName X轴名称
 * @param transverse 是否横向显示
 * @param xActiveName 当前激活的X轴名称
 * @returns X轴配置对象
 */
export function getXAxisConfig(
  xdata: string[],
  xAxisName: string,
  transverse: boolean,
  xActiveName: string
) {
  return {
    name: xAxisName,
    axisLabel: {
      margin: 15,
      textStyle: {},
      rotate: xdata.length >= 5 ? 35 : 0,
      width: 100,
      formatter: (params: string) => {
        return `{${xActiveName === params ? 'active' : 'normal'}|${params}}`
      },
      rich: {
        active: {
          fontSize: 16,
          color: '#0077ff',
          fontWeight: 'bold'
        },
        normal: {
          fontSize: 14,
          color: 'rgba(0, 0, 0, 0.45)',
          fontWeight: '400'
        }
      }
    },
    triggerEvent: true,
    type: transverse ? '' : 'category',
    data: transverse ? undefined : xdata,
    axisPointer: { type: 'shadow' },
    splitLine: { show: true },
    axisTick: { show: false },
    axisLine: { show: false },
    nameTextStyle: { color: 'rgba(0,0,0,0.45)' }
  }
}

/**
 * 生成工具提示配置
 * @param tooltipFormatter 自定义格式化函数
 * @returns 工具提示配置对象
 */
export function getTooltipConfig(tooltipFormatter?: (params: any) => string) {
  const config: any = {
    trigger: 'axis' as const,
    borderColor: '#0077FF',
    padding: 0,
    axisPointer: {
      type: 'cross' as const,
      shadowStyle: {
        color: 'rgba(41, 148, 255, 0.1)'
      }
    }
  }

  if (tooltipFormatter) {
    config.formatter = tooltipFormatter
  }

  return config
}

/**
 * 生成图例配置
 * @param legendData 图例数据
 * @returns 图例配置对象
 */
export function getLegendConfig(legendData: string[]) {
  return {
    show: true,
    bottom: 0,
    itemWidth: 14,
    data: legendData
  }
}
