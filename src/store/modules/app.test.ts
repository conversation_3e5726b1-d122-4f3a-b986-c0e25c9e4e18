import { describe, it, expect, beforeEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useAppStore } from './app'

describe('useAppStore', () => {
  beforeEach(() => {
    // 为每个测试创建新的Pinia实例
    setActivePinia(createPinia())
  })

  describe('初始状态', () => {
    it('应该具有正确的初始状态', () => {
      const store = useAppStore()

      expect(store.isCollapse).toBe(false)
      expect(store.user).toEqual({
        id: 1,
        userName: '管理员',
        userAccount: 'admin'
      })
    })
  })

  describe('getters', () => {
    it('userInfo getter应该返回当前用户信息', () => {
      const store = useAppStore()

      expect(store.userInfo).toEqual({
        id: 1,
        userName: '管理员',
        userAccount: 'admin'
      })
    })

    it('userInfo getter应该在用户信息变化时反映最新值', () => {
      const store = useAppStore()

      // 修改用户信息
      store.setUser({
        userName: '新用户',
        email: '<EMAIL>'
      })

      expect(store.userInfo).toEqual({
        id: 1,
        userName: '新用户',
        userAccount: 'admin',
        email: '<EMAIL>'
      })
    })
  })

  describe('actions', () => {
    describe('setIsCollapse', () => {
      it('应该设置折叠状态为true', () => {
        const store = useAppStore()

        store.setIsCollapse(true)

        expect(store.isCollapse).toBe(true)
      })

      it('应该设置折叠状态为false', () => {
        const store = useAppStore()

        // 先设置为true
        store.setIsCollapse(true)
        expect(store.isCollapse).toBe(true)

        // 再设置为false
        store.setIsCollapse(false)
        expect(store.isCollapse).toBe(false)
      })
    })

    describe('toggleCollapse', () => {
      it('应该切换折叠状态从false到true', () => {
        const store = useAppStore()

        // 初始状态为false
        expect(store.isCollapse).toBe(false)

        store.toggleCollapse()

        expect(store.isCollapse).toBe(true)
      })

      it('应该切换折叠状态从true到false', () => {
        const store = useAppStore()

        // 先设置为true
        store.setIsCollapse(true)
        expect(store.isCollapse).toBe(true)

        store.toggleCollapse()

        expect(store.isCollapse).toBe(false)
      })

      it('应该支持多次切换', () => {
        const store = useAppStore()

        // 初始状态: false
        expect(store.isCollapse).toBe(false)

        // 第一次切换: false -> true
        store.toggleCollapse()
        expect(store.isCollapse).toBe(true)

        // 第二次切换: true -> false
        store.toggleCollapse()
        expect(store.isCollapse).toBe(false)

        // 第三次切换: false -> true
        store.toggleCollapse()
        expect(store.isCollapse).toBe(true)
      })
    })

    describe('setUser', () => {
      it('应该更新用户部分信息', () => {
        const store = useAppStore()

        store.setUser({
          userName: '新用户名',
          email: '<EMAIL>'
        })

        expect(store.user).toEqual({
          id: 1,
          userName: '新用户名',
          userAccount: 'admin',
          email: '<EMAIL>'
        })
      })

      it('应该更新用户头像', () => {
        const store = useAppStore()

        store.setUser({
          avatar: 'https://example.com/avatar.jpg'
        })

        expect(store.user.avatar).toBe('https://example.com/avatar.jpg')
        // 其他字段应该保持不变
        expect(store.user.userName).toBe('管理员')
        expect(store.user.userAccount).toBe('admin')
      })

      it('应该支持覆盖现有属性', () => {
        const store = useAppStore()

        // 第一次设置
        store.setUser({
          userName: '第一个用户名',
          email: '<EMAIL>'
        })

        expect(store.user.userName).toBe('第一个用户名')
        expect(store.user.email).toBe('<EMAIL>')

        // 第二次设置，覆盖用户名
        store.setUser({
          userName: '第二个用户名',
          avatar: 'avatar.jpg'
        })

        expect(store.user.userName).toBe('第二个用户名')
        expect(store.user.email).toBe('<EMAIL>') // 保持不变
        expect(store.user.avatar).toBe('avatar.jpg')
      })

      it('应该支持更新用户ID', () => {
        const store = useAppStore()

        store.setUser({
          id: 123,
          userName: '特定用户'
        })

        expect(store.user.id).toBe(123)
        expect(store.user.userName).toBe('特定用户')
      })
    })

    describe('logout', () => {
      it('应该重置用户信息到空状态', () => {
        const store = useAppStore()

        // 先设置一些用户信息
        store.setUser({
          userName: '已登录用户',
          email: '<EMAIL>',
          avatar: 'avatar.jpg'
        })

        // 验证信息已设置
        expect(store.user.userName).toBe('已登录用户')
        expect(store.user.email).toBe('<EMAIL>')
        expect(store.user.avatar).toBe('avatar.jpg')

        // 执行登出
        store.logout()

        // 验证用户信息已重置
        expect(store.user).toEqual({
          id: 0,
          userName: '',
          userAccount: ''
        })
      })

      it('登出后不应该影响折叠状态', () => {
        const store = useAppStore()

        // 设置折叠状态
        store.setIsCollapse(true)
        expect(store.isCollapse).toBe(true)

        // 执行登出
        store.logout()

        // 折叠状态应该保持不变
        expect(store.isCollapse).toBe(true)
      })
    })
  })

  describe('状态持久化', () => {
    it('不同store实例应该独立', () => {
      const store1 = useAppStore()
      const store2 = useAppStore()

      // 在同一个Pinia实例中，应该是同一个store
      expect(store1).toBe(store2)
    })

    it('状态变化应该响应式更新', () => {
      const store = useAppStore()
      const userInfo = store.userInfo

      // 修改用户信息
      store.setUser({ userName: '响应式用户' })

      // getter应该返回新值
      expect(store.userInfo.userName).toBe('响应式用户')
      // 但不是同一个对象引用（因为使用了计算属性）
      expect(store.userInfo).not.toBe(userInfo)
    })
  })
})
