import { defineStore } from 'pinia'
import {
  UserStatus,
  RoleStatus,
  type User,
  type Role,
  type Permission,
  type Menu,
  type UserQueryParams,
  type RoleQueryParams,
  type CreateUserRequest,
  type UpdateUserRequest,
  type CreateRoleRequest,
  type UpdateRoleRequest,
  type PageResult
} from '@/types/system'
import UserAPI from '@/api/system/user'
import RoleAPI, { PermissionAPI, MenuAPI } from '@/api/system/role'
import { ElMessage } from 'element-plus'

interface SystemState {
  // 用户管理相关状态
  users: User[]
  userTotal: number
  userLoading: boolean
  currentUser: User | null
  userQueryParams: UserQueryParams

  // 角色管理相关状态
  roles: Role[]
  roleTotal: number
  roleLoading: boolean
  currentRole: Role | null
  roleQueryParams: RoleQueryParams

  // 权限管理相关状态
  permissions: Permission[]
  permissionTree: Permission[]
  permissionLoading: boolean

  // 菜单管理相关状态
  menus: Menu[]
  menuTree: Menu[]
  menuLoading: boolean

  // 统计信息
  userStats: Record<string, number>
}

export const useSystemStore = defineStore('system', {
  state: (): SystemState => ({
    // 用户管理状态
    users: [],
    userTotal: 0,
    userLoading: false,
    currentUser: null,
    userQueryParams: {
      page: 1,
      pageSize: 10
    },

    // 角色管理状态
    roles: [],
    roleTotal: 0,
    roleLoading: false,
    currentRole: null,
    roleQueryParams: {
      page: 1,
      pageSize: 10
    },

    // 权限管理状态
    permissions: [],
    permissionTree: [],
    permissionLoading: false,

    // 菜单管理状态
    menus: [],
    menuTree: [],
    menuLoading: false,

    // 统计信息
    userStats: {}
  }),

  getters: {
    // 用户相关计算属性
    activeUsers: (state): User[] =>
      state.users.filter((user: User) => user.status === UserStatus.ACTIVE),
    disabledUsers: (state): User[] =>
      state.users.filter((user: User) => user.status === UserStatus.DISABLED),
    lockedUsers: (state): User[] =>
      state.users.filter((user: User) => user.status === UserStatus.LOCKED),

    // 角色相关计算属性
    enabledRoles: (state): Role[] =>
      state.roles.filter((role: Role) => role.status === RoleStatus.ENABLE),
    disabledRoles: (state): Role[] =>
      state.roles.filter((role: Role) => role.status === RoleStatus.DISABLE),

    // 按级别分组的角色
    rolesByLevel: (state): Record<number, Role[]> => {
      const groups: Record<number, Role[]> = {}
      state.roles.forEach((role: Role) => {
        if (!groups[role.level]) {
          groups[role.level] = []
        }
        groups[role.level].push(role)
      })
      return groups
    },

    // 权限树结构
    permissionTreeData: (state): Permission[] => state.permissionTree,

    // 菜单树结构
    menuTreeData: (state): Menu[] => state.menuTree
  },

  actions: {
    // ==================== 用户管理操作 ====================

    /**
     * 获取用户列表
     */
    async fetchUsers(params?: Partial<UserQueryParams>) {
      this.userLoading = true
      try {
        const queryParams = { ...this.userQueryParams, ...params }
        this.userQueryParams = queryParams

        const result = await UserAPI.getUserList(queryParams)
        this.users = result.data
        this.userTotal = result.total

        return result
      } catch (error) {
        console.error('获取用户列表失败:', error)
        ElMessage.error('获取用户列表失败')
        throw error
      } finally {
        this.userLoading = false
      }
    },

    /**
     * 根据ID获取用户详情
     */
    async fetchUserById(id: number) {
      try {
        const user = await UserAPI.getUserById(id)
        this.currentUser = user
        return user
      } catch (error) {
        console.error('获取用户详情失败:', error)
        ElMessage.error('获取用户详情失败')
        throw error
      }
    },

    /**
     * 创建用户
     */
    async createUser(userData: CreateUserRequest) {
      try {
        const newUser = await UserAPI.createUser(userData)

        // 刷新用户列表
        await this.fetchUsers()

        ElMessage.success('创建用户成功')
        return newUser
      } catch (error: any) {
        console.error('创建用户失败:', error)
        ElMessage.error(error.message || '创建用户失败')
        throw error
      }
    },

    /**
     * 更新用户
     */
    async updateUser(id: number, userData: UpdateUserRequest) {
      try {
        const updatedUser = await UserAPI.updateUser(id, userData)

        // 更新本地状态
        const index = this.users.findIndex(user => user.id === id)
        if (index !== -1) {
          this.users[index] = updatedUser
        }

        if (this.currentUser?.id === id) {
          this.currentUser = updatedUser
        }

        ElMessage.success('更新用户成功')
        return updatedUser
      } catch (error: any) {
        console.error('更新用户失败:', error)
        ElMessage.error(error.message || '更新用户失败')
        throw error
      }
    },

    /**
     * 删除用户
     */
    async deleteUser(id: number) {
      try {
        await UserAPI.deleteUser(id)

        // 从本地状态中移除
        this.users = this.users.filter(user => user.id !== id)
        this.userTotal -= 1

        if (this.currentUser?.id === id) {
          this.currentUser = null
        }

        ElMessage.success('删除用户成功')
      } catch (error: any) {
        console.error('删除用户失败:', error)
        ElMessage.error(error.message || '删除用户失败')
        throw error
      }
    },

    /**
     * 批量删除用户
     */
    async batchDeleteUsers(ids: number[]) {
      try {
        await UserAPI.batchDeleteUsers(ids)

        // 从本地状态中移除
        this.users = this.users.filter(user => !ids.includes(user.id))
        this.userTotal -= ids.length

        ElMessage.success(`成功删除 ${ids.length} 个用户`)
      } catch (error: any) {
        console.error('批量删除用户失败:', error)
        ElMessage.error(error.message || '批量删除用户失败')
        throw error
      }
    },

    /**
     * 重置用户密码
     */
    async resetUserPassword(id: number, newPassword?: string) {
      try {
        await UserAPI.resetPassword(id, newPassword)
        ElMessage.success('重置密码成功')
      } catch (error: any) {
        console.error('重置密码失败:', error)
        ElMessage.error(error.message || '重置密码失败')
        throw error
      }
    },

    /**
     * 更新用户状态
     */
    async updateUserStatus(id: number, status: UserStatus) {
      try {
        await UserAPI.updateUserStatus(id, status)

        // 更新本地状态
        const user = this.users.find(user => user.id === id)
        if (user) {
          user.status = status
          user.updateTime = new Date()
        }

        ElMessage.success('更新用户状态成功')
      } catch (error: any) {
        console.error('更新用户状态失败:', error)
        ElMessage.error(error.message || '更新用户状态失败')
        throw error
      }
    },

    /**
     * 获取用户统计信息
     */
    async fetchUserStats() {
      try {
        this.userStats = await UserAPI.getUserStats()
        return this.userStats
      } catch (error) {
        console.error('获取用户统计失败:', error)
        throw error
      }
    },

    // ==================== 角色管理操作 ====================

    /**
     * 获取角色列表
     */
    async fetchRoles(params?: Partial<RoleQueryParams>) {
      this.roleLoading = true
      try {
        const queryParams = { ...this.roleQueryParams, ...params }
        this.roleQueryParams = queryParams

        const result = await RoleAPI.getRoleList(queryParams)
        this.roles = result.data
        this.roleTotal = result.total

        return result
      } catch (error) {
        console.error('获取角色列表失败:', error)
        ElMessage.error('获取角色列表失败')
        throw error
      } finally {
        this.roleLoading = false
      }
    },

    /**
     * 获取所有角色（不分页）
     */
    async fetchAllRoles() {
      try {
        this.roles = await RoleAPI.getAllRoles()
        return this.roles
      } catch (error) {
        console.error('获取所有角色失败:', error)
        ElMessage.error('获取所有角色失败')
        throw error
      }
    },

    /**
     * 根据ID获取角色详情
     */
    async fetchRoleById(id: number) {
      try {
        const role = await RoleAPI.getRoleById(id)
        this.currentRole = role
        return role
      } catch (error) {
        console.error('获取角色详情失败:', error)
        ElMessage.error('获取角色详情失败')
        throw error
      }
    },

    /**
     * 创建角色
     */
    async createRole(roleData: CreateRoleRequest) {
      try {
        const newRole = await RoleAPI.createRole(roleData)

        // 刷新角色列表
        await this.fetchRoles()

        ElMessage.success('创建角色成功')
        return newRole
      } catch (error: any) {
        console.error('创建角色失败:', error)
        ElMessage.error(error.message || '创建角色失败')
        throw error
      }
    },

    /**
     * 更新角色
     */
    async updateRole(id: number, roleData: UpdateRoleRequest) {
      try {
        const updatedRole = await RoleAPI.updateRole(id, roleData)

        // 更新本地状态
        const index = this.roles.findIndex(role => role.id === id)
        if (index !== -1) {
          this.roles[index] = updatedRole
        }

        if (this.currentRole?.id === id) {
          this.currentRole = updatedRole
        }

        ElMessage.success('更新角色成功')
        return updatedRole
      } catch (error: any) {
        console.error('更新角色失败:', error)
        ElMessage.error(error.message || '更新角色失败')
        throw error
      }
    },

    /**
     * 删除角色
     */
    async deleteRole(id: number) {
      try {
        await RoleAPI.deleteRole(id)

        // 从本地状态中移除
        this.roles = this.roles.filter(role => role.id !== id)
        this.roleTotal -= 1

        if (this.currentRole?.id === id) {
          this.currentRole = null
        }

        ElMessage.success('删除角色成功')
      } catch (error: any) {
        console.error('删除角色失败:', error)
        ElMessage.error(error.message || '删除角色失败')
        throw error
      }
    },

    /**
     * 批量删除角色
     */
    async batchDeleteRoles(ids: number[]) {
      try {
        await RoleAPI.batchDeleteRoles(ids)

        // 从本地状态中移除
        this.roles = this.roles.filter(role => !ids.includes(role.id))
        this.roleTotal -= ids.length

        ElMessage.success(`成功删除 ${ids.length} 个角色`)
      } catch (error: any) {
        console.error('批量删除角色失败:', error)
        ElMessage.error(error.message || '批量删除角色失败')
        throw error
      }
    },

    /**
     * 更新角色状态
     */
    async updateRoleStatus(id: number, status: RoleStatus) {
      try {
        await RoleAPI.updateRoleStatus(id, status)

        // 更新本地状态
        const role = this.roles.find((role: Role) => role.id === id)
        if (role) {
          role.status = status
          role.updateTime = new Date()
        }

        ElMessage.success('更新角色状态成功')
      } catch (error: any) {
        console.error('更新角色状态失败:', error)
        ElMessage.error(error.message || '更新角色状态失败')
        throw error
      }
    },

    /**
     * 复制角色
     */
    async copyRole(id: number, newName: string, newCode: string) {
      try {
        const newRole = await RoleAPI.copyRole(id, newName, newCode)

        // 添加到本地状态
        this.roles.push(newRole)
        this.roleTotal += 1

        ElMessage.success('角色复制成功')
        return newRole
      } catch (error: any) {
        console.error('复制角色失败:', error)
        ElMessage.error(error.message || '复制角色失败')
        throw error
      }
    },

    /**
     * 为角色分配权限
     */
    async assignPermissions(roleId: number, permissionIds: number[]) {
      try {
        await RoleAPI.assignPermissions(roleId, permissionIds)

        // 刷新角色详情
        if (this.currentRole?.id === roleId) {
          await this.fetchRoleById(roleId)
        }

        ElMessage.success('分配权限成功')
      } catch (error: any) {
        console.error('分配权限失败:', error)
        ElMessage.error(error.message || '分配权限失败')
        throw error
      }
    },

    // ==================== 权限管理操作 ====================

    /**
     * 获取所有权限
     */
    async fetchPermissions() {
      this.permissionLoading = true
      try {
        this.permissions = await PermissionAPI.getAllPermissions()
        return this.permissions
      } catch (error) {
        console.error('获取权限列表失败:', error)
        ElMessage.error('获取权限列表失败')
        throw error
      } finally {
        this.permissionLoading = false
      }
    },

    /**
     * 获取权限树
     */
    async fetchPermissionTree() {
      this.permissionLoading = true
      try {
        this.permissionTree = await PermissionAPI.getPermissionTree()
        return this.permissionTree
      } catch (error) {
        console.error('获取权限树失败:', error)
        ElMessage.error('获取权限树失败')
        throw error
      } finally {
        this.permissionLoading = false
      }
    },

    // ==================== 菜单管理操作 ====================

    /**
     * 获取所有菜单
     */
    async fetchMenus() {
      this.menuLoading = true
      try {
        this.menus = await MenuAPI.getAllMenus()
        return this.menus
      } catch (error) {
        console.error('获取菜单列表失败:', error)
        ElMessage.error('获取菜单列表失败')
        throw error
      } finally {
        this.menuLoading = false
      }
    },

    /**
     * 获取菜单树
     */
    async fetchMenuTree() {
      this.menuLoading = true
      try {
        this.menuTree = await MenuAPI.getMenuTree()
        return this.menuTree
      } catch (error) {
        console.error('获取菜单树失败:', error)
        ElMessage.error('获取菜单树失败')
        throw error
      } finally {
        this.menuLoading = false
      }
    },

    // ==================== 通用操作 ====================

    /**
     * 重置用户查询参数
     */
    resetUserQuery() {
      this.userQueryParams = {
        page: 1,
        pageSize: 10
      }
    },

    /**
     * 重置角色查询参数
     */
    resetRoleQuery() {
      this.roleQueryParams = {
        page: 1,
        pageSize: 10
      }
    },

    /**
     * 清空当前用户
     */
    clearCurrentUser() {
      this.currentUser = null
    },

    /**
     * 清空当前角色
     */
    clearCurrentRole() {
      this.currentRole = null
    },

    /**
     * 初始化系统数据
     */
    async initSystemData() {
      try {
        await Promise.all([
          this.fetchUsers(),
          this.fetchRoles(),
          this.fetchPermissions(),
          this.fetchMenus(),
          this.fetchUserStats()
        ])
      } catch (error) {
        console.error('初始化系统数据失败:', error)
      }
    }
  }
})
