import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useSystemStore } from './system'
import { UserStatus, RoleStatus } from '@/types/system'

// Mock ElMessage
vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn()
  }
}))

// Mock API
vi.mock('@/api/system/user', () => ({
  default: {
    getUserList: vi.fn(),
    getUserById: vi.fn(),
    createUser: vi.fn(),
    updateUser: vi.fn(),
    deleteUser: vi.fn(),
    batchDeleteUsers: vi.fn()
  }
}))

vi.mock('@/api/system/role', () => ({
  default: {
    getRoleList: vi.fn(),
    createRole: vi.fn(),
    updateRole: vi.fn(),
    deleteRole: vi.fn()
  },
  PermissionAPI: {
    getPermissionList: vi.fn()
  },
  MenuAPI: {
    getMenuList: vi.fn()
  }
}))

describe('useSystemStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  describe('初始状态', () => {
    it('应该具有正确的初始状态', () => {
      const store = useSystemStore()

      // 用户管理状态
      expect(store.users).toEqual([])
      expect(store.userTotal).toBe(0)
      expect(store.userLoading).toBe(false)
      expect(store.currentUser).toBeNull()
      expect(store.userQueryParams).toEqual({
        page: 1,
        pageSize: 10
      })

      // 角色管理状态
      expect(store.roles).toEqual([])
      expect(store.roleTotal).toBe(0)
      expect(store.roleLoading).toBe(false)
      expect(store.currentRole).toBeNull()
      expect(store.roleQueryParams).toEqual({
        page: 1,
        pageSize: 10
      })

      // 权限管理状态
      expect(store.permissions).toEqual([])
      expect(store.permissionTree).toEqual([])
      expect(store.permissionLoading).toBe(false)

      // 菜单管理状态
      expect(store.menus).toEqual([])
      expect(store.menuTree).toEqual([])
      expect(store.menuLoading).toBe(false)

      // 统计信息
      expect(store.userStats).toEqual({})
    })
  })

  describe('getters', () => {
    it('activeUsers应该返回活跃用户', () => {
      const store = useSystemStore()

      // 模拟用户数据
      store.users = [
        { id: 1, username: 'user1', status: UserStatus.ACTIVE } as any,
        { id: 2, username: 'user2', status: UserStatus.DISABLED } as any,
        { id: 3, username: 'user3', status: UserStatus.ACTIVE } as any
      ]

      const activeUsers = store.activeUsers
      expect(activeUsers).toHaveLength(2)
      expect(activeUsers.every(user => user.status === UserStatus.ACTIVE)).toBe(true)
    })

    it('disabledUsers应该返回禁用用户', () => {
      const store = useSystemStore()

      store.users = [
        { id: 1, userName: 'user1', status: UserStatus.ACTIVE } as any,
        { id: 2, userName: 'user2', status: UserStatus.DISABLED } as any,
        { id: 3, userName: 'user3', status: UserStatus.DISABLED } as any
      ]

      const disabledUsers = store.disabledUsers
      expect(disabledUsers).toHaveLength(2)
      expect(disabledUsers.every(user => user.status === UserStatus.DISABLED)).toBe(true)
    })

    it('lockedUsers应该返回锁定用户', () => {
      const store = useSystemStore()

      store.users = [
        { id: 1, userName: 'user1', status: UserStatus.ACTIVE } as any,
        { id: 2, userName: 'user2', status: UserStatus.LOCKED } as any,
        { id: 3, userName: 'user3', status: UserStatus.LOCKED } as any
      ]

      const lockedUsers = store.lockedUsers
      expect(lockedUsers).toHaveLength(2)
      expect(lockedUsers.every(user => user.status === UserStatus.LOCKED)).toBe(true)
    })

    it('enabledRoles应该返回启用的角色', () => {
      const store = useSystemStore()

      store.roles = [
        { id: 1, name: 'role1', status: RoleStatus.ENABLE } as any,
        { id: 2, name: 'role2', status: RoleStatus.DISABLE } as any,
        { id: 3, name: 'role3', status: RoleStatus.ENABLE } as any
      ]

      const enabledRoles = store.enabledRoles
      expect(enabledRoles).toHaveLength(2)
      expect(enabledRoles.every(role => role.status === RoleStatus.ENABLE)).toBe(true)
    })

    it('disabledRoles应该返回禁用的角色', () => {
      const store = useSystemStore()

      store.roles = [
        { id: 1, name: 'role1', status: RoleStatus.ENABLE } as any,
        { id: 2, name: 'role2', status: RoleStatus.DISABLE } as any,
        { id: 3, name: 'role3', status: RoleStatus.DISABLE } as any
      ]

      const disabledRoles = store.disabledRoles
      expect(disabledRoles).toHaveLength(2)
      expect(disabledRoles.every(role => role.status === RoleStatus.DISABLE)).toBe(true)
    })

    it('rolesByLevel应该按级别分组角色', () => {
      const store = useSystemStore()

      store.roles = [
        { id: 1, name: 'role1', level: 1 } as any,
        { id: 2, name: 'role2', level: 2 } as any,
        { id: 3, name: 'role3', level: 1 } as any,
        { id: 4, name: 'role4', level: 3 } as any
      ]

      const rolesByLevel = store.rolesByLevel
      expect(rolesByLevel[1]).toHaveLength(2)
      expect(rolesByLevel[2]).toHaveLength(1)
      expect(rolesByLevel[3]).toHaveLength(1)
      expect(rolesByLevel[1][0].name).toBe('role1')
      expect(rolesByLevel[1][1].name).toBe('role3')
    })

    it('permissionTreeData应该返回权限树', () => {
      const store = useSystemStore()

      const mockPermissionTree = [{ id: 1, name: 'permission1', children: [] }]
      store.permissionTree = mockPermissionTree as any

      expect(store.permissionTreeData).toStrictEqual(mockPermissionTree)
    })

    it('menuTreeData应该返回菜单树', () => {
      const store = useSystemStore()

      const mockMenuTree = [{ id: 1, name: 'menu1', children: [] }]
      store.menuTree = mockMenuTree as any

      expect(store.menuTreeData).toStrictEqual(mockMenuTree)
    })
  })

  describe('状态管理', () => {
    it('应该正确更新用户列表', () => {
      const store = useSystemStore()

      const mockUsers = [
        { id: 1, username: 'user1', status: UserStatus.ACTIVE },
        { id: 2, username: 'user2', status: UserStatus.DISABLED }
      ]

      store.users = mockUsers as any
      store.userTotal = 2

      expect(store.users).toHaveLength(2)
      expect(store.userTotal).toBe(2)
      expect(store.users[0].username).toBe('user1')
    })

    it('应该正确更新角色列表', () => {
      const store = useSystemStore()

      const mockRoles = [
        { id: 1, name: 'admin', status: RoleStatus.ENABLE },
        { id: 2, name: 'user', status: RoleStatus.ENABLE }
      ]

      store.roles = mockRoles as any
      store.roleTotal = 2

      expect(store.roles).toHaveLength(2)
      expect(store.roleTotal).toBe(2)
      expect(store.roles[0].name).toBe('admin')
    })

    it('应该正确设置当前用户', () => {
      const store = useSystemStore()

      const mockUser = {
        id: 1,
        username: 'currentUser',
        status: UserStatus.ACTIVE
      }

      store.currentUser = mockUser as any

      expect(store.currentUser).toStrictEqual(mockUser)
      expect(store.currentUser?.username).toBe('currentUser')
    })

    it('应该正确设置当前角色', () => {
      const store = useSystemStore()

      const mockRole = {
        id: 1,
        name: 'currentRole',
        status: RoleStatus.ENABLE
      }

      store.currentRole = mockRole as any

      expect(store.currentRole).toStrictEqual(mockRole)
      expect(store.currentRole?.name).toBe('currentRole')
    })

    it('应该正确更新查询参数', () => {
      const store = useSystemStore()

      // 更新用户查询参数
      store.userQueryParams = {
        page: 2,
        pageSize: 20,
        username: 'test'
      }

      expect(store.userQueryParams.page).toBe(2)
      expect(store.userQueryParams.pageSize).toBe(20)
      expect(store.userQueryParams.username).toBe('test')

      // 更新角色查询参数
      store.roleQueryParams = {
        page: 3,
        pageSize: 15,
        name: 'admin'
      }

      expect(store.roleQueryParams.page).toBe(3)
      expect(store.roleQueryParams.pageSize).toBe(15)
      expect(store.roleQueryParams.name).toBe('admin')
    })

    it('应该正确管理加载状态', () => {
      const store = useSystemStore()

      // 测试用户加载状态
      store.userLoading = true
      expect(store.userLoading).toBe(true)

      store.userLoading = false
      expect(store.userLoading).toBe(false)

      // 测试角色加载状态
      store.roleLoading = true
      expect(store.roleLoading).toBe(true)

      store.roleLoading = false
      expect(store.roleLoading).toBe(false)

      // 测试权限加载状态
      store.permissionLoading = true
      expect(store.permissionLoading).toBe(true)

      store.permissionLoading = false
      expect(store.permissionLoading).toBe(false)

      // 测试菜单加载状态
      store.menuLoading = true
      expect(store.menuLoading).toBe(true)

      store.menuLoading = false
      expect(store.menuLoading).toBe(false)
    })
  })

  describe('复杂状态操作', () => {
    it('应该支持用户状态过滤的组合操作', () => {
      const store = useSystemStore()

      const mockUsers = [
        { id: 1, userName: 'user1', status: UserStatus.ACTIVE },
        { id: 2, userName: 'user2', status: UserStatus.DISABLED },
        { id: 3, userName: 'user3', status: UserStatus.LOCKED },
        { id: 4, userName: 'user4', status: UserStatus.ACTIVE },
        { id: 5, userName: 'user5', status: UserStatus.DISABLED }
      ]

      store.users = mockUsers as any

      // 测试各种状态过滤
      expect(store.activeUsers).toHaveLength(2)
      expect(store.disabledUsers).toHaveLength(2)
      expect(store.lockedUsers).toHaveLength(1)

      // 验证总数正确
      expect(store.activeUsers.length + store.disabledUsers.length + store.lockedUsers.length).toBe(
        5
      )
    })

    it('应该支持多级别角色分组', () => {
      const store = useSystemStore()

      const mockRoles = [
        { id: 1, name: 'super_admin', level: 1 },
        { id: 2, name: 'admin', level: 2 },
        { id: 3, name: 'manager', level: 2 },
        { id: 4, name: 'user', level: 3 },
        { id: 5, name: 'guest', level: 4 }
      ]

      store.roles = mockRoles as any
      const rolesByLevel = store.rolesByLevel

      expect(Object.keys(rolesByLevel)).toEqual(['1', '2', '3', '4'])
      expect(rolesByLevel[1]).toHaveLength(1)
      expect(rolesByLevel[2]).toHaveLength(2)
      expect(rolesByLevel[3]).toHaveLength(1)
      expect(rolesByLevel[4]).toHaveLength(1)
    })
  })
})
