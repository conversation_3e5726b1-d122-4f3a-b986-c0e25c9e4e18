import type { User, Role } from '@/types/system'
import { UserStatus, DataScope, RoleStatus } from '@/types/system'

// Mock 角色数据（用于用户关联）
const mockRoles: Role[] = [
  {
    id: 1,
    name: '超级管理员',
    code: 'SUPER_ADMIN',
    description: '拥有系统全部权限的超级管理员',
    level: 1,
    permissions: [],
    menus: [],
    dataScope: DataScope.ALL,
    status: RoleStatus.ENABLE,
    createTime: new Date('2024-01-01'),
    updateTime: new Date('2024-01-01')
  },
  {
    id: 2,
    name: '部门经理',
    code: 'DEPT_MANAGER',
    description: '部门管理人员，负责部门数据管理',
    level: 2,
    permissions: [],
    menus: [],
    dataScope: DataScope.DEPT_AND_SUB,
    status: RoleStatus.ENABLE,
    createTime: new Date('2024-02-01'),
    updateTime: new Date('2024-02-01')
  },
  {
    id: 3,
    name: '普通用户',
    code: 'USER',
    description: '系统普通用户',
    level: 3,
    permissions: [],
    menus: [],
    dataScope: DataScope.SELF,
    status: RoleStatus.ENABLE,
    createTime: new Date('2024-03-01'),
    updateTime: new Date('2024-03-01')
  },
  {
    id: 4,
    name: '数据分析师',
    code: 'ANALYST',
    description: '专门负责数据分析的用户',
    level: 2,
    permissions: [],
    menus: [],
    dataScope: DataScope.ALL,
    status: RoleStatus.ENABLE,
    createTime: new Date('2024-04-01'),
    updateTime: new Date('2024-04-01')
  }
]

// Mock 用户数据
export const userListMock: User[] = [
  {
    id: 1,
    username: 'admin',
    nickname: '超级管理员',
    email: '<EMAIL>',
    phone: '13800138000',
    avatar: 'https://picsum.photos/100/100?random=1',
    status: UserStatus.ACTIVE,
    roles: [mockRoles[0]], // 超级管理员角色
    lastLoginTime: new Date('2025-01-10 09:30:00'),
    createTime: new Date('2024-01-01'),
    updateTime: new Date('2025-01-10'),
    remark: '系统超级管理员账号'
  },
  {
    id: 2,
    username: 'manager',
    nickname: '部门经理',
    email: '<EMAIL>',
    phone: '13800138001',
    avatar: 'https://picsum.photos/100/100?random=2',
    status: UserStatus.ACTIVE,
    roles: [mockRoles[1]], // 部门经理角色
    lastLoginTime: new Date('2025-01-09 16:45:00'),
    createTime: new Date('2024-03-15'),
    updateTime: new Date('2025-01-09'),
    remark: '产品部门经理'
  },
  {
    id: 3,
    username: 'analyst',
    nickname: '数据分析师',
    email: '<EMAIL>',
    phone: '13800138002',
    avatar: 'https://picsum.photos/100/100?random=3',
    status: UserStatus.ACTIVE,
    roles: [mockRoles[3]], // 数据分析师角色
    lastLoginTime: new Date('2025-01-09 14:20:00'),
    createTime: new Date('2024-05-20'),
    updateTime: new Date('2025-01-09'),
    remark: '专业数据分析师'
  },
  {
    id: 4,
    username: 'user001',
    nickname: '张三',
    email: '<EMAIL>',
    phone: '13800138003',
    avatar: 'https://picsum.photos/100/100?random=4',
    status: UserStatus.ACTIVE,
    roles: [mockRoles[2]], // 普通用户角色
    lastLoginTime: new Date('2025-01-08 10:15:00'),
    createTime: new Date('2024-06-01'),
    updateTime: new Date('2025-01-08')
  },
  {
    id: 5,
    username: 'user002',
    nickname: '李四',
    email: '<EMAIL>',
    phone: '13800138004',
    avatar: 'https://picsum.photos/100/100?random=5',
    status: UserStatus.DISABLED,
    roles: [mockRoles[2]], // 普通用户角色
    lastLoginTime: new Date('2024-12-20 09:30:00'),
    createTime: new Date('2024-07-10'),
    updateTime: new Date('2024-12-25'),
    remark: '已禁用的测试账号'
  },
  {
    id: 6,
    username: 'user003',
    nickname: '王五',
    email: '<EMAIL>',
    phone: '13800138005',
    avatar: 'https://picsum.photos/100/100?random=6',
    status: UserStatus.LOCKED,
    roles: [mockRoles[2]], // 普通用户角色
    lastLoginTime: new Date('2024-11-15 16:45:00'),
    createTime: new Date('2024-08-05'),
    updateTime: new Date('2024-11-20'),
    remark: '因违规操作被锁定'
  },
  {
    id: 7,
    username: 'tester',
    nickname: '测试员',
    email: '<EMAIL>',
    phone: '13800138006',
    avatar: 'https://picsum.photos/100/100?random=7',
    status: UserStatus.ACTIVE,
    roles: [mockRoles[2]], // 普通用户角色
    lastLoginTime: new Date('2025-01-07 13:22:00'),
    createTime: new Date('2024-09-12'),
    updateTime: new Date('2025-01-07'),
    remark: '系统测试专用账号'
  },
  {
    id: 8,
    username: 'guest',
    nickname: '访客',
    email: '<EMAIL>',
    phone: '13800138007',
    status: UserStatus.ACTIVE,
    roles: [mockRoles[2]], // 普通用户角色
    createTime: new Date('2024-10-01'),
    updateTime: new Date('2024-10-01'),
    remark: '临时访客账号'
  },
  {
    id: 9,
    username: 'developer',
    nickname: '开发工程师',
    email: '<EMAIL>',
    phone: '13800138008',
    avatar: 'https://picsum.photos/100/100?random=9',
    status: UserStatus.ACTIVE,
    roles: [mockRoles[1], mockRoles[3]], // 多角色：部门经理 + 数据分析师
    lastLoginTime: new Date('2025-01-09 18:30:00'),
    createTime: new Date('2024-11-01'),
    updateTime: new Date('2025-01-09'),
    remark: '技术部开发工程师'
  },
  {
    id: 10,
    username: 'support',
    nickname: '客服代表',
    email: '<EMAIL>',
    phone: '13800138009',
    avatar: 'https://picsum.photos/100/100?random=10',
    status: UserStatus.ACTIVE,
    roles: [mockRoles[2]], // 普通用户角色
    lastLoginTime: new Date('2025-01-08 11:45:00'),
    createTime: new Date('2024-12-01'),
    updateTime: new Date('2025-01-08'),
    remark: '客户服务代表'
  }
]

// 获取所有用户状态的统计信息
export const getUserStatusStats = () => {
  const stats = {
    total: userListMock.length,
    active: userListMock.filter(u => u.status === UserStatus.ACTIVE).length,
    disabled: userListMock.filter(u => u.status === UserStatus.DISABLED).length,
    locked: userListMock.filter(u => u.status === UserStatus.LOCKED).length
  }
  return stats
}

// 根据角色ID获取用户列表
export const getUsersByRoleId = (roleId: number): User[] => {
  return userListMock.filter(user => user.roles.some(role => role.id === roleId))
}

// 根据状态获取用户列表
export const getUsersByStatus = (status: UserStatus): User[] => {
  return userListMock.filter(user => user.status === status)
}

// 模拟搜索用户
export const searchUsers = (keyword: string): User[] => {
  if (!keyword) return userListMock

  const lowerKeyword = keyword.toLowerCase()
  return userListMock.filter(
    user =>
      user.username.toLowerCase().includes(lowerKeyword) ||
      user.nickname.toLowerCase().includes(lowerKeyword) ||
      user.email.toLowerCase().includes(lowerKeyword)
  )
}
