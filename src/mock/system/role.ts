import type { Role, Permission, Menu } from '@/types/system'
import { RoleStatus, DataScope, PermissionType, PermissionStatus } from '@/types/system'

// Mock 权限数据
export const permissionListMock: Permission[] = [
  // 系统管理权限
  {
    id: 1,
    name: '系统管理',
    code: 'system',
    type: PermissionType.MENU,
    path: '/system',
    icon: 'Setting',
    sort: 1,
    status: PermissionStatus.ENABLE
  },
  {
    id: 2,
    name: '用户管理',
    code: 'system:user',
    type: PermissionType.MENU,
    parentId: 1,
    path: '/system/user',
    icon: 'User',
    sort: 1,
    status: PermissionStatus.ENABLE
  },
  {
    id: 3,
    name: '查看用户',
    code: 'system:user:view',
    type: PermissionType.BUTTON,
    parentId: 2,
    sort: 1,
    status: PermissionStatus.ENABLE
  },
  {
    id: 4,
    name: '新增用户',
    code: 'system:user:add',
    type: PermissionType.BUTTON,
    parentId: 2,
    sort: 2,
    status: PermissionStatus.ENABLE
  },
  {
    id: 5,
    name: '编辑用户',
    code: 'system:user:edit',
    type: PermissionType.BUTTON,
    parentId: 2,
    sort: 3,
    status: PermissionStatus.ENABLE
  },
  {
    id: 6,
    name: '删除用户',
    code: 'system:user:delete',
    type: PermissionType.BUTTON,
    parentId: 2,
    sort: 4,
    status: PermissionStatus.ENABLE
  },
  {
    id: 7,
    name: '重置密码',
    code: 'system:user:reset',
    type: PermissionType.BUTTON,
    parentId: 2,
    sort: 5,
    status: PermissionStatus.ENABLE
  },
  {
    id: 8,
    name: '角色管理',
    code: 'system:role',
    type: PermissionType.MENU,
    parentId: 1,
    path: '/system/role',
    icon: 'UserFilled',
    sort: 2,
    status: PermissionStatus.ENABLE
  },
  {
    id: 9,
    name: '查看角色',
    code: 'system:role:view',
    type: PermissionType.BUTTON,
    parentId: 8,
    sort: 1,
    status: PermissionStatus.ENABLE
  },
  {
    id: 10,
    name: '新增角色',
    code: 'system:role:add',
    type: PermissionType.BUTTON,
    parentId: 8,
    sort: 2,
    status: PermissionStatus.ENABLE
  },
  {
    id: 11,
    name: '编辑角色',
    code: 'system:role:edit',
    type: PermissionType.BUTTON,
    parentId: 8,
    sort: 3,
    status: PermissionStatus.ENABLE
  },
  {
    id: 12,
    name: '删除角色',
    code: 'system:role:delete',
    type: PermissionType.BUTTON,
    parentId: 8,
    sort: 4,
    status: PermissionStatus.ENABLE
  },
  {
    id: 13,
    name: '分配权限',
    code: 'system:role:permission',
    type: PermissionType.BUTTON,
    parentId: 8,
    sort: 5,
    status: PermissionStatus.ENABLE
  },
  // 数据分析权限
  {
    id: 14,
    name: '数据分析',
    code: 'data',
    type: PermissionType.MENU,
    path: '/data',
    icon: 'DataAnalysis',
    sort: 2,
    status: PermissionStatus.ENABLE
  },
  {
    id: 15,
    name: '数据报告',
    code: 'data:report',
    type: PermissionType.MENU,
    parentId: 14,
    path: '/data/report',
    icon: 'Document',
    sort: 1,
    status: PermissionStatus.ENABLE
  },
  {
    id: 16,
    name: '查看报告',
    code: 'data:report:view',
    type: PermissionType.BUTTON,
    parentId: 15,
    sort: 1,
    status: PermissionStatus.ENABLE
  },
  {
    id: 17,
    name: '导出报告',
    code: 'data:report:export',
    type: PermissionType.BUTTON,
    parentId: 15,
    sort: 2,
    status: PermissionStatus.ENABLE
  },
  {
    id: 18,
    name: '数据分析',
    code: 'data:analysis',
    type: PermissionType.MENU,
    parentId: 14,
    path: '/data/analysis',
    icon: 'TrendCharts',
    sort: 2,
    status: PermissionStatus.ENABLE
  },
  {
    id: 19,
    name: '查看分析',
    code: 'data:analysis:view',
    type: PermissionType.BUTTON,
    parentId: 18,
    sort: 1,
    status: PermissionStatus.ENABLE
  },
  {
    id: 20,
    name: '创建分析',
    code: 'data:analysis:create',
    type: PermissionType.BUTTON,
    parentId: 18,
    sort: 2,
    status: PermissionStatus.ENABLE
  },
  // API权限
  {
    id: 21,
    name: '获取用户列表',
    code: 'api:user:list',
    type: PermissionType.API,
    path: '/api/users',
    method: 'GET',
    sort: 1,
    status: PermissionStatus.ENABLE
  },
  {
    id: 22,
    name: '创建用户',
    code: 'api:user:create',
    type: PermissionType.API,
    path: '/api/users',
    method: 'POST',
    sort: 2,
    status: PermissionStatus.ENABLE
  },
  {
    id: 23,
    name: '更新用户',
    code: 'api:user:update',
    type: PermissionType.API,
    path: '/api/users/:id',
    method: 'PUT',
    sort: 3,
    status: PermissionStatus.ENABLE
  },
  {
    id: 24,
    name: '删除用户',
    code: 'api:user:delete',
    type: PermissionType.API,
    path: '/api/users/:id',
    method: 'DELETE',
    sort: 4,
    status: PermissionStatus.ENABLE
  },
  {
    id: 25,
    name: '获取角色列表',
    code: 'api:role:list',
    type: PermissionType.API,
    path: '/api/roles',
    method: 'GET',
    sort: 5,
    status: PermissionStatus.ENABLE
  },
  {
    id: 26,
    name: '创建角色',
    code: 'api:role:create',
    type: PermissionType.API,
    path: '/api/roles',
    method: 'POST',
    sort: 6,
    status: PermissionStatus.ENABLE
  },
  {
    id: 27,
    name: '更新角色',
    code: 'api:role:update',
    type: PermissionType.API,
    path: '/api/roles/:id',
    method: 'PUT',
    sort: 7,
    status: PermissionStatus.ENABLE
  },
  {
    id: 28,
    name: '删除角色',
    code: 'api:role:delete',
    type: PermissionType.API,
    path: '/api/roles/:id',
    method: 'DELETE',
    sort: 8,
    status: PermissionStatus.ENABLE
  },
  {
    id: 29,
    name: '获取数据报告',
    code: 'api:data:report',
    type: PermissionType.API,
    path: '/api/data/reports',
    method: 'GET',
    sort: 9,
    status: PermissionStatus.ENABLE
  },
  {
    id: 30,
    name: '导出数据报告',
    code: 'api:data:export',
    type: PermissionType.API,
    path: '/api/data/export',
    method: 'POST',
    sort: 10,
    status: PermissionStatus.ENABLE
  }
]

// Mock 菜单数据
export const menuListMock: Menu[] = [
  {
    id: 1,
    name: '首页',
    path: '/home',
    icon: 'HomeFilled',
    sort: 1,
    status: PermissionStatus.ENABLE
  },
  {
    id: 2,
    name: '数据分析',
    path: '/data',
    icon: 'DataAnalysis',
    sort: 2,
    status: PermissionStatus.ENABLE,
    children: [
      {
        id: 3,
        name: '数据报告',
        path: '/data/report',
        icon: 'Document',
        parentId: 2,
        sort: 1,
        status: PermissionStatus.ENABLE
      },
      {
        id: 4,
        name: '数据分析',
        path: '/data/analysis',
        icon: 'TrendCharts',
        parentId: 2,
        sort: 2,
        status: PermissionStatus.ENABLE
      }
    ]
  },
  {
    id: 5,
    name: '系统管理',
    path: '/system',
    icon: 'Setting',
    sort: 3,
    status: PermissionStatus.ENABLE,
    children: [
      {
        id: 6,
        name: '用户管理',
        path: '/system/user',
        icon: 'User',
        parentId: 5,
        sort: 1,
        status: PermissionStatus.ENABLE
      },
      {
        id: 7,
        name: '角色管理',
        path: '/system/role',
        icon: 'UserFilled',
        parentId: 5,
        sort: 2,
        status: PermissionStatus.ENABLE
      }
    ]
  }
]

// Mock 角色数据
export const roleListMock: Role[] = [
  {
    id: 1,
    name: '超级管理员',
    code: 'SUPER_ADMIN',
    description: '拥有系统全部权限的超级管理员',
    level: 1,
    permissions: permissionListMock, // 全部权限
    menus: menuListMock, // 全部菜单
    dataScope: DataScope.ALL,
    status: RoleStatus.ENABLE,
    createTime: new Date('2024-01-01'),
    updateTime: new Date('2024-01-01')
  },
  {
    id: 2,
    name: '部门经理',
    code: 'DEPT_MANAGER',
    description: '部门管理人员，负责部门数据管理',
    level: 2,
    permissions: permissionListMock.filter(
      p =>
        p.code.startsWith('data:') ||
        p.code === 'data' ||
        p.code.startsWith('system:user:view') ||
        p.code.startsWith('system:user:add')
    ),
    menus: menuListMock.filter(m => m.path === '/home' || m.path === '/data'),
    dataScope: DataScope.DEPT_AND_SUB,
    status: RoleStatus.ENABLE,
    createTime: new Date('2024-02-01'),
    updateTime: new Date('2024-02-01')
  },
  {
    id: 3,
    name: '普通用户',
    code: 'USER',
    description: '系统普通用户',
    level: 3,
    permissions: permissionListMock.filter(
      p => p.code === 'data:report:view' || p.code === 'data:analysis:view'
    ),
    menus: menuListMock.filter(m => m.path === '/home' || m.path === '/data/report'),
    dataScope: DataScope.SELF,
    status: RoleStatus.ENABLE,
    createTime: new Date('2024-03-01'),
    updateTime: new Date('2024-03-01')
  },
  {
    id: 4,
    name: '数据分析师',
    code: 'ANALYST',
    description: '专门负责数据分析的用户',
    level: 2,
    permissions: permissionListMock.filter(p => p.code.startsWith('data:') || p.code === 'data'),
    menus: menuListMock.filter(m => m.path === '/home' || m.path === '/data'),
    dataScope: DataScope.ALL,
    status: RoleStatus.ENABLE,
    createTime: new Date('2024-04-01'),
    updateTime: new Date('2024-04-01')
  },
  {
    id: 5,
    name: '只读角色',
    code: 'READONLY',
    description: '只读权限的角色',
    level: 3,
    permissions: permissionListMock.filter(
      p => p.code.includes(':view') || p.code === 'data' || p.code === 'system'
    ),
    menus: menuListMock,
    dataScope: DataScope.SELF,
    status: RoleStatus.ENABLE,
    createTime: new Date('2024-05-01'),
    updateTime: new Date('2024-05-01')
  },
  {
    id: 6,
    name: '测试角色',
    code: 'TESTER',
    description: '用于系统测试的角色',
    level: 2,
    permissions: permissionListMock.filter(
      p => !p.code.includes(':delete') && !p.code.includes(':reset')
    ),
    menus: menuListMock,
    dataScope: DataScope.DEPT,
    status: RoleStatus.DISABLE, // 禁用状态
    createTime: new Date('2024-06-01'),
    updateTime: new Date('2024-06-01')
  }
]

// 根据角色级别获取角色列表
export const getRolesByLevel = (level: number): Role[] => {
  return roleListMock.filter(role => role.level === level)
}

// 根据状态获取角色列表
export const getRolesByStatus = (status: RoleStatus): Role[] => {
  return roleListMock.filter(role => role.status === status)
}

// 获取角色的权限代码列表
export const getRolePermissionCodes = (roleId: number): string[] => {
  const role = roleListMock.find(r => r.id === roleId)
  return role ? role.permissions.map(p => p.code) : []
}

// 获取角色的菜单路径列表
export const getRoleMenuPaths = (roleId: number): string[] => {
  const role = roleListMock.find(r => r.id === roleId)
  if (!role) return []

  const paths: string[] = []
  const extractPaths = (menus: Menu[]) => {
    menus.forEach(menu => {
      paths.push(menu.path)
      if (menu.children) {
        extractPaths(menu.children)
      }
    })
  }

  extractPaths(role.menus)
  return paths
}

// 模拟搜索角色
export const searchRoles = (keyword: string): Role[] => {
  if (!keyword) return roleListMock

  const lowerKeyword = keyword.toLowerCase()
  return roleListMock.filter(
    role =>
      role.name.toLowerCase().includes(lowerKeyword) ||
      role.code.toLowerCase().includes(lowerKeyword) ||
      (role.description && role.description.toLowerCase().includes(lowerKeyword))
  )
}
