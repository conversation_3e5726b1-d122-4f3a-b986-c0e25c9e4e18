/**
 * 数据字典相关API接口
 */

import http from '@/api/http'
import type {
  DictTypeModel,
  DictItemModel,
  DictQueryParams,
  DictItemQueryParams,
  DictPageResult,
  DictItemPageResult,
  DictItemListResult,
  DictDetailResult,
  DictItemDetailResult,
  ApiResult
} from './index.d'

// 数据字典类型管理接口
export const dictApi = {
  // 分页查询数据字典列表
  getDictList: (params: DictQueryParams): Promise<DictPageResult> => {
    return http.post('/insDict/dict-list', params)
  },

  // 新增数据字典
  createDict: (data: DictTypeModel): Promise<ApiResult<number>> => {
    return http.post('/insDict/insert', data)
  },

  // 更新数据字典
  updateDict: (data: DictTypeModel): Promise<ApiResult<number>> => {
    return http.post('/insDict/update', data)
  },

  // 根据ID查询数据字典详情
  getDictDetail: (id: string): Promise<DictDetailResult> => {
    return http.get(`/insDict/dict-detail/${id}`)
  },

  // 删除数据字典
  deleteDict: (id: string): Promise<ApiResult<number>> => {
    return http.delete(`/insDict/delete/${id}`)
  },

  // 批量删除数据字典
  batchDeleteDict: (ids: string[]): Promise<ApiResult<number>> => {
    return http.post('/insDict/batch-delete', ids)
  }
}

// 数据字典项管理接口
export const dictItemApi = {
  // 分页查询数据字典项列表
  getDictItemList: (params: DictItemQueryParams): Promise<DictItemPageResult> => {
    return http.post('/insDictItem/dict-item-list', params)
  },

  // 根据字典ID查询所有字典项
  getDictItemsByDictId: (dictId: string): Promise<DictItemListResult> => {
    return http.get(`/insDictItem/dict-items-by-dict/${dictId}`)
  },

  // 新增数据字典项
  createDictItem: (data: DictItemModel): Promise<ApiResult<number>> => {
    return http.post('/insDictItem/insert', data)
  },

  // 更新数据字典项
  updateDictItem: (data: DictItemModel): Promise<ApiResult<number>> => {
    return http.post('/insDictItem/update', data)
  },

  // 根据ID查询数据字典项详情
  getDictItemDetail: (id: string): Promise<DictItemDetailResult> => {
    return http.get(`/insDictItem/dict-item-detail/${id}`)
  },

  // 删除数据字典项
  deleteDictItem: (id: string): Promise<ApiResult<number>> => {
    return http.delete(`/insDictItem/delete/${id}`)
  }
}
