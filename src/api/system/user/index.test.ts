import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { UserAPI } from './index'
import { UserStatus } from '@/types/system'
import type { UserQueryParams, CreateUserRequest, UpdateUserRequest } from './index.d'

// Mock延迟函数
vi.mock('./index', async () => {
  const actual = await vi.importActual('./index')
  return {
    ...actual,
    // 重写delay函数为立即返回
    delay: vi.fn(() => Promise.resolve())
  }
})

describe('UserAPI测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // 重置console.log mock
    vi.spyOn(console, 'log').mockImplementation(() => {})
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('getUserList', () => {
    it('应该返回正确的分页用户列表', async () => {
      const params: UserQueryParams = {
        page: 1,
        pageSize: 10
      }

      const result = await UserAPI.getUserList(params)

      expect(result).toHaveProperty('data')
      expect(result).toHaveProperty('total')
      expect(result).toHaveProperty('page', 1)
      expect(result).toHaveProperty('pageSize', 10)
      expect(Array.isArray(result.data)).toBe(true)
      expect(typeof result.total).toBe('number')
    })

    it('应该正确执行用户名搜索过滤', async () => {
      const params: UserQueryParams = {
        page: 1,
        pageSize: 10,
        username: 'admin'
      }

      const result = await UserAPI.getUserList(params)

      // 验证返回的用户中包含admin用户名
      const hasAdmin = result.data.some(user => user.username.toLowerCase().includes('admin'))
      expect(hasAdmin).toBe(true)
    })

    it('应该正确执行状态过滤', async () => {
      const params: UserQueryParams = {
        page: 1,
        pageSize: 10,
        status: UserStatus.ACTIVE
      }

      const result = await UserAPI.getUserList(params)

      // 验证所有返回的用户状态都是ACTIVE
      result.data.forEach(user => {
        expect(user.status).toBe(UserStatus.ACTIVE)
      })
    })

    it('应该正确处理分页参数', async () => {
      const params: UserQueryParams = {
        page: 2,
        pageSize: 5
      }

      const result = await UserAPI.getUserList(params)

      expect(result.page).toBe(2)
      expect(result.pageSize).toBe(5)
      expect(result.data.length).toBeLessThanOrEqual(5)
    })

    it('应该正确处理邮箱和昵称搜索', async () => {
      const emailParams: UserQueryParams = {
        page: 1,
        pageSize: 10,
        email: 'admin'
      }

      const emailResult = await UserAPI.getUserList(emailParams)
      expect(emailResult.data.length).toBeGreaterThanOrEqual(0)

      const nicknameParams: UserQueryParams = {
        page: 1,
        pageSize: 10,
        nickname: '管理员'
      }

      const nicknameResult = await UserAPI.getUserList(nicknameParams)
      expect(nicknameResult.data.length).toBeGreaterThanOrEqual(0)
    })
  })

  describe('createUser', () => {
    it('应该成功创建新用户', async () => {
      const userData: CreateUserRequest = {
        username: 'testuser_' + Date.now(),
        nickname: '测试用户',
        email: 'test_' + Date.now() + '@example.com',
        phone: '13800138999',
        password: '123456',
        roleIds: [1],
        remark: '测试用户'
      }

      const result = await UserAPI.createUser(userData)

      expect(result).toHaveProperty('id')
      expect(result.username).toBe(userData.username)
      expect(result.nickname).toBe(userData.nickname)
      expect(result.email).toBe(userData.email)
      expect(result.status).toBe(UserStatus.ACTIVE)
    })

    it('应该在用户名重复时抛出错误', async () => {
      const userData: CreateUserRequest = {
        username: 'admin', // 使用已存在的用户名
        nickname: '测试用户',
        email: '<EMAIL>',
        phone: '13800138998',
        password: '123456',
        roleIds: [1],
        remark: '测试用户'
      }

      await expect(UserAPI.createUser(userData)).rejects.toThrow('用户名已存在')
    })

    it('应该在邮箱重复时抛出错误', async () => {
      const userData: CreateUserRequest = {
        username: 'testuser2',
        nickname: '测试用户',
        email: '<EMAIL>', // 使用已存在的邮箱
        phone: '13800138997',
        password: '123456',
        roleIds: [1],
        remark: '测试用户'
      }

      await expect(UserAPI.createUser(userData)).rejects.toThrow('邮箱已存在')
    })
  })

  describe('updateUser', () => {
    it('应该成功更新用户信息', async () => {
      const userData: UpdateUserRequest = {
        nickname: '更新后的昵称',
        email: 'updated_' + Date.now() + '@example.com',
        phone: '13800138996'
      }

      const result = await UserAPI.updateUser(2, userData) // 使用存在的用户ID

      expect(result.nickname).toBe(userData.nickname)
      expect(result.email).toBe(userData.email)
      expect(result.phone).toBe(userData.phone)
      expect(result.updateTime).toBeInstanceOf(Date)
    })

    it('应该在用户不存在时抛出错误', async () => {
      const userData: UpdateUserRequest = {
        nickname: '不存在的用户'
      }

      await expect(UserAPI.updateUser(999, userData)).rejects.toThrow('用户不存在')
    })

    it('应该在邮箱冲突时抛出错误', async () => {
      const userData: UpdateUserRequest = {
        email: '<EMAIL>' // 使用已被其他用户占用的邮箱
      }

      await expect(UserAPI.updateUser(2, userData)).rejects.toThrow('邮箱已被其他用户使用')
    })
  })

  describe('deleteUser', () => {
    it('应该成功删除用户', async () => {
      // 首先创建一个测试用户
      const userData: CreateUserRequest = {
        username: 'deletetest_' + Date.now(),
        nickname: '待删除用户',
        email: 'delete_' + Date.now() + '@example.com',
        phone: '13800138995',
        password: '123456',
        roleIds: [1],
        remark: '待删除用户'
      }

      const createdUser = await UserAPI.createUser(userData)

      // 删除用户应该不抛出错误
      await expect(UserAPI.deleteUser(createdUser.id)).resolves.toBeUndefined()
    })

    it('应该在删除管理员时抛出错误', async () => {
      // 假设ID为1的是admin用户
      await expect(UserAPI.deleteUser(1)).rejects.toThrow('不能删除超级管理员账号')
    })

    it('应该在用户不存在时抛出错误', async () => {
      await expect(UserAPI.deleteUser(999)).rejects.toThrow('用户不存在')
    })
  })

  describe('batchDeleteUsers', () => {
    it('应该成功批量删除用户', async () => {
      const ids = [4, 5] // 使用存在的非管理员用户ID

      await expect(UserAPI.batchDeleteUsers(ids)).resolves.toBeUndefined()
    })

    it('应该在包含管理员时抛出错误', async () => {
      const ids = [1, 2] // 包含admin用户ID

      await expect(UserAPI.batchDeleteUsers(ids)).rejects.toThrow('不能删除超级管理员账号')
    })

    it('应该正确处理不存在的用户ID', async () => {
      const ids = [999, 998] // 不存在的用户ID

      // 应该不抛出错误，因为函数会过滤不存在的ID
      await expect(UserAPI.batchDeleteUsers(ids)).resolves.toBeUndefined()
    })
  })

  describe('resetPassword', () => {
    it('应该成功重置用户密码', async () => {
      const consoleLogSpy = vi.spyOn(console, 'log')

      await UserAPI.resetPassword(2, '新密码123')

      expect(consoleLogSpy).toHaveBeenCalledWith(expect.stringContaining('重置用户'))
    })

    it('应该在用户不存在时抛出错误', async () => {
      await expect(UserAPI.resetPassword(999, '新密码')).rejects.toThrow('用户不存在')
    })

    it('应该支持默认密码重置', async () => {
      const consoleLogSpy = vi.spyOn(console, 'log')

      await UserAPI.resetPassword(2)

      expect(consoleLogSpy).toHaveBeenCalledWith(expect.stringContaining('123456'))
    })
  })

  describe('assignRoles', () => {
    it('应该成功为用户分配角色', async () => {
      const consoleLogSpy = vi.spyOn(console, 'log')

      await UserAPI.assignRoles(2, [1, 2])

      expect(consoleLogSpy).toHaveBeenCalledWith(expect.stringContaining('分配角色'))
    })

    it('应该在用户不存在时抛出错误', async () => {
      await expect(UserAPI.assignRoles(999, [1])).rejects.toThrow('用户不存在')
    })
  })

  describe('updateUserStatus', () => {
    it('应该成功更新用户状态', async () => {
      await expect(UserAPI.updateUserStatus(2, UserStatus.DISABLED)).resolves.toBeUndefined()
    })

    it('应该在用户不存在时抛出错误', async () => {
      await expect(UserAPI.updateUserStatus(999, UserStatus.ACTIVE)).rejects.toThrow('用户不存在')
    })

    it('应该在禁用管理员时抛出错误', async () => {
      await expect(UserAPI.updateUserStatus(1, UserStatus.DISABLED)).rejects.toThrow(
        '不能禁用超级管理员账号'
      )
    })
  })
})
