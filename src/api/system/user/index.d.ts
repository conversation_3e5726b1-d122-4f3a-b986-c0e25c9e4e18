import type {
  User,
  UserQueryParams,
  CreateUserRequest,
  UpdateUserRequest,
  PageResult,
  UserStatus
} from '@/types/system'

/**
 * 用户管理 API 类型定义
 */
export interface IUserAPI {
  /**
   * 获取用户列表（分页）
   */
  getUserList(params: UserQueryParams): Promise<PageResult<User>>

  /**
   * 根据ID获取用户详情
   */
  getUserById(id: number): Promise<User | null>

  /**
   * 创建新用户
   */
  createUser(userData: CreateUserRequest): Promise<User>

  /**
   * 更新用户信息
   */
  updateUser(id: number, userData: UpdateUserRequest): Promise<User>

  /**
   * 删除用户
   */
  deleteUser(id: number): Promise<void>

  /**
   * 批量删除用户
   */
  batchDeleteUsers(ids: number[]): Promise<void>

  /**
   * 重置用户密码
   */
  resetPassword(id: number, newPassword?: string): Promise<void>

  /**
   * 分配角色给用户
   */
  assignRoles(userId: number, roleIds: number[]): Promise<void>

  /**
   * 更新用户状态
   */
  updateUserStatus(id: number, status: UserStatus): Promise<void>

  /**
   * 搜索用户
   */
  searchUsers(keyword: string): Promise<User[]>

  /**
   * 获取用户统计信息
   */
  getUserStats(): Promise<Record<string, number>>
}

// 导出类型
export type {
  User,
  UserQueryParams,
  CreateUserRequest,
  UpdateUserRequest,
  PageResult,
  UserStatus
} from '@/types/system'
