import type {
  User,
  UserQueryParams,
  CreateUserRequest,
  UpdateUserRequest,
  PageResult
} from './index.d'
import { UserStatus } from '@/types/system'
import { userListMock, getUserStatusStats, searchUsers } from '@/mock/system/user'

// 模拟API延迟
const delay = (ms: number = 300) => new Promise(resolve => setTimeout(resolve, ms))

// 生成唯一ID
const generateId = () => Math.max(...userListMock.map(u => u.id)) + 1

/**
 * 用户管理 API 接口
 */
export class UserAPI {
  /**
   * 获取用户列表（分页）
   */
  static async getUserList(params: UserQueryParams): Promise<PageResult<User>> {
    await delay()

    let filteredUsers = [...userListMock]

    // 搜索过滤
    if (params.username) {
      filteredUsers = filteredUsers.filter(user =>
        user.username.toLowerCase().includes(params.username!.toLowerCase())
      )
    }

    if (params.nickname) {
      filteredUsers = filteredUsers.filter(user =>
        user.nickname.toLowerCase().includes(params.nickname!.toLowerCase())
      )
    }

    if (params.email) {
      filteredUsers = filteredUsers.filter(user =>
        user.email.toLowerCase().includes(params.email!.toLowerCase())
      )
    }

    if (params.status) {
      filteredUsers = filteredUsers.filter(user => user.status === params.status)
    }

    if (params.roleId) {
      filteredUsers = filteredUsers.filter(user =>
        user.roles.some(role => role.id === params.roleId)
      )
    }

    // 分页处理
    const { page = 1, pageSize = 10 } = params
    const total = filteredUsers.length
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const data = filteredUsers.slice(startIndex, endIndex)

    return {
      data,
      total,
      page,
      pageSize
    }
  }

  /**
   * 根据ID获取用户详情
   */
  static async getUserById(id: number): Promise<User | null> {
    await delay()
    return userListMock.find(user => user.id === id) || null
  }

  /**
   * 创建新用户
   */
  static async createUser(userData: CreateUserRequest): Promise<User> {
    await delay()

    // 检查用户名是否已存在
    const existingUser = userListMock.find(user => user.username === userData.username)
    if (existingUser) {
      throw new Error('用户名已存在')
    }

    // 检查邮箱是否已存在
    const existingEmail = userListMock.find(user => user.email === userData.email)
    if (existingEmail) {
      throw new Error('邮箱已存在')
    }

    const newUser: User = {
      id: generateId(),
      username: userData.username,
      nickname: userData.nickname,
      email: userData.email,
      phone: userData.phone,
      status: UserStatus.ACTIVE,
      roles: [], // 实际项目中需要根据 roleIds 查询角色信息
      createTime: new Date(),
      updateTime: new Date(),
      remark: userData.remark
    }

    userListMock.push(newUser)
    return newUser
  }

  /**
   * 更新用户信息
   */
  static async updateUser(id: number, userData: UpdateUserRequest): Promise<User> {
    await delay()

    const userIndex = userListMock.findIndex(user => user.id === id)
    if (userIndex === -1) {
      throw new Error('用户不存在')
    }

    // 如果更新邮箱，检查是否与其他用户冲突
    if (userData.email) {
      const existingUser = userListMock.find(
        user => user.id !== id && user.email === userData.email
      )
      if (existingUser) {
        throw new Error('邮箱已被其他用户使用')
      }
    }

    const updatedUser = {
      ...userListMock[userIndex],
      ...userData,
      updateTime: new Date()
    }

    userListMock[userIndex] = updatedUser
    return updatedUser
  }

  /**
   * 删除用户
   */
  static async deleteUser(id: number): Promise<void> {
    await delay()

    const userIndex = userListMock.findIndex(user => user.id === id)
    if (userIndex === -1) {
      throw new Error('用户不存在')
    }

    // 不允许删除超级管理员
    if (userListMock[userIndex].username === 'admin') {
      throw new Error('不能删除超级管理员账号')
    }

    userListMock.splice(userIndex, 1)
  }

  /**
   * 批量删除用户
   */
  static async batchDeleteUsers(ids: number[]): Promise<void> {
    await delay()

    // 检查是否包含超级管理员
    const adminUser = userListMock.find(user => user.username === 'admin')
    if (adminUser && ids.includes(adminUser.id)) {
      throw new Error('不能删除超级管理员账号')
    }

    // 过滤掉不存在的用户ID
    const validIds = ids.filter(id => userListMock.some(user => user.id === id))

    // 删除用户
    validIds.forEach(id => {
      const index = userListMock.findIndex(user => user.id === id)
      if (index !== -1) {
        userListMock.splice(index, 1)
      }
    })
  }

  /**
   * 重置用户密码
   */
  static async resetPassword(id: number, newPassword?: string): Promise<void> {
    await delay()

    const user = userListMock.find(user => user.id === id)
    if (!user) {
      throw new Error('用户不存在')
    }

    // 实际项目中会更新用户密码
    // 这里只是模拟操作，返回成功
    console.log(`重置用户 ${user.username} 的密码为: ${newPassword || '123456'}`)
  }

  /**
   * 分配角色给用户
   */
  static async assignRoles(userId: number, roleIds: number[]): Promise<void> {
    await delay()

    const userIndex = userListMock.findIndex(user => user.id === userId)
    if (userIndex === -1) {
      throw new Error('用户不存在')
    }

    // 实际项目中需要根据 roleIds 查询角色信息
    // 这里只是模拟更新用户的角色关联
    userListMock[userIndex].updateTime = new Date()
    console.log(`为用户 ${userListMock[userIndex].username} 分配角色: ${roleIds.join(', ')}`)
  }

  /**
   * 更新用户状态
   */
  static async updateUserStatus(id: number, status: UserStatus): Promise<void> {
    await delay()

    const userIndex = userListMock.findIndex(user => user.id === id)
    if (userIndex === -1) {
      throw new Error('用户不存在')
    }

    // 不允许禁用超级管理员
    if (userListMock[userIndex].username === 'admin' && status !== UserStatus.ACTIVE) {
      throw new Error('不能禁用超级管理员账号')
    }

    userListMock[userIndex].status = status
    userListMock[userIndex].updateTime = new Date()
  }

  /**
   * 搜索用户
   */
  static async searchUsers(keyword: string): Promise<User[]> {
    await delay()
    return searchUsers(keyword)
  }

  /**
   * 获取用户统计信息
   */
  static async getUserStats(): Promise<Record<string, number>> {
    await delay()
    return getUserStatusStats()
  }
}

// 默认导出
export default UserAPI
