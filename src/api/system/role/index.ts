import type {
  Role,
  Permission,
  Menu,
  RoleQueryParams,
  CreateRoleRequest,
  UpdateRoleRequest,
  PageResult,
  NewRoleQueryParams,
  SaveOrUpdateRoleRequest,
  QueryUserPermissionRequest
} from './index.d'
import { RoleStatus } from '@/types/system'
import {
  roleListMock,
  permissionListMock,
  menuListMock,
  getRolesByLevel,
  searchRoles
} from '@/mock/system/role'
import http from '@/api/http'

// 模拟API延迟
const delay = (ms: number = 300) => new Promise(resolve => setTimeout(resolve, ms))

// 生成唯一ID
const generateId = () => Math.max(...roleListMock.map(r => r.id)) + 1

/**
 * 角色管理 API 接口
 */
export class RoleAPI {
  /**
   * 获取角色列表（分页）
   */
  static async getRoleList(params: RoleQueryParams): Promise<PageResult<Role>> {
    await delay()

    let filteredRoles = [...roleListMock]

    // 搜索过滤
    if (params.name) {
      filteredRoles = filteredRoles.filter(role =>
        role.name.toLowerCase().includes(params.name!.toLowerCase())
      )
    }

    if (params.code) {
      filteredRoles = filteredRoles.filter(role =>
        role.code.toLowerCase().includes(params.code!.toLowerCase())
      )
    }

    if (params.status) {
      filteredRoles = filteredRoles.filter(role => role.status === params.status)
    }

    if (params.level) {
      filteredRoles = filteredRoles.filter(role => role.level === params.level)
    }

    // 分页处理
    const { page = 1, pageSize = 10 } = params
    const total = filteredRoles.length
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const data = filteredRoles.slice(startIndex, endIndex)

    return {
      data,
      total,
      page,
      pageSize
    }
  }

  /**
   * 获取所有角色（不分页）
   */
  static async getAllRoles(): Promise<Role[]> {
    await delay()
    return [...roleListMock]
  }

  /**
   * 根据ID获取角色详情
   */
  static async getRoleById(id: number): Promise<Role | null> {
    await delay()
    return roleListMock.find(role => role.id === id) || null
  }

  /**
   * 创建新角色
   */
  static async createRole(roleData: CreateRoleRequest): Promise<Role> {
    await delay()

    // 检查角色编码是否已存在
    const existingRole = roleListMock.find(role => role.code === roleData.code)
    if (existingRole) {
      throw new Error('角色编码已存在')
    }

    // 检查角色名称是否已存在
    const existingName = roleListMock.find(role => role.name === roleData.name)
    if (existingName) {
      throw new Error('角色名称已存在')
    }

    // 根据权限ID获取权限详情
    const permissions = permissionListMock.filter(p => roleData.permissionIds.includes(p.id))

    // 根据菜单ID获取菜单详情
    const menus = menuListMock.filter(m => roleData.menuIds.includes(m.id))

    const newRole: Role = {
      id: generateId(),
      name: roleData.name,
      code: roleData.code,
      description: roleData.description,
      level: roleData.level,
      permissions,
      menus,
      dataScope: roleData.dataScope,
      status: RoleStatus.ENABLE,
      createTime: new Date(),
      updateTime: new Date()
    }

    roleListMock.push(newRole)
    return newRole
  }

  /**
   * 更新角色信息
   */
  static async updateRole(id: number, roleData: UpdateRoleRequest): Promise<Role> {
    await delay()

    const roleIndex = roleListMock.findIndex(role => role.id === id)
    if (roleIndex === -1) {
      throw new Error('角色不存在')
    }

    // 如果更新角色名称，检查是否与其他角色冲突
    if (roleData.name) {
      const existingRole = roleListMock.find(role => role.id !== id && role.name === roleData.name)
      if (existingRole) {
        throw new Error('角色名称已被其他角色使用')
      }
    }

    // 更新权限和菜单
    let permissions = roleListMock[roleIndex].permissions
    let menus = roleListMock[roleIndex].menus

    if (roleData.permissionIds) {
      permissions = permissionListMock.filter(p => roleData.permissionIds!.includes(p.id))
    }

    if (roleData.menuIds) {
      menus = menuListMock.filter(m => roleData.menuIds!.includes(m.id))
    }

    const updatedRole = {
      ...roleListMock[roleIndex],
      ...roleData,
      permissions,
      menus,
      updateTime: new Date()
    }

    roleListMock[roleIndex] = updatedRole
    return updatedRole
  }

  /**
   * 删除角色
   */
  static async deleteRole(id: number): Promise<void> {
    await delay()

    const roleIndex = roleListMock.findIndex(role => role.id === id)
    if (roleIndex === -1) {
      throw new Error('角色不存在')
    }

    // 不允许删除超级管理员角色
    if (roleListMock[roleIndex].code === 'SUPER_ADMIN') {
      throw new Error('不能删除超级管理员角色')
    }

    // 检查是否有用户正在使用此角色
    // 实际项目中需要查询用户表
    console.log(`删除角色: ${roleListMock[roleIndex].name}`)

    roleListMock.splice(roleIndex, 1)
  }

  /**
   * 批量删除角色
   */
  static async batchDeleteRoles(ids: number[]): Promise<void> {
    await delay()

    // 检查是否包含超级管理员角色
    const superAdminRole = roleListMock.find(role => role.code === 'SUPER_ADMIN')
    if (superAdminRole && ids.includes(superAdminRole.id)) {
      throw new Error('不能删除超级管理员角色')
    }

    // 过滤掉不存在的角色ID
    const validIds = ids.filter(id => roleListMock.some(role => role.id === id))

    // 删除角色
    validIds.forEach(id => {
      const index = roleListMock.findIndex(role => role.id === id)
      if (index !== -1) {
        roleListMock.splice(index, 1)
      }
    })
  }

  /**
   * 复制角色
   */
  static async copyRole(id: number, newName: string, newCode: string): Promise<Role> {
    await delay()

    const sourceRole = roleListMock.find(role => role.id === id)
    if (!sourceRole) {
      throw new Error('源角色不存在')
    }

    // 检查新角色编码是否已存在
    const existingCode = roleListMock.find(role => role.code === newCode)
    if (existingCode) {
      throw new Error('角色编码已存在')
    }

    // 检查新角色名称是否已存在
    const existingName = roleListMock.find(role => role.name === newName)
    if (existingName) {
      throw new Error('角色名称已存在')
    }

    const newRole: Role = {
      ...sourceRole,
      id: generateId(),
      name: newName,
      code: newCode,
      createTime: new Date(),
      updateTime: new Date()
    }

    roleListMock.push(newRole)
    return newRole
  }

  /**
   * 更新角色状态
   */
  static async updateRoleStatus(id: number, status: RoleStatus): Promise<void> {
    await delay()

    const roleIndex = roleListMock.findIndex(role => role.id === id)
    if (roleIndex === -1) {
      throw new Error('角色不存在')
    }

    // 不允许禁用超级管理员角色
    if (roleListMock[roleIndex].code === 'SUPER_ADMIN' && status === RoleStatus.DISABLE) {
      throw new Error('不能禁用超级管理员角色')
    }

    roleListMock[roleIndex].status = status
    roleListMock[roleIndex].updateTime = new Date()
  }

  /**
   * 获取角色的权限列表
   */
  static async getRolePermissions(id: number): Promise<Permission[]> {
    await delay()

    const role = roleListMock.find(role => role.id === id)
    if (!role) {
      throw new Error('角色不存在')
    }

    return role.permissions
  }

  /**
   * 为角色分配权限
   */
  static async assignPermissions(roleId: number, permissionIds: number[]): Promise<void> {
    await delay()

    const roleIndex = roleListMock.findIndex(role => role.id === roleId)
    if (roleIndex === -1) {
      throw new Error('角色不存在')
    }

    // 根据权限ID获取权限详情
    const permissions = permissionListMock.filter(p => permissionIds.includes(p.id))

    roleListMock[roleIndex].permissions = permissions
    roleListMock[roleIndex].updateTime = new Date()
  }

  /**
   * 获取角色的菜单列表
   */
  static async getRoleMenus(id: number): Promise<Menu[]> {
    await delay()

    const role = roleListMock.find(role => role.id === id)
    if (!role) {
      throw new Error('角色不存在')
    }

    return role.menus
  }

  /**
   * 为角色分配菜单
   */
  static async assignMenus(roleId: number, menuIds: number[]): Promise<void> {
    await delay()

    const roleIndex = roleListMock.findIndex(role => role.id === roleId)
    if (roleIndex === -1) {
      throw new Error('角色不存在')
    }

    // 根据菜单ID获取菜单详情
    const menus = menuListMock.filter(m => menuIds.includes(m.id))

    roleListMock[roleIndex].menus = menus
    roleListMock[roleIndex].updateTime = new Date()
  }

  /**
   * 搜索角色
   */
  static async searchRoles(keyword: string): Promise<Role[]> {
    await delay()
    return searchRoles(keyword)
  }

  /**
   * 根据级别获取角色
   */
  static async getRolesByLevel(level: number): Promise<Role[]> {
    await delay()
    return getRolesByLevel(level)
  }
}

/**
 * 权限管理 API 接口
 */
export class PermissionAPI {
  /**
   * 获取所有权限（树形结构）
   */
  static async getAllPermissions(): Promise<Permission[]> {
    await delay()
    return [...permissionListMock]
  }

  /**
   * 获取权限树形结构
   */
  static async getPermissionTree(): Promise<Permission[]> {
    await delay()

    // 构建权限树
    const buildTree = (permissions: Permission[], parentId?: number): Permission[] => {
      return permissions
        .filter(p => p.parentId === parentId)
        .map(p => ({
          ...p,
          children: buildTree(permissions, p.id)
        }))
    }

    return buildTree(permissionListMock)
  }
}

/**
 * 菜单管理 API 接口
 */
export class MenuAPI {
  /**
   * 获取所有菜单
   */
  static async getAllMenus(): Promise<Menu[]> {
    await delay()
    return [...menuListMock]
  }

  /**
   * 获取菜单树形结构
   */
  static async getMenuTree(): Promise<Menu[]> {
    await delay()
    return menuListMock.filter(m => !m.parentId)
  }
}

// 默认导出
export default RoleAPI

const defaultParams = {
  dateUnit: 1,
  startDate: '2023-08-01',
  endDate: '2023-08-30'
}

/**
 * @description: 分页查询角色信息
 * @param {any} data
 * @return {*}
 */
export const getRoleListApi = (data?: NewRoleQueryParams): Promise<BaseResponse<any>> => {
  return http({
    url: '/role/list',
    method: 'post',
    data: {
      ...defaultParams,
      ...data
    }
  })
}

/**
 * @description: 新增角色信息
 * @param {any} data
 * @return {*}
 */
export const saveOrUpdateRoleApi = (data?: SaveOrUpdateRoleRequest): Promise<BaseResponse<any>> => {
  return http({
    url: '/role/saveOrUpdate',
    method: 'post',
    data: {
      ...defaultParams,
      ...data
    }
  })
}

/**
 * @description: 根据Id查询单条信息
 * @param {any} data
 * @return {*}
 */
export const queryUserPermissionApi = (
  data?: QueryUserPermissionRequest
): Promise<BaseResponse<any>> => {
  return http({
    url: '/role/queryUserPermission',
    method: 'post',
    data: {
      ...defaultParams,
      ...data
    }
  })
}

/**
 * @description: 获取权限菜单下拉
 * @return {*}
 */
export const queryMenuPermissionListApi = (
  data?: NewRoleQueryParams
): Promise<BaseResponse<any>> => {
  return http({
    url: '/role/queryMenuPermissionList',
    method: 'post',
    data: {
      ...defaultParams,
      ...data
    }
  })
}

/**
 * @description: 根据 RoleId 获取编辑回显的数据
 * @param {NewRoleQueryParams} data
 * @return {*}
 */
export const getListByRoleIdApi = (data?: NewRoleQueryParams): Promise<BaseResponse<any>> => {
  return http({
    url: '/role/getListByRoleId',
    method: 'post',
    data: {
      ...defaultParams,
      ...data
    }
  })
}
