import type {
  Role,
  Permission,
  Menu,
  RoleQueryParams,
  CreateRoleRequest,
  UpdateRoleRequest,
  PageResult,
  RoleStatus
} from '@/types/system'

/**
 * 角色管理 API 类型定义
 */
export interface IRoleAPI {
  /**
   * 获取角色列表（分页）
   */
  getRoleList(params: RoleQueryParams): Promise<PageResult<Role>>

  /**
   * 获取所有角色（不分页）
   */
  getAllRoles(): Promise<Role[]>

  /**
   * 根据ID获取角色详情
   */
  getRoleById(id: number): Promise<Role | null>

  /**
   * 创建新角色
   */
  createRole(roleData: CreateRoleRequest): Promise<Role>

  /**
   * 更新角色信息
   */
  updateRole(id: number, roleData: UpdateRoleRequest): Promise<Role>

  /**
   * 删除角色
   */
  deleteRole(id: number): Promise<void>

  /**
   * 批量删除角色
   */
  batchDeleteRoles(ids: number[]): Promise<void>

  /**
   * 复制角色
   */
  copyRole(id: number, newName: string, newCode: string): Promise<Role>

  /**
   * 更新角色状态
   */
  updateRoleStatus(id: number, status: RoleStatus): Promise<void>

  /**
   * 获取角色的权限列表
   */
  getRolePermissions(id: number): Promise<Permission[]>

  /**
   * 为角色分配权限
   */
  assignPermissions(roleId: number, permissionIds: number[]): Promise<void>

  /**
   * 获取角色的菜单列表
   */
  getRoleMenus(id: number): Promise<Menu[]>

  /**
   * 为角色分配菜单
   */
  assignMenus(roleId: number, menuIds: number[]): Promise<void>

  /**
   * 搜索角色
   */
  searchRoles(keyword: string): Promise<Role[]>

  /**
   * 根据级别获取角色
   */
  getRolesByLevel(level: number): Promise<Role[]>
}

/**
 * 权限管理 API 类型定义
 */
export interface IPermissionAPI {
  /**
   * 获取所有权限（树形结构）
   */
  getAllPermissions(): Promise<Permission[]>

  /**
   * 获取权限树形结构
   */
  getPermissionTree(): Promise<Permission[]>
}

/**
 * 菜单管理 API 类型定义
 */
export interface IMenuAPI {
  /**
   * 获取所有菜单
   */
  getAllMenus(): Promise<Menu[]>

  /**
   * 获取菜单树形结构
   */
  getMenuTree(): Promise<Menu[]>
}

// 导出类型
export type {
  Role,
  Permission,
  Menu,
  RoleQueryParams,
  CreateRoleRequest,
  UpdateRoleRequest,
  PageResult,
  RoleStatus
} from '@/types/system'

export interface NewRoleQueryParams {
  /** 分页大小 */
  pageSize?: number

  /** 页码 */
  pageNum?: number

  /** 排序 */
  order?: string

  /** 角色状态 */
  enabled?: string

  /** 角色名称 */
  roleName?: string

  /** 客户ID，默认0 */
  clientId?: string

  /** 角色ID */
  roleId?: string

  /** 搜索关键字 */
  searchKeyword?: string

  /** 品牌编码 */
  brandCode?: string

  /** 品牌名称 */
  brandName?: string

  /** 是否校验管理员，默认false */
  checkAdmin?: boolean

  /** 是否全选，默认false */
  selectAll?: boolean

  /** 权限ID列表 */
  permissionIdList?: string[]

  /** 标签类型 */
  tagLibType?: string
}

export interface SaveOrUpdateRoleRequest {
  /** 角色Id编辑时必传 */
  id?: string

  roleId?: string

  /** 客户ID不能为空 */
  clientId: string

  /** 角色名称 */
  roleName: string

  /** 菜单IdList */
  permissionIdList: string[]

  /** 关联车系，传code以后英文逗号(,)分隔 */
  seriesIds: string[]

  /** 关联渠道ID，以后英文逗号(,)分隔 */
  channelIds: string[]

  /** 关联业务标签，传code以后英文逗号(,)分隔 */
  businessTagIds?: string[]

  /** 关联业务标签，传code以后英文逗号(,)分隔 */
  serviceTagIds?: string[]

  /** 关联质量标签，传code以后英文逗号(,)分隔 */
  qualityTagIds?: string[]

  /** 关联业务标签，传code以后英文逗号(,)分隔 */
  areaIds: string[]

  /** 功能权限：是否可以导出 true为是 */
  isExport?: boolean

  /** 功能权限：是否可以下载 true为是 */
  isDownload?: boolean

  /** 是否拥有所有权限 true:是 */
  allPermission?: boolean

  /** 品牌code */
  brandCode: string

  /** 角色状态 */
  enabled?: number

  remark?: string
}

export interface QueryUserPermissionRequest {
  /** 用户ID */
  userId: string

  /** 客户ID */
  clientId: string

  /** 是否树形结构，默认false */
  tree?: boolean

  /** 是否管理员 */
  admin?: boolean
}
