import { describe, it, expect, beforeEach, vi } from 'vitest'
import { RoleAPI } from './index'
import type { RoleQueryParams, CreateRoleRequest, UpdateRoleRequest } from './index.d'
import { RoleStatus, DataScope } from '@/types/system'
import { roleListMock } from '@/mock/system/role'

// Mock数据重置
const mockRoleListOriginal = [
  {
    id: 1,
    name: '超级管理员',
    code: 'SUPER_ADMIN',
    description: '系统超级管理员',
    level: 1,
    permissions: [],
    menus: [],
    dataScope: DataScope.ALL,
    status: RoleStatus.ENABLE,
    createTime: new Date('2024-01-01'),
    updateTime: new Date('2024-01-01')
  },
  {
    id: 2,
    name: '系统管理员',
    code: 'SYS_ADMIN',
    description: '系统管理员',
    level: 2,
    permissions: [],
    menus: [],
    dataScope: DataScope.DEPT,
    status: RoleStatus.ENABLE,
    createTime: new Date('2024-01-02'),
    updateTime: new Date('2024-01-02')
  },
  {
    id: 3,
    name: '普通用户',
    code: 'USER',
    description: '普通用户角色',
    level: 3,
    permissions: [],
    menus: [],
    dataScope: DataScope.SELF,
    status: RoleStatus.DISABLE,
    createTime: new Date('2024-01-03'),
    updateTime: new Date('2024-01-03')
  }
]

// 重置Mock数据
const resetMockData = () => {
  // 直接重置roleListMock数组内容
  roleListMock.length = 0
  roleListMock.push(...mockRoleListOriginal)
}

describe('RoleAPI', () => {
  beforeEach(() => {
    resetMockData()
  })

  describe('getRoleList', () => {
    it('应该返回所有角色的分页列表', async () => {
      const params: RoleQueryParams = { page: 1, pageSize: 10 }
      const result = await RoleAPI.getRoleList(params)

      expect(result.data).toHaveLength(3)
      expect(result.total).toBe(3)
      expect(result.page).toBe(1)
      expect(result.pageSize).toBe(10)
      expect(result.data[0].name).toBe('超级管理员')
    })

    it('应该支持按角色名称搜索', async () => {
      const params: RoleQueryParams = {
        page: 1,
        pageSize: 10,
        name: '管理员'
      }
      const result = await RoleAPI.getRoleList(params)

      expect(result.data).toHaveLength(2)
      expect(result.data.every(role => role.name.includes('管理员'))).toBe(true)
    })

    it('应该支持按角色编码搜索', async () => {
      const params: RoleQueryParams = {
        page: 1,
        pageSize: 10,
        code: 'ADMIN'
      }
      const result = await RoleAPI.getRoleList(params)

      expect(result.data).toHaveLength(2)
      expect(result.data.every(role => role.code.includes('ADMIN'))).toBe(true)
    })

    it('应该支持按状态过滤', async () => {
      const params: RoleQueryParams = {
        page: 1,
        pageSize: 10,
        status: RoleStatus.ENABLE
      }
      const result = await RoleAPI.getRoleList(params)

      expect(result.data).toHaveLength(2)
      expect(result.data.every(role => role.status === RoleStatus.ENABLE)).toBe(true)
    })

    it('应该支持按级别过滤', async () => {
      const params: RoleQueryParams = {
        page: 1,
        pageSize: 10,
        level: 1
      }
      const result = await RoleAPI.getRoleList(params)

      expect(result.data).toHaveLength(1)
      expect(result.data[0].level).toBe(1)
    })

    it('应该支持分页', async () => {
      const params: RoleQueryParams = { page: 2, pageSize: 2 }
      const result = await RoleAPI.getRoleList(params)

      expect(result.data).toHaveLength(1)
      expect(result.total).toBe(3)
      expect(result.page).toBe(2)
      expect(result.pageSize).toBe(2)
    })
  })

  describe('getAllRoles', () => {
    it('应该返回所有角色（不分页）', async () => {
      const roles = await RoleAPI.getAllRoles()

      expect(roles).toHaveLength(3)
      expect(roles[0].name).toBe('超级管理员')
      expect(roles[1].name).toBe('系统管理员')
      expect(roles[2].name).toBe('普通用户')
    })
  })

  describe('getRoleById', () => {
    it('应该根据ID获取角色详情', async () => {
      const role = await RoleAPI.getRoleById(1)

      expect(role).not.toBeNull()
      expect(role!.id).toBe(1)
      expect(role!.name).toBe('超级管理员')
      expect(role!.code).toBe('SUPER_ADMIN')
    })

    it('应该在角色不存在时返回null', async () => {
      const role = await RoleAPI.getRoleById(999)

      expect(role).toBeNull()
    })
  })

  describe('createRole', () => {
    it('应该成功创建新角色', async () => {
      const roleData: CreateRoleRequest = {
        name: '新创建角色',
        code: 'NEW_TEST_ROLE',
        description: '测试角色描述',
        level: 4,
        permissionIds: [1, 2],
        menuIds: [1, 2],
        dataScope: DataScope.DEPT
      }

      const newRole = await RoleAPI.createRole(roleData)

      expect(newRole.id).toBeGreaterThan(3)
      expect(newRole.name).toBe('新创建角色')
      expect(newRole.code).toBe('NEW_TEST_ROLE')
      expect(newRole.status).toBe(RoleStatus.ENABLE)
      expect(newRole.permissions).toHaveLength(2)
      expect(newRole.menus).toHaveLength(2)
    })

    it('应该在角色编码已存在时抛出错误', async () => {
      const roleData: CreateRoleRequest = {
        name: '新角色',
        code: 'SUPER_ADMIN', // 已存在的编码
        description: '新角色描述',
        level: 4,
        permissionIds: [],
        menuIds: [],
        dataScope: DataScope.SELF
      }

      await expect(RoleAPI.createRole(roleData)).rejects.toThrow('角色编码已存在')
    })

    it('应该在角色名称已存在时抛出错误', async () => {
      const roleData: CreateRoleRequest = {
        name: '超级管理员', // 已存在的名称
        code: 'NEW_ROLE',
        description: '新角色描述',
        level: 4,
        permissionIds: [],
        menuIds: [],
        dataScope: DataScope.SELF
      }

      await expect(RoleAPI.createRole(roleData)).rejects.toThrow('角色名称已存在')
    })
  })

  describe('updateRole', () => {
    it('应该成功更新角色信息', async () => {
      const updateData: UpdateRoleRequest = {
        name: '更新的角色',
        description: '更新的描述',
        level: 5,
        permissionIds: [1],
        menuIds: [1]
      }

      const updatedRole = await RoleAPI.updateRole(2, updateData)

      expect(updatedRole.id).toBe(2)
      expect(updatedRole.name).toBe('更新的角色')
      expect(updatedRole.description).toBe('更新的描述')
      expect(updatedRole.level).toBe(5)
      expect(updatedRole.permissions).toHaveLength(1)
      expect(updatedRole.menus).toHaveLength(1)
    })

    it('应该在角色不存在时抛出错误', async () => {
      const updateData: UpdateRoleRequest = {
        name: '更新的角色'
      }

      await expect(RoleAPI.updateRole(999, updateData)).rejects.toThrow('角色不存在')
    })

    it('应该在角色名称冲突时抛出错误', async () => {
      const updateData: UpdateRoleRequest = {
        name: '超级管理员' // 与其他角色冲突
      }

      await expect(RoleAPI.updateRole(2, updateData)).rejects.toThrow('角色名称已被其他角色使用')
    })
  })

  describe('deleteRole', () => {
    it('应该成功删除角色', async () => {
      await expect(RoleAPI.deleteRole(3)).resolves.toBeUndefined()
    })

    it('应该在角色不存在时抛出错误', async () => {
      await expect(RoleAPI.deleteRole(999)).rejects.toThrow('角色不存在')
    })

    it('应该在尝试删除超级管理员角色时抛出错误', async () => {
      await expect(RoleAPI.deleteRole(1)).rejects.toThrow('不能删除超级管理员角色')
    })
  })

  describe('batchDeleteRoles', () => {
    it('应该成功批量删除角色', async () => {
      await expect(RoleAPI.batchDeleteRoles([2, 3])).resolves.toBeUndefined()
    })

    it('应该在包含超级管理员角色时抛出错误', async () => {
      await expect(RoleAPI.batchDeleteRoles([1, 2, 3])).rejects.toThrow('不能删除超级管理员角色')
    })

    it('应该处理不存在的角色ID', async () => {
      await expect(RoleAPI.batchDeleteRoles([999, 1000])).resolves.toBeUndefined()
    })
  })

  describe('copyRole', () => {
    it('应该成功复制角色', async () => {
      const newRole = await RoleAPI.copyRole(2, '复制的系统管理员', 'COPY_SYS_ADMIN')

      expect(newRole.id).toBeGreaterThan(3)
      expect(newRole.name).toBe('复制的系统管理员')
      expect(newRole.code).toBe('COPY_SYS_ADMIN')
      expect(newRole.description).toBe('系统管理员') // 继承原角色描述
      expect(newRole.level).toBe(2) // 继承原角色级别
    })

    it('应该在源角色不存在时抛出错误', async () => {
      await expect(RoleAPI.copyRole(999, '新角色', 'NEW_ROLE')).rejects.toThrow('源角色不存在')
    })

    it('应该在角色编码已存在时抛出错误', async () => {
      await expect(RoleAPI.copyRole(2, '新角色', 'SUPER_ADMIN')).rejects.toThrow('角色编码已存在')
    })

    it('应该在角色名称已存在时抛出错误', async () => {
      await expect(RoleAPI.copyRole(2, '超级管理员', 'NEW_CODE')).rejects.toThrow('角色名称已存在')
    })
  })

  describe('updateRoleStatus', () => {
    it('应该成功更新角色状态', async () => {
      await expect(RoleAPI.updateRoleStatus(3, RoleStatus.ENABLE)).resolves.toBeUndefined()
    })

    it('应该在角色不存在时抛出错误', async () => {
      await expect(RoleAPI.updateRoleStatus(999, RoleStatus.DISABLE)).rejects.toThrow('角色不存在')
    })

    it('应该在尝试禁用超级管理员角色时抛出错误', async () => {
      await expect(RoleAPI.updateRoleStatus(1, RoleStatus.DISABLE)).rejects.toThrow(
        '不能禁用超级管理员角色'
      )
    })

    it('应该允许启用超级管理员角色', async () => {
      await expect(RoleAPI.updateRoleStatus(1, RoleStatus.ENABLE)).resolves.toBeUndefined()
    })
  })

  describe('getRolePermissions', () => {
    it('应该获取角色的权限列表', async () => {
      const permissions = await RoleAPI.getRolePermissions(1)

      expect(Array.isArray(permissions)).toBe(true)
    })

    it('应该在角色不存在时抛出错误', async () => {
      await expect(RoleAPI.getRolePermissions(999)).rejects.toThrow('角色不存在')
    })
  })

  describe('assignPermissions', () => {
    it('应该成功为角色分配权限', async () => {
      await expect(RoleAPI.assignPermissions(2, [1, 2])).resolves.toBeUndefined()
    })

    it('应该在角色不存在时抛出错误', async () => {
      await expect(RoleAPI.assignPermissions(999, [1, 2])).rejects.toThrow('角色不存在')
    })
  })

  describe('getRoleMenus', () => {
    it('应该获取角色的菜单列表', async () => {
      const menus = await RoleAPI.getRoleMenus(1)

      expect(Array.isArray(menus)).toBe(true)
    })

    it('应该在角色不存在时抛出错误', async () => {
      await expect(RoleAPI.getRoleMenus(999)).rejects.toThrow('角色不存在')
    })
  })

  describe('assignMenus', () => {
    it('应该成功为角色分配菜单', async () => {
      await expect(RoleAPI.assignMenus(2, [1, 2])).resolves.toBeUndefined()
    })

    it('应该在角色不存在时抛出错误', async () => {
      await expect(RoleAPI.assignMenus(999, [1, 2])).rejects.toThrow('角色不存在')
    })
  })

  describe('searchRoles', () => {
    it('应该成功搜索角色', async () => {
      const roles = await RoleAPI.searchRoles('管理员')

      expect(Array.isArray(roles)).toBe(true)
    })

    it('应该处理空关键词', async () => {
      const roles = await RoleAPI.searchRoles('')

      expect(Array.isArray(roles)).toBe(true)
    })
  })
})
