export interface VocDataItem {
  /** 日期 */
  date?: string
  /** 总提及量 */
  totalMentions?: number
  /** 正面提及量 */
  positiveMentions?: number
  /** 中性提及量 */
  neutralMentions?: number
  /** 负面提及量 */
  negativeMentions?: number
  /** 体验值 */
  experienceValue?: number
  /** 体验值环比 */
  experienceValueMoM?: number
}

/**
 * 客户标签体验值VO
 */
export interface CustomerTagExperienceVo {
  /** 全旅程客户标签名称 */
  customerTagName?: string

  /** 全旅程客户标签体验值 */
  experienceValue?: number

  /** 正面提及量 */
  positiveMentions?: number

  /** 中性提及量 */
  neutralMentions?: number

  /** 负面提及量 */
  negativeMentions?: number

  /** 总提及量 */
  totalMentions?: number
}

/**
 * 客户标签体验数据响应接口
 */
export interface CustomerTagExperienceResponse {
  /** 客户标签体验值VO数组 */
  customerTagExperienceList?: CustomerTagExperienceVo[]

  /** 体验值(所有标签的) */
  totalExperienceValue?: number

  /** 体验值环比(所有标签的) */
  totalExperienceValueMoM?: number

  /** 提及量(所有标签的) */
  totalMentions?: number

  /** 提及量环比(所有标签的) */
  totalMentionsMoM?: number
}

/**
 * 关键词数据接口
 */
export interface KeywordData {
  /** 关键词 */
  keyword?: string

  /** 情感 */
  sentiment?: string

  /** 提及量 */
  mentions?: number

  /** 提及量变化 */
  mentionsChange?: number

  /** 提及量环比 */
  mentionsMoM?: number

  /** 提及率 */
  mentionRate?: number
}

/**
 * 年龄分布VO
 */
export interface AgeDistributionVo {
  /** 年龄段 */
  ageRange?: string

  /** 占比 */
  proportion?: number
}

/**
 * 车龄分布VO
 */
export interface CarAgeDistributionVo {
  /** 车龄 */
  carAge?: string

  /** 占比 */
  proportion?: number
}

/**
 * 客户类型分布VO
 */
export interface CustomerTypeDistributionVo {
  /** 客户类型 */
  customerType?: string

  /** 占比 */
  proportion?: number
}

/**
 * 省份分布VO
 */
export interface ProvinceDistributionVo {
  /** 省份名称 */
  provinceName?: string

  /** 占比 */
  proportion?: number
}

/**
 * 学历分布VO
 */
export interface EducationDistributionVo {
  /** 学历 */
  education?: string

  /** 占比 */
  proportion?: number
}

/**
 * 性别分布VO
 */
export interface GenderDistributionVo {
  /** 性别 */
  gender?: string

  /** 占比 */
  proportion?: number
}

/**
 * 人群特征分布数据响应接口
 */
export interface PopulationCharacteristicsResponse {
  /** 人群总人数 */
  totalUsers?: number

  /** 年龄分布VO数组 */
  ageDistributions?: AgeDistributionVo[]

  /** 车龄分布VO数组 */
  carAgeDistributions?: CarAgeDistributionVo[]

  /** 客户类型分布VO数组 */
  customerTypeDistributions?: CustomerTypeDistributionVo[]

  /** 省份分布VO数组 */
  provinceDistributions?: ProvinceDistributionVo[]

  /** 学历分布VO数组 */
  educationDistributions?: EducationDistributionVo[]

  /** 性别分布VO数组 */
  genderDistributions?: GenderDistributionVo[]
}

/**
 * 数据源分析数据项
 */
export interface DataSourceAnalysisItem {
  /** 提及量 */
  mentions?: number
  /** 数据源名称(渠道名称) */
  dataSourceName?: string
  /** 数据源编码(渠道编码) */
  dataSourceCode?: string
  /** 正面提及量 */
  positiveMentions?: number
  /** 中性提及量 */
  neutralMentions?: number
  /** 负面提及量 */
  negativeMentions?: number
}

/**
 * 提及量趋势数据项
 */
export interface MentionTrendItem {
  /** 提及量 */
  mentions?: number
  /** 体验值 */
  experienceValue?: number
  /** 日期 */
  date?: string
  /** 正面提及量 */
  positiveMentions?: number
  /** 中性提及量 */
  neutralMentions?: number
  /** 负面提及量 */
  negativeMentions?: number
}

/**
 * 词云图数据项
 */
export interface WordCloudItem {
  /** 提及量 */
  mentions?: number
  /** 热词名称 */
  hotWordName?: string
  /** 正面提及量 */
  positiveMentions?: number
  /** 中性提及量 */
  neutralMentions?: number
  /** 负面提及量 */
  negativeMentions?: number
}

/**
 * @description: 地域分析
 * @return {*}
 */
export interface ProvinceAnalysis {
  /** 省份名称 */
  provinceName?: string

  /** 提及量 */
  mentions?: number

  /** 提及量环比 */
  mentionsMoM?: number

  /** 体验值 */
  experienceValue?: number

  /** 体验值环比 */
  experienceValueMoM?: number

  /** 热词名称 */
  hotWordName?: string
}
