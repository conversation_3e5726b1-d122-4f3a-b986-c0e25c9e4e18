import http from '../http/index'
import type {
  CustomerTagExperienceResponse,
  DataSourceAnalysisItem,
  KeywordData,
  MentionTrendItem,
  PopulationCharacteristicsResponse,
  ProvinceAnalysis,
  VocDataItem,
  WordCloudItem
} from './index.d'

const defaultParams = {
  dateUnit: 1,
  startDate: '2023-08-01',
  endDate: '2023-08-30'
}

/**
 * @description: 获取VOC体验值趋势
 * @param {any} data
 * @return {*}
 */
export const getVocTrendChartData = (data?: any): Promise<BaseResponse<VocDataItem[]>> => {
  return http({
    url: '/drill-down/voc-experience-trend',
    method: 'post',
    data: {
      ...defaultParams,
      ...data
    }
  })
}

/**
 * @description: 获取全旅程客户体验值
 * @param {any} data
 * @return {*}
 */
export const getCustomerJourneyExperience = (
  data?: any
): Promise<BaseResponse<CustomerTagExperienceResponse>> => {
  return http({
    url: '/drill-down/customer-journey-experience',
    method: 'post',
    data: {
      ...defaultParams,
      ...data
    }
  })
}

/**
 * @description: 获取全旅程top问题
 * @param {any} data
 * @return {*}
 */
export const getTopIssues = (data?: any): Promise<BaseResponse<KeywordData[]>> => {
  return http({
    url: '/drill-down/top-issues',
    method: 'post',
    data: {
      ...defaultParams,
      ...data
    }
  })
}

/**
 * @description: 获取人群特征
 * @param {any} data
 * @return {*}
 */
export const getDemographics = (
  data?: any
): Promise<BaseResponse<PopulationCharacteristicsResponse>> => {
  return http({
    url: '/drill-down/demographics',
    method: 'post',
    data: {
      ...defaultParams,
      ...data
    }
  })
}

/**
 * @description: 获取数据来源
 * @param {any} data
 * @return {*}
 */
export const getDataSourceAnalysis = (
  data?: any
): Promise<BaseResponse<DataSourceAnalysisItem[]>> => {
  return http({
    url: '/drill-down/data-sources',
    method: 'post',
    data: {
      ...defaultParams,
      ...data
    }
  })
}

/**
 * @description: 获取提及量趋势
 * @param {any} data
 * @return {*}
 */
export const getMentionTrend = (data?: any): Promise<BaseResponse<MentionTrendItem[]>> => {
  return http({
    url: '/drill-down/mention-trend',
    method: 'post',
    data: {
      ...defaultParams,
      ...data
    }
  })
}

/**
 * @description: 获取词云图
 * @param {any} data
 * @return {*}
 */
export const getWordCloud = (data?: any): Promise<BaseResponse<WordCloudItem[]>> => {
  return http({
    url: '/drill-down/word-cloud',
    method: 'post',
    data: {
      ...defaultParams,
      ...data
    }
  })
}
/**
 * @description: 获取地域分析
 * @param {any} data
 * @return {*}
 */
export const getRegionalAnalysis = (data?: any): Promise<BaseResponse<ProvinceAnalysis[]>> => {
  return http({
    url: '/drill-down/regional-analysis',
    method: 'post',
    data: {
      ...defaultParams,
      ...data
    }
  })
}
