import axios from 'axios'
import type { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
})

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 在这里可以添加token等认证信息
    // config.headers.Authorization = `Bearer ${localStorage.getItem('token')}`
    config.headers.Authorization = `Bearer 1234567890`
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data } = response
    console.log(data, 'data')
    // 根据后端接口返回的约定结构处理
    if (data.code === '200') {
      // 正常返回数据
      return data
    } else {
      // 业务处理错误
      ElMessage.error(data.message || '请求失败')
      return Promise.reject(new Error(data.message || '未知错误'))
    }
  },
  error => {
    // 处理http错误状态码
    let message = '请求失败'
    if (error.response) {
      const { status } = error.response
      switch (status) {
        case 401:
          message = '未授权，请重新登录'
          // 清除用户信息并跳转登录页
          localStorage.removeItem('token')
          window.location.href = '/#/login'
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求地址出错'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = `未知错误(${status})`
      }
    } else if (error.message.includes('timeout')) {
      message = '请求超时，请稍后再试'
    }

    ElMessage.error(message)
    return Promise.reject(error)
  }
)

export default service
